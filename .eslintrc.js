module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // Security rules to prevent XSS and other vulnerabilities
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    
    // Prevent unsafe innerHTML usage
    'no-unsanitized/method': 'error',
    'no-unsanitized/property': 'error',
    
    // Prevent console usage in production (except for SnapLogger)
    'no-console': ['warn', {
      allow: ['warn', 'error'] // Allow console.warn and console.error as fallbacks
    }],
    
    // Require input validation
    'security/detect-object-injection': 'warn',
    'security/detect-non-literal-regexp': 'warn',
    'security/detect-unsafe-regex': 'error',
    'security/detect-buffer-noassert': 'error',
    'security/detect-child-process': 'error',
    'security/detect-disable-mustache-escape': 'error',
    'security/detect-eval-with-expression': 'error',
    'security/detect-no-csrf-before-method-override': 'error',
    'security/detect-non-literal-fs-filename': 'warn',
    'security/detect-non-literal-require': 'warn',
    'security/detect-possible-timing-attacks': 'warn',
    'security/detect-pseudoRandomBytes': 'error',
    
    // General code quality rules
    'prefer-const': 'error',
    'no-var': 'error',
    'no-unused-vars': ['warn', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    'no-undef': 'error',
    'eqeqeq': 'error',
    'curly': 'error',
    'no-throw-literal': 'error',
    'no-return-assign': 'error',
    'no-self-compare': 'error',
    'no-sequences': 'error',
    'no-unmodified-loop-condition': 'error',
    'no-unused-expressions': 'error',
    'no-useless-call': 'error',
    'no-useless-concat': 'error',
    'no-useless-return': 'error',
    'prefer-promise-reject-errors': 'error',
    'require-await': 'error',
    'yoda': 'error'
  },
  plugins: [
    'security',
    'no-unsanitized'
  ],
  globals: {
    // Global variables specific to the Snap Dashboard application
    'window': 'readonly',
    'document': 'readonly',
    'SnapLogger': 'readonly',
    'SnapChart': 'readonly',
    'SnapLoader': 'readonly',
    'MockZeroData': 'readonly',
    'DashboardMockData': 'readonly',
    'EventCleanupManager': 'readonly',
    'GlobalDropdownManager': 'readonly',
    'UnifiedTimerManager': 'readonly',
    'DateChangeManager': 'readonly',
    'DOMOptimizer': 'readonly',
    'MemoryMonitor': 'readonly',
    'IndexedDBManager': 'readonly',
    'DailySalesManager': 'readonly',
    'ProductsPageManager': 'readonly',
    'ViewportLazyLoader': 'readonly',
    'ContentClassManager': 'readonly',
    'Timezone': 'readonly',
    'ProductionDateCallbacks': 'readonly'
  },
  overrides: [
    {
      // Specific rules for utility files
      files: ['utils/**/*.js'],
      rules: {
        'no-console': 'off' // Allow console in utility files for fallback logging
      }
    },
    {
      // Specific rules for test files
      files: ['**/*.test.js', '**/*.spec.js'],
      env: {
        jest: true
      },
      rules: {
        'no-console': 'off' // Allow console in test files
      }
    },
    {
      // Specific rules for configuration files
      files: ['.eslintrc.js', 'webpack.config.js', '*.config.js'],
      env: {
        node: true
      },
      rules: {
        'no-console': 'off' // Allow console in config files
      }
    }
  ],
  settings: {
    // Custom settings for security plugins
    'no-unsanitized/method': {
      // Allow specific methods that are known to be safe
      escape: {
        methods: ['safeSetHTML', 'sanitizeHTML', 'sanitizeUserInput']
      }
    }
  }
};
