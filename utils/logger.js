/**
 * Centralized Logger for Snap Dashboard
 * Controls console output verbosity to prevent console spam
 */

class Logger {
  constructor() {
    // Log levels: 0=silent, 1=error, 2=warn, 3=info, 4=debug, 5=verbose
    this.logLevel = this.getLogLevel();
    this.prefix = '🔧 Snap';
    
    // Performance tracking
    this.startTime = Date.now();
    this.logCount = 0;
    this.maxLogs = 1000; // Prevent infinite logging
  }

  /**
   * Get log level from localStorage or default to warn level
   */
  getLogLevel() {
    const saved = localStorage.getItem('snapLogLevel');
    if (saved !== null) {
      return parseInt(saved, 10);
    }
    
    // Default to warn level (only errors and warnings)
    return 2;
  }

  /**
   * Set log level and save to localStorage
   */
  setLogLevel(level) {
    this.logLevel = Math.max(0, Math.min(5, level));
    localStorage.setItem('snapLogLevel', this.logLevel.toString());
    this.info(`Log level set to: ${this.getLevelName(this.logLevel)}`);
  }

  /**
   * Get human-readable level name
   */
  getLevelName(level) {
    const names = ['Silent', 'Error', 'Warn', 'Info', 'Debug', 'Verbose'];
    return names[level] || 'Unknown';
  }

  /**
   * Check if we should log at this level
   */
  shouldLog(level) {
    if (this.logCount >= this.maxLogs) return false;
    return this.logLevel >= level;
  }

  /**
   * Format log message with timestamp and prefix
   */
  formatMessage(level, message, ...args) {
    this.logCount++;
    const timestamp = new Date().toLocaleTimeString();
    const levelName = this.getLevelName(level).toUpperCase();
    return [`[${timestamp}] ${this.prefix} ${levelName}:`, message, ...args];
  }

  /**
   * Error logging (level 1)
   */
  error(message, ...args) {
    if (this.shouldLog(1)) {
      console.error(...this.formatMessage(1, message, ...args));
    }
  }

  /**
   * Warning logging (level 2)
   */
  warn(message, ...args) {
    if (this.shouldLog(2)) {
      console.warn(...this.formatMessage(2, message, ...args));
    }
  }

  /**
   * Info logging (level 3)
   */
  info(message, ...args) {
    if (this.shouldLog(3)) {
      console.log(...this.formatMessage(3, message, ...args));
    }
  }

  /**
   * Debug logging (level 4)
   */
  debug(message, ...args) {
    if (this.shouldLog(4)) {
      console.log(...this.formatMessage(4, message, ...args));
    }
  }

  /**
   * Verbose logging (level 5)
   */
  verbose(message, ...args) {
    if (this.shouldLog(5)) {
      console.log(...this.formatMessage(5, message, ...args));
    }
  }

  /**
   * Performance logging
   */
  perf(operation, duration) {
    if (this.shouldLog(4)) {
      console.log(`⚡ ${operation}: ${duration}ms`);
    }
  }

  /**
   * Group logging for related operations
   */
  group(title, callback) {
    if (this.shouldLog(3)) {
      console.group(title);
      callback();
      console.groupEnd();
    } else {
      callback();
    }
  }

  /**
   * Get current stats
   */
  getStats() {
    const uptime = Date.now() - this.startTime;
    return {
      logLevel: this.logLevel,
      levelName: this.getLevelName(this.logLevel),
      logCount: this.logCount,
      uptime: `${Math.round(uptime / 1000)}s`
    };
  }

  /**
   * Reset log count
   */
  reset() {
    this.logCount = 0;
    this.startTime = Date.now();
    this.info('Logger reset');
  }
}

// Create global logger instance
window.SnapLogger = new Logger();

// Expose helper functions for easy access
window.setLogLevel = (level) => window.SnapLogger.setLogLevel(level);
window.getLogStats = () => window.SnapLogger.getStats();

// Add console commands for debugging
console.log(`
🔧 Snap Dashboard Logger Initialized
Current level: ${window.SnapLogger.getLevelName(window.SnapLogger.logLevel)}

Commands:
- setLogLevel(0-5): Set verbosity (0=silent, 1=error, 2=warn, 3=info, 4=debug, 5=verbose)
- getLogStats(): View logging statistics
- SnapLogger.reset(): Reset log counter

Current stats:`, window.SnapLogger.getStats());
