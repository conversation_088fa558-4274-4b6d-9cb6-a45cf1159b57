/**
 * Unit Tests for domHelpers.js
 * Tests all helper functions for typical use cases, edge cases, and error handling
 */

// Mock DOM environment for testing
const jsdom = require('jsdom');
const { JSDOM } = jsdom;

// Setup DOM environment
const dom = new JSDOM(`
<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body>
    <div id="test-container"></div>
    <div id="test-element" class="test-class" data-test="test-value"></div>
    <button id="test-button">Test Button</button>
</body>
</html>
`, { url: 'http://localhost' });

global.window = dom.window;
global.document = dom.window.document;
global.HTMLElement = dom.window.HTMLElement;
global.Node = dom.window.Node;

// Mock performance.now() for testing
global.performance = {
    now: jest.fn(() => Date.now())
};

// Mock sanitizeHTML module
jest.mock('./sanitizeHTML.js', () => ({
    sanitizeHTML: jest.fn((html) => html),
    createSafeElement: jest.fn((template) => {
        const fragment = document.createDocumentFragment();
        const div = document.createElement('div');
        div.innerHTML = template;
        fragment.appendChild(div);
        return fragment;
    }),
    isContentSafe: jest.fn(() => true)
}));

// Import the functions after mocking
require('./domHelpers.js');

describe('domHelpers - Performance Compatibility', () => {
    test('getCurrentTime should use performance.now() when available', () => {
        const mockPerformanceNow = jest.fn(() => 123.456);
        global.performance.now = mockPerformanceNow;
        
        // Trigger a performance monitoring operation
        window.configureDOMHelpers({ performanceMonitoring: true });
        const testElement = document.getElementById('test-element');
        
        window.safeSetHTML(testElement, '<span>test</span>');
        
        expect(mockPerformanceNow).toHaveBeenCalled();
    });

    test('getCurrentTime should fallback to Date.now() when performance.now() unavailable', () => {
        const mockDateNow = jest.fn(() => 1234567890);
        global.Date.now = mockDateNow;
        delete global.performance.now;
        
        // Trigger a performance monitoring operation
        window.configureDOMHelpers({ performanceMonitoring: true });
        const testElement = document.getElementById('test-element');
        
        window.safeSetHTML(testElement, '<span>test</span>');
        
        expect(mockDateNow).toHaveBeenCalled();
    });
});

describe('domHelpers - safeSetHTML', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-element');
        testElement.innerHTML = '';
    });

    test('should set HTML content successfully', () => {
        const result = window.safeSetHTML(testElement, '<span>test content</span>');
        expect(result).toBe(true);
        expect(testElement.innerHTML).toContain('test content');
    });

    test('should handle trusted content without sanitization', () => {
        const result = window.safeSetHTML(testElement, '<script>alert("xss")</script>', { 
            trusted: true, 
            policy: 'trusted' 
        });
        expect(result).toBe(true);
        expect(testElement.innerHTML).toContain('<script>');
    });

    test('should sanitize untrusted content', () => {
        const result = window.safeSetHTML(testElement, '<script>alert("xss")</script>');
        expect(result).toBe(true);
        // Content should be sanitized (mocked to return original)
    });

    test('should return false for invalid element', () => {
        const result = window.safeSetHTML(null, '<span>test</span>');
        expect(result).toBe(false);
    });

    test('should return false for non-string HTML', () => {
        const result = window.safeSetHTML(testElement, 123);
        expect(result).toBe(false);
    });

    test('should handle errors gracefully', () => {
        // Mock element to throw error
        const mockElement = {
            nodeType: 1,
            innerHTML: {
                set innerHTML(value) { throw new Error('Test error'); }
            }
        };
        
        const result = window.safeSetHTML(mockElement, '<span>test</span>');
        expect(result).toBe(false);
    });
});

describe('domHelpers - createElementFromTemplate', () => {
    test('should create element from template string', () => {
        const result = window.createElementFromTemplate('<div class="test">content</div>');
        expect(result).not.toBeNull();
        expect(result.nodeType).toBe(Node.DOCUMENT_FRAGMENT_NODE);
    });

    test('should return null for non-string template', () => {
        const result = window.createElementFromTemplate(123);
        expect(result).toBeNull();
    });

    test('should handle trusted templates', () => {
        const result = window.createElementFromTemplate('<div>trusted</div>', { trusted: true });
        expect(result).not.toBeNull();
    });

    test('should handle errors gracefully', () => {
        // Mock createSafeElement to throw error
        const { createSafeElement } = require('./sanitizeHTML.js');
        createSafeElement.mockImplementationOnce(() => { throw new Error('Test error'); });
        
        const result = window.createElementFromTemplate('<div>test</div>');
        expect(result).toBeNull();
    });
});

describe('domHelpers - updateTextContent', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-element');
    });

    test('should update text content successfully', () => {
        const result = window.updateTextContent(testElement, 'New text content');
        expect(result).toBe(true);
        expect(testElement.textContent).toBe('New text content');
    });

    test('should handle null text content', () => {
        const result = window.updateTextContent(testElement, null);
        expect(result).toBe(true);
        expect(testElement.textContent).toBe('');
    });

    test('should handle undefined text content', () => {
        const result = window.updateTextContent(testElement, undefined);
        expect(result).toBe(true);
        expect(testElement.textContent).toBe('');
    });

    test('should return false for invalid element', () => {
        const result = window.updateTextContent(null, 'test');
        expect(result).toBe(false);
    });

    test('should return false for non-string text', () => {
        const result = window.updateTextContent(testElement, 123);
        expect(result).toBe(false);
    });

    test('should handle errors gracefully', () => {
        // Mock element to throw error
        const mockElement = {
            nodeType: 1,
            textContent: {
                set textContent(value) { throw new Error('Test error'); }
            }
        };
        
        const result = window.updateTextContent(mockElement, 'test');
        expect(result).toBe(false);
    });
});

describe('domHelpers - updateAttributes', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-element');
        testElement.removeAttribute('data-test');
    });

    test('should update attributes successfully', () => {
        const attributes = { 'data-test': 'new-value', 'title': 'test title' };
        const result = window.updateAttributes(testElement, attributes);
        
        expect(result).toBe(true);
        expect(testElement.getAttribute('data-test')).toBe('new-value');
        expect(testElement.getAttribute('title')).toBe('test title');
    });

    test('should remove attributes when value is null', () => {
        testElement.setAttribute('data-test', 'old-value');
        const result = window.updateAttributes(testElement, { 'data-test': null });
        
        expect(result).toBe(true);
        expect(testElement.hasAttribute('data-test')).toBe(false);
    });

    test('should block dangerous attributes', () => {
        const attributes = { 'onclick': 'alert("xss")', 'onload': 'evil()' };
        const result = window.updateAttributes(testElement, attributes);
        
        expect(result).toBe(true);
        expect(testElement.hasAttribute('onclick')).toBe(false);
        expect(testElement.hasAttribute('onload')).toBe(false);
    });

    test('should block javascript: URLs', () => {
        const attributes = { 'href': 'javascript:alert("xss")' };
        const result = window.updateAttributes(testElement, attributes);
        
        expect(result).toBe(true);
        expect(testElement.hasAttribute('href')).toBe(false);
    });

    test('should allow data: URLs when explicitly permitted', () => {
        const attributes = { 'src': 'data:image/png;base64,test' };
        const result = window.updateAttributes(testElement, attributes, { allowDataUrls: true });
        
        expect(result).toBe(true);
        expect(testElement.getAttribute('src')).toBe('data:image/png;base64,test');
    });

    test('should return false for invalid element', () => {
        const result = window.updateAttributes(null, { 'class': 'test' });
        expect(result).toBe(false);
    });

    test('should return false for invalid attributes object', () => {
        const result = window.updateAttributes(testElement, 'invalid');
        expect(result).toBe(false);
    });

    test('should handle errors gracefully', () => {
        // Mock element to throw error
        const mockElement = {
            nodeType: 1,
            setAttribute: jest.fn(() => { throw new Error('Test error'); })
        };
        
        const result = window.updateAttributes(mockElement, { 'class': 'test' });
        expect(result).toBe(false);
    });
});

describe('domHelpers - appendSafeHTML', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-container');
        testElement.innerHTML = '';
    });

    test('should append HTML content successfully', () => {
        const result = window.appendSafeHTML(testElement, '<span>appended content</span>');
        expect(result).toBe(true);
        expect(testElement.innerHTML).toContain('appended content');
    });

    test('should return false for invalid element', () => {
        const result = window.appendSafeHTML(null, '<span>test</span>');
        expect(result).toBe(false);
    });

    test('should handle createElementFromTemplate failure', () => {
        // Mock createElementFromTemplate to return null
        const original = window.createElementFromTemplate;
        window.createElementFromTemplate = jest.fn(() => null);
        
        const result = window.appendSafeHTML(testElement, '<span>test</span>');
        expect(result).toBe(false);
        
        window.createElementFromTemplate = original;
    });

    test('should handle appendChild errors', () => {
        // Mock element to throw error on appendChild
        const mockElement = {
            nodeType: 1,
            appendChild: jest.fn(() => { throw new Error('Test error'); })
        };
        
        const result = window.appendSafeHTML(mockElement, '<span>test</span>');
        expect(result).toBe(false);
    });
});

describe('domHelpers - replaceSafeHTML', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-container');
        testElement.innerHTML = '<div>existing content</div>';
    });

    test('should replace HTML content successfully', () => {
        const result = window.replaceSafeHTML(testElement, '<span>new content</span>');
        expect(result).toBe(true);
        expect(testElement.innerHTML).toContain('new content');
        expect(testElement.innerHTML).not.toContain('existing content');
    });

    test('should return false for invalid element', () => {
        const result = window.replaceSafeHTML(null, '<span>test</span>');
        expect(result).toBe(false);
    });

    test('should handle appendSafeHTML failure', () => {
        // Mock appendSafeHTML to return false
        const original = window.appendSafeHTML;
        window.appendSafeHTML = jest.fn(() => false);
        
        const result = window.replaceSafeHTML(testElement, '<span>test</span>');
        expect(result).toBe(false);
        
        window.appendSafeHTML = original;
    });

    test('should handle innerHTML errors', () => {
        // Mock element to throw error on innerHTML
        const mockElement = {
            nodeType: 1,
            innerHTML: {
                set innerHTML(value) { throw new Error('Test error'); }
            }
        };
        
        const result = window.replaceSafeHTML(mockElement, '<span>test</span>');
        expect(result).toBe(false);
    });
});

describe('domHelpers - insertSafeHTML', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-container');
        testElement.innerHTML = '<div>existing</div>';
    });

    test('should insert HTML at beforebegin position', () => {
        const result = window.insertSafeHTML(testElement, 'beforebegin', '<span>before</span>');
        expect(result).toBe(true);
        expect(testElement.previousElementSibling.innerHTML).toContain('before');
    });

    test('should insert HTML at afterbegin position', () => {
        const result = window.insertSafeHTML(testElement, 'afterbegin', '<span>after begin</span>');
        expect(result).toBe(true);
        expect(testElement.innerHTML).toContain('after begin');
    });

    test('should insert HTML at beforeend position', () => {
        const result = window.insertSafeHTML(testElement, 'beforeend', '<span>before end</span>');
        expect(result).toBe(true);
        expect(testElement.innerHTML).toContain('before end');
    });

    test('should insert HTML at afterend position', () => {
        const result = window.insertSafeHTML(testElement, 'afterend', '<span>after</span>');
        expect(result).toBe(true);
        expect(testElement.nextElementSibling.innerHTML).toContain('after');
    });

    test('should return false for invalid element', () => {
        const result = window.insertSafeHTML(null, 'beforebegin', '<span>test</span>');
        expect(result).toBe(false);
    });

    test('should return false for invalid position', () => {
        const result = window.insertSafeHTML(testElement, 'invalid', '<span>test</span>');
        expect(result).toBe(false);
    });

    test('should handle createElementFromTemplate failure', () => {
        // Mock createElementFromTemplate to return null
        const original = window.createElementFromTemplate;
        window.createElementFromTemplate = jest.fn(() => null);
        
        const result = window.insertSafeHTML(testElement, 'beforebegin', '<span>test</span>');
        expect(result).toBe(false);
        
        window.createElementFromTemplate = original;
    });

    test('should handle insertAdjacentHTML errors', () => {
        // Mock element to throw error on insertAdjacentHTML
        const mockElement = {
            nodeType: 1,
            insertAdjacentHTML: jest.fn(() => { throw new Error('Test error'); })
        };
        
        const result = window.insertSafeHTML(mockElement, 'beforebegin', '<span>test</span>');
        expect(result).toBe(false);
    });
});

describe('domHelpers - createSafeEventHandler', () => {
    test('should create safe event handler wrapper', () => {
        const mockHandler = jest.fn();
        const safeHandler = window.createSafeEventHandler(mockHandler);
        
        expect(typeof safeHandler).toBe('function');
        
        const mockEvent = { preventDefault: jest.fn(), stopPropagation: jest.fn() };
        safeHandler.call({}, mockEvent);
        
        expect(mockHandler).toHaveBeenCalledWith(mockEvent);
    });

    test('should prevent default when specified', () => {
        const mockHandler = jest.fn();
        const safeHandler = window.createSafeEventHandler(mockHandler, { preventDefault: true });
        
        const mockEvent = { preventDefault: jest.fn(), stopPropagation: jest.fn() };
        safeHandler.call({}, mockEvent);
        
        expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    test('should stop propagation when specified', () => {
        const mockHandler = jest.fn();
        const safeHandler = window.createSafeEventHandler(mockHandler, { stopPropagation: true });
        
        const mockEvent = { preventDefault: jest.fn(), stopPropagation: jest.fn() };
        safeHandler.call({}, mockEvent);
        
        expect(mockEvent.stopPropagation).toHaveBeenCalled();
    });

    test('should call error handler when original handler throws', () => {
        const mockError = new Error('Test error');
        const mockHandler = jest.fn(() => { throw mockError; });
        const mockErrorHandler = jest.fn();
        
        const safeHandler = window.createSafeEventHandler(mockHandler, { onError: mockErrorHandler });
        
        const mockEvent = { preventDefault: jest.fn(), stopPropagation: jest.fn() };
        safeHandler.call({}, mockEvent);
        
        expect(mockErrorHandler).toHaveBeenCalledWith(mockError, mockEvent);
    });

    test('should return empty function for non-function handler', () => {
        const result = window.createSafeEventHandler('not a function');
        expect(typeof result).toBe('function');
        
        const mockEvent = { preventDefault: jest.fn(), stopPropagation: jest.fn() };
        expect(() => result(mockEvent)).not.toThrow();
    });
});

describe('domHelpers - batchDOMOperations', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-element');
        testElement.innerHTML = '';
    });

    test('should execute batch operations successfully', () => {
        const operations = [
            { type: 'setHTML', element: testElement, content: '<span>test</span>' },
            { type: 'setText', element: testElement, content: 'new text' },
            { type: 'setAttributes', element: testElement, content: { 'data-test': 'value' } }
        ];
        
        const result = window.batchDOMOperations(operations);
        expect(result).toBe(true);
    });

    test('should return false for non-array operations', () => {
        const result = window.batchDOMOperations('not an array');
        expect(result).toBe(false);
    });

    test('should handle invalid operation objects', () => {
        const operations = [
            { type: 'setHTML', element: testElement, content: '<span>test</span>' },
            null,
            { type: 'setText', element: testElement, content: 'new text' }
        ];
        
        const result = window.batchDOMOperations(operations);
        expect(result).toBe(false);
    });

    test('should handle unknown operation types', () => {
        const operations = [
            { type: 'unknown', element: testElement, content: 'test' }
        ];
        
        const result = window.batchDOMOperations(operations);
        expect(result).toBe(false);
    });

    test('should handle operation failures', () => {
        const operations = [
            { type: 'setHTML', element: null, content: '<span>test</span>' }
        ];
        
        const result = window.batchDOMOperations(operations);
        expect(result).toBe(false);
    });

    test('should handle errors gracefully', () => {
        // Mock safeSetHTML to throw error
        const original = window.safeSetHTML;
        window.safeSetHTML = jest.fn(() => { throw new Error('Test error'); });
        
        const operations = [
            { type: 'setHTML', element: testElement, content: '<span>test</span>' }
        ];
        
        const result = window.batchDOMOperations(operations);
        expect(result).toBe(false);
        
        window.safeSetHTML = original;
    });
});

describe('domHelpers - configureDOMHelpers', () => {
    beforeEach(() => {
        // Reset configuration
        window.configureDOMHelpers({
            debug: false,
            defaultPolicy: 'general',
            performanceMonitoring: false
        });
    });

    test('should configure debug setting', () => {
        window.configureDOMHelpers({ debug: true });
        expect(window.DOM_CONFIG.DEBUG).toBe(true);
    });

    test('should configure default policy', () => {
        window.configureDOMHelpers({ defaultPolicy: 'trusted' });
        expect(window.DOM_CONFIG.DEFAULT_POLICY).toBe('trusted');
    });

    test('should configure performance monitoring', () => {
        window.configureDOMHelpers({ performanceMonitoring: true });
        expect(window.DOM_CONFIG.PERFORMANCE_MONITORING).toBe(true);
    });

    test('should handle partial configuration', () => {
        window.configureDOMHelpers({ debug: true });
        expect(window.DOM_CONFIG.DEBUG).toBe(true);
        expect(window.DOM_CONFIG.DEFAULT_POLICY).toBe('general'); // unchanged
        expect(window.DOM_CONFIG.PERFORMANCE_MONITORING).toBe(false); // unchanged
    });
});

describe('domHelpers - Integration Tests', () => {
    let testElement;

    beforeEach(() => {
        testElement = document.getElementById('test-container');
        testElement.innerHTML = '';
    });

    test('should work together in complex scenarios', () => {
        // Configure helpers
        window.configureDOMHelpers({ 
            debug: true, 
            performanceMonitoring: true 
        });

        // Create element from template
        const fragment = window.createElementFromTemplate('<div class="test">content</div>');
        expect(fragment).not.toBeNull();

        // Set HTML content
        const setResult = window.safeSetHTML(testElement, '<div>initial</div>');
        expect(setResult).toBe(true);

        // Update attributes
        const attrResult = window.updateAttributes(testElement, { 'data-test': 'value' });
        expect(attrResult).toBe(true);

        // Append more content
        const appendResult = window.appendSafeHTML(testElement, '<span>appended</span>');
        expect(appendResult).toBe(true);

        // Verify final state
        expect(testElement.innerHTML).toContain('initial');
        expect(testElement.innerHTML).toContain('appended');
        expect(testElement.getAttribute('data-test')).toBe('value');
    });

    test('should handle batch operations with mixed types', () => {
        const operations = [
            { type: 'setHTML', element: testElement, content: '<div>start</div>' },
            { type: 'setAttributes', element: testElement, content: { 'class': 'container' } },
            { type: 'setText', element: testElement, content: 'text content' }
        ];

        const result = window.batchDOMOperations(operations);
        expect(result).toBe(true);
    });
});
