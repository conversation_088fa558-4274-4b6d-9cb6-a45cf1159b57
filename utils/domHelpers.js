/**
 * Safe DOM Manipulation Helper Functions
 * Provides secure alternatives to unsafe innerHTML usage
 */

// Global DOM helpers utility - compatible with existing script loading
(function() {
  'use strict';

  // Get sanitizeHTML functions from global scope (loaded by sanitizeHTML.js)
  const sanitizeHTML = window.sanitizeHTML || function(html) { return html; };
  const createSafeElement = window.createSafeElement || function(tag) { return document.createElement(tag); };
  const isContentSafe = window.isContentSafe || function() { return true; };

  /**
   * Configuration for DOM helpers
   */
  const DOM_CONFIG = {
      DEBUG: false, // Set to true to log sanitization events
      DEFAULT_POLICY: 'general',
      PERFORMANCE_MONITORING: false
  };

  /**
   * Helper function to get current timestamp with fallback for environments without performance.now()
   * @returns {number} Current timestamp in milliseconds
   */
  function getCurrentTime() {
      if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
          return performance.now();
      }
      return Date.now();
  }

  /**
   * Performance monitoring for DOM operations
   */
  class DOMPerformanceMonitor {
      constructor() {
          this.operations = new Map();
      }
      
      start(operation) {
          if (!DOM_CONFIG.PERFORMANCE_MONITORING) return;
          this.operations.set(operation, getCurrentTime());
      }
      
      end(operation) {
          if (!DOM_CONFIG.PERFORMANCE_MONITORING) return;
          const startTime = this.operations.get(operation);
          if (startTime) {
              const duration = getCurrentTime() - startTime;
              console.debug(`DOM operation '${operation}' took ${duration.toFixed(2)}ms`);
              this.operations.delete(operation);
          }
      }
  }

  const performanceMonitor = new DOMPerformanceMonitor();

  /**
   * Safely set HTML content on an element
   * @param {HTMLElement} element - Target element
   * @param {string} html - HTML content to set
   * @param {Object} options - Options for sanitization and behavior
   * @returns {boolean} True if operation was successful
   */
  function safeSetHTML(element, html, options = {}) {
      if (!element || !element.nodeType) {
          console.warn('safeSetHTML: Invalid element provided');
          return false;
      }
      
      if (typeof html !== 'string') {
          console.warn('safeSetHTML: HTML content must be a string');
          return false;
      }
      
      performanceMonitor.start('safeSetHTML');
      
      try {
          const policy = options.policy || DOM_CONFIG.DEFAULT_POLICY;
          const trusted = options.trusted === true;
          
          // If content is trusted and explicitly marked, skip sanitization
          if (trusted && policy === 'trusted') {
              if (DOM_CONFIG.DEBUG) {
                  console.debug('safeSetHTML: Setting trusted content without sanitization');
              }
              element.innerHTML = html;
          } else {
              // Sanitize the content
              const sanitizedHTML = sanitizeHTML(html, policy, { trusted });
              
              if (DOM_CONFIG.DEBUG && sanitizedHTML !== html) {
                  console.debug('safeSetHTML: Content was sanitized', {
                      original: html.substring(0, 100),
                      sanitized: sanitizedHTML.substring(0, 100)
                  });
              }
              
              element.innerHTML = sanitizedHTML;
          }
          
          performanceMonitor.end('safeSetHTML');
          return true;
      } catch (error) {
          console.error('safeSetHTML: Error setting HTML content:', error);
          performanceMonitor.end('safeSetHTML');
          return false;
      }
  }

  /**
   * Create an element from a template string
   * @param {string} template - HTML template string
   * @param {Object} options - Options for sanitization
   * @returns {DocumentFragment|null} Created element fragment
   */
  function createElementFromTemplate(template, options = {}) {
      if (typeof template !== 'string') {
          console.warn('createElementFromTemplate: Template must be a string');
          return null;
      }
      
      performanceMonitor.start('createElementFromTemplate');
      
      try {
          const policy = options.policy || DOM_CONFIG.DEFAULT_POLICY;
          const trusted = options.trusted === true;
          
          const safeFragment = createSafeElement(template, policy, { trusted });
          
          if (DOM_CONFIG.DEBUG) {
              console.debug('createElementFromTemplate: Created safe element from template');
          }
          
          performanceMonitor.end('createElementFromTemplate');
          return safeFragment;
      } catch (error) {
          console.error('createElementFromTemplate: Error creating element', error);
          performanceMonitor.end('createElementFromTemplate');
          return null;
      }
  }

  /**
   * Safely update text content of an element
   * @param {HTMLElement} element - Target element
   * @param {string} text - Text content to set
   * @returns {boolean} True if operation was successful
   */
  function updateTextContent(element, text) {
      if (!element || !element.nodeType) {
          console.warn('updateTextContent: Invalid element provided');
          return false;
      }
      
      if (typeof text !== 'string' && text !== null && text !== undefined) {
          console.warn('updateTextContent: Text content must be a string');
          return false;
      }
      
      try {
          element.textContent = text || '';
          return true;
      } catch (error) {
          console.error('updateTextContent: Error setting text content', error);
          return false;
      }
  }

  /**
   * Safely update attributes of an element
   * @param {HTMLElement} element - Target element
   * @param {Object} attributes - Object containing attribute key-value pairs
   * @param {Object} options - Options for attribute validation
   * @returns {boolean} True if operation was successful
   */
  function updateAttributes(element, attributes, options = {}) {
      if (!element || !element.nodeType) {
          console.warn('updateAttributes: Invalid element provided');
          return false;
      }
      
      if (!attributes || typeof attributes !== 'object') {
          console.warn('updateAttributes: Attributes must be an object');
          return false;
      }
      
      const allowedAttributes = options.allowedAttributes || [
          'class', 'id', 'title', 'alt', 'src', 'href', 'data-*', 'aria-*'
      ];
      
      const dangerousAttributes = [
          'onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur',
          'onchange', 'onsubmit', 'onreset', 'onselect', 'onkeydown', 'onkeyup'
      ];
      
      try {
          for (const [key, value] of Object.entries(attributes)) {
              // Check for dangerous attributes
              if (dangerousAttributes.includes(key.toLowerCase())) {
                  console.warn(`updateAttributes: Dangerous attribute '${key}' blocked`);
                  continue;
              }
              
              // Check if attribute is allowed (basic check)
              const isAllowed = allowedAttributes.some(allowed => {
                  if (allowed.endsWith('*')) {
                      return key.startsWith(allowed.slice(0, -1));
                  }
                  return key === allowed;
              });
              
              if (!isAllowed && !options.allowAll) {
                  console.warn(`updateAttributes: Attribute '${key}' not in allowed list`);
                  continue;
              }
              
              // Validate attribute value
              if (typeof value === 'string') {
                  // Check for javascript: URLs
                  if ((key === 'href' || key === 'src') && value.toLowerCase().startsWith('javascript:')) {
                      console.warn(`updateAttributes: JavaScript URL blocked in '${key}' attribute`);
                      continue;
                  }
                  
                  // Check for data: URLs in src attributes (potential XSS)
                  if (key === 'src' && value.toLowerCase().startsWith('data:') && !options.allowDataUrls) {
                      console.warn(`updateAttributes: Data URL blocked in '${key}' attribute`);
                      continue;
                  }
              }
              
              // Set the attribute
              if (value === null || value === undefined) {
                  element.removeAttribute(key);
              } else {
                  element.setAttribute(key, String(value));
              }
          }
          
          return true;
      } catch (error) {
          console.error('updateAttributes: Error setting attributes', error);
          return false;
      }
  }

  /**
   * Append safe HTML content to an element
   * @param {HTMLElement} element - Target element
   * @param {string} html - HTML content to append
   * @param {Object} options - Options for sanitization
   * @returns {boolean} True if operation was successful
   */
  function appendSafeHTML(element, html, options = {}) {
      if (!element || !element.nodeType) {
          console.warn('appendSafeHTML: Invalid element provided');
          return false;
      }
      
      const fragment = createElementFromTemplate(html, options);
      if (!fragment) {
          return false;
      }
      
      try {
          element.appendChild(fragment);
          return true;
      } catch (error) {
          console.error('appendSafeHTML: Error appending content', error);
          return false;
      }
  }

  /**
   * Replace element content with safe HTML
   * @param {HTMLElement} element - Target element
   * @param {string} html - HTML content to replace with
   * @param {Object} options - Options for sanitization
   * @returns {boolean} True if operation was successful
   */
  function replaceSafeHTML(element, html, options = {}) {
      if (!element || !element.nodeType) {
          console.warn('replaceSafeHTML: Invalid element provided');
          return false;
      }
      
      try {
          // Clear existing content
          element.innerHTML = '';
          
          // Append new content
          return appendSafeHTML(element, html, options);
      } catch (error) {
          console.error('replaceSafeHTML: Error replacing content', error);
          return false;
      }
  }

  /**
   * Safely insert HTML at a specific position
   * @param {HTMLElement} element - Target element
   * @param {string} position - Position to insert ('beforebegin', 'afterbegin', 'beforeend', 'afterend')
   * @param {string} html - HTML content to insert
   * @param {Object} options - Options for sanitization
   * @returns {boolean} True if operation was successful
   */
  function insertSafeHTML(element, position, html, options = {}) {
      if (!element || !element.nodeType) {
          console.warn('insertSafeHTML: Invalid element provided');
          return false;
      }
      
      const validPositions = ['beforebegin', 'afterbegin', 'beforeend', 'afterend'];
      if (!validPositions.includes(position)) {
          console.warn('insertSafeHTML: Invalid position provided');
          return false;
      }
      
      const fragment = createElementFromTemplate(html, options);
      if (!fragment) {
          return false;
      }
      
      try {
          // Convert fragment to HTML string for insertAdjacentHTML
          const tempDiv = document.createElement('div');
          tempDiv.appendChild(fragment.cloneNode(true));
          const sanitizedHTML = tempDiv.innerHTML;
          
          element.insertAdjacentHTML(position, sanitizedHTML);
          return true;
      } catch (error) {
          console.error('insertSafeHTML: Error inserting content', error);
          return false;
      }
  }

  /**
   * Create a safe event handler wrapper
   * @param {Function} handler - Event handler function
   * @param {Object} options - Options for event handling
   * @returns {Function} Wrapped event handler
   */
  function createSafeEventHandler(handler, options = {}) {
      if (typeof handler !== 'function') {
          console.warn('createSafeEventHandler: Handler must be a function');
          return () => {};
      }
      
      return function safeHandler(event) {
          try {
              // Prevent default if specified
              if (options.preventDefault) {
                  event.preventDefault();
              }
              
              // Stop propagation if specified
              if (options.stopPropagation) {
                  event.stopPropagation();
              }
              
              // Call the original handler
              return handler.call(this, event);
          } catch (error) {
              console.error('Safe event handler error:', error);
              
              // Call error handler if provided
              if (options.onError && typeof options.onError === 'function') {
                  options.onError(error, event);
              }
          }
      };
  }

  /**
   * Batch DOM operations for better performance
   * @param {Array} operations - Array of DOM operations
   * @returns {boolean} True if all operations were successful
   */
  function batchDOMOperations(operations) {
      if (!Array.isArray(operations)) {
          console.warn('batchDOMOperations: Operations must be an array');
          return false;
      }
      
      performanceMonitor.start('batchDOMOperations');
      
      let allSuccessful = true;
      
      try {
          // Use document fragment for better performance
          const fragment = document.createDocumentFragment();
          
          for (const operation of operations) {
              if (!operation || typeof operation !== 'object') {
                  console.warn('batchDOMOperations: Invalid operation object');
                  allSuccessful = false;
                  continue;
              }
              
              const { type, element, content, options = {} } = operation;
              
              switch (type) {
                  case 'setHTML':
                      if (!safeSetHTML(element, content, options)) {
                          allSuccessful = false;
                      }
                      break;
                      
                  case 'setText':
                      if (!updateTextContent(element, content)) {
                          allSuccessful = false;
                      }
                      break;
                      
                  case 'setAttributes':
                      if (!updateAttributes(element, content, options)) {
                          allSuccessful = false;
                      }
                      break;
                      
                  default:
                      console.warn(`batchDOMOperations: Unknown operation type '${type}'`);
                      allSuccessful = false;
              }
          }
          
          performanceMonitor.end('batchDOMOperations');
          return allSuccessful;
      } catch (error) {
          console.error('batchDOMOperations: Error in batch operations', error);
          performanceMonitor.end('batchDOMOperations');
          return false;
      }
  }

  /**
   * Configure DOM helpers behavior
   * @param {Object} config - Configuration options
   */
  function configureDOMHelpers(config) {
      if (config.debug !== undefined) {
          DOM_CONFIG.DEBUG = Boolean(config.debug);
      }
      
      if (config.defaultPolicy) {
          DOM_CONFIG.DEFAULT_POLICY = config.defaultPolicy;
      }
      
      if (config.performanceMonitoring !== undefined) {
          DOM_CONFIG.PERFORMANCE_MONITORING = Boolean(config.performanceMonitoring);
      }
  }

  // Make functions globally available
  window.safeSetHTML = safeSetHTML;
  window.createElementFromTemplate = createElementFromTemplate;
  window.updateTextContent = updateTextContent;
  window.updateAttributes = updateAttributes;
  window.appendSafeHTML = appendSafeHTML;
  window.replaceSafeHTML = replaceSafeHTML;
  window.insertSafeHTML = insertSafeHTML;
  window.createSafeEventHandler = createSafeEventHandler;
  window.batchDOMOperations = batchDOMOperations;
  window.configureDOMHelpers = configureDOMHelpers;
  window.DOM_CONFIG = DOM_CONFIG;

})(); // End IIFE
