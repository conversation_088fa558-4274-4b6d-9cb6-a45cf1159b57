/**
 * Content Class Manager
 * Automatically applies CSS classes to dynamically added content based on predefined rules
 */

(function() {
  'use strict';

  /**
   * ContentClassManager class for managing dynamic content styling
   */
  class ContentClassManager {
    constructor() {
      this.initialized = false;
      this.observers = new Map();
      this.reprocessTimeout = null;
      this.domReadyHandler = null;
      
      // Configuration for mutation observer
      this.observerConfig = {
        childList: true,      // Watch for changes to child elements
        subtree: true,        // Watch the entire subtree
        attributes: false,    // Don't watch attribute changes (performance)
        characterData: false  // Don't watch text changes (performance)
      };
      
      // Default class mapping rules
      this.classRules = new Map();
      this.setupDefaultRules();
    }

    /**
     * Set up default class mapping rules
     */
    setupDefaultRules() {
      // Product type rules
      this.classRules.set('product-type', {
        't-shirt': 'product-t-shirt',
        'hoodie': 'product-hoodie',
        'sweatshirt': 'product-sweatshirt',
        'tank-top': 'product-tank-top',
        'long-sleeve': 'product-long-sleeve',
        'raglan': 'product-raglan',
        'v-neck': 'product-v-neck',
        'phone-case': 'product-phone-case',
        'popsocket': 'product-popsocket',
        'tote-bag': 'product-tote-bag',
        'throw-pillow': 'product-throw-pillow',
        'tumbler': 'product-tumbler'
      });

      // Gender type rules
      this.classRules.set('gender-type', {
        'male': 'gender-male',
        'female': 'gender-female',
        'unisex': 'gender-unisex',
        'youth': 'gender-youth',
        'girls': 'gender-girls'
      });

      // Fit type rules
      this.classRules.set('fit-type', {
        'regular': 'fit-regular',
        'slim': 'fit-slim',
        'loose': 'fit-loose',
        'relaxed': 'fit-relaxed'
      });

      // Marketplace rules
      this.classRules.set('marketplace', {
        'US': 'marketplace-us',
        'UK': 'marketplace-uk',
        'DE': 'marketplace-de',
        'FR': 'marketplace-fr',
        'IT': 'marketplace-it',
        'ES': 'marketplace-es',
        'JP': 'marketplace-jp'
      });

      // Status rules
      this.classRules.set('status', {
        'active': 'status-active',
        'inactive': 'status-inactive',
        'pending': 'status-pending',
        'draft': 'status-draft'
      });

      // Performance rules
      this.classRules.set('performance', {
        'high': 'performance-high',
        'medium': 'performance-medium',
        'low': 'performance-low',
        'no-sales': 'performance-no-sales'
      });
    }

    /**
     * Initialize the content class manager
     */
    init() {
      if (this.initialized) {
        console.log('ContentClassManager: Already initialized');
        return;
      }

      try {
        console.log('ContentClassManager: Initializing...');
        
        // Set up mutation observer for dynamic content
        this.setupMutationObserver();
        
        // Process existing content
        this.processExistingContent();
        
        this.initialized = true;
        console.log('ContentClassManager: Initialization complete');
      } catch (error) {
        console.error('ContentClassManager: Initialization failed:', error);
      }
    }

    /**
     * Process existing content in the DOM
     */
    processExistingContent() {
      try {
        // Process all elements that might need class updates
        const elementsToProcess = document.querySelectorAll('[data-product-type], [data-gender-type], [data-fit-type], [data-marketplace], [data-status], [data-performance]');
        
        elementsToProcess.forEach(element => {
          this.applyClassesToElement(element);
        });
        
        if (elementsToProcess.length > 0) {
          console.log(`ContentClassManager: Processed ${elementsToProcess.length} existing elements`);
        }
      } catch (error) {
        console.error('ContentClassManager: Error processing existing content:', error);
      }
    }

    /**
     * Set up mutation observer to watch for new content
     */
    setupMutationObserver() {
      const mutationCallback = (mutations) => {
        let hasRelevantChanges = false;
        
        mutations.forEach(mutation => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(node => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check if the added element needs class updates
                if (this.elementNeedsClassUpdate(node)) {
                  hasRelevantChanges = true;
                  this.applyClassesToElement(node);
                }
                
                // Check child elements as well
                const childElements = node.querySelectorAll('[data-product-type], [data-gender-type], [data-fit-type], [data-marketplace], [data-status], [data-performance]');
                childElements.forEach(child => {
                  hasRelevantChanges = true;
                  this.applyClassesToElement(child);
                });
              }
            });
          }
        });
        
        // If we have changes, schedule a reprocess to catch any missed elements
        if (hasRelevantChanges) {
          this.scheduleReprocess();
        }
      };

      // Create and configure the mutation observer
      const observer = new MutationObserver(mutationCallback);
      
      // Start observing the entire document body for changes
      // This ensures we catch all dynamically added content regardless of location
      observer.observe(document.body, this.observerConfig);

      // Store the observer reference for proper cleanup
      this.observers.set('main', observer);
    }

    /**
     * Check if an element needs class updates
     */
    elementNeedsClassUpdate(element) {
      return element.hasAttribute('data-product-type') ||
             element.hasAttribute('data-gender-type') ||
             element.hasAttribute('data-fit-type') ||
             element.hasAttribute('data-marketplace') ||
             element.hasAttribute('data-status') ||
             element.hasAttribute('data-performance');
    }

    /**
     * Apply appropriate classes to an element based on its data attributes
     */
    applyClassesToElement(element) {
      try {
        // Apply product type classes
        if (element.hasAttribute('data-product-type')) {
          const productType = element.getAttribute('data-product-type');
          const classMap = this.classRules.get('product-type');
          if (classMap && classMap[productType]) {
            element.classList.add(classMap[productType]);
          }
        }

        // Apply gender type classes
        if (element.hasAttribute('data-gender-type')) {
          const genderType = element.getAttribute('data-gender-type');
          const classMap = this.classRules.get('gender-type');
          if (classMap && classMap[genderType]) {
            element.classList.add(classMap[genderType]);
          }
        }

        // Apply fit type classes
        if (element.hasAttribute('data-fit-type')) {
          const fitType = element.getAttribute('data-fit-type');
          const classMap = this.classRules.get('fit-type');
          if (classMap && classMap[fitType]) {
            element.classList.add(classMap[fitType]);
          }
        }

        // Apply marketplace classes
        if (element.hasAttribute('data-marketplace')) {
          const marketplace = element.getAttribute('data-marketplace');
          const classMap = this.classRules.get('marketplace');
          if (classMap && classMap[marketplace]) {
            element.classList.add(classMap[marketplace]);
          }
        }

        // Apply status classes
        if (element.hasAttribute('data-status')) {
          const status = element.getAttribute('data-status');
          const classMap = this.classRules.get('status');
          if (classMap && classMap[status]) {
            element.classList.add(classMap[status]);
          }
        }

        // Apply performance classes
        if (element.hasAttribute('data-performance')) {
          const performance = element.getAttribute('data-performance');
          const classMap = this.classRules.get('performance');
          if (classMap && classMap[performance]) {
            element.classList.add(classMap[performance]);
          }
        }
      } catch (error) {
        console.error('ContentClassManager: Error applying classes to element:', {
          error: error.message,
          stack: error.stack,
          element: element.tagName,
          attributes: {
            'data-product-type': element.getAttribute('data-product-type'),
            'data-gender-type': element.getAttribute('data-gender-type'),
            'data-fit-type': element.getAttribute('data-fit-type'),
            'data-marketplace': element.getAttribute('data-marketplace'),
            'data-status': element.getAttribute('data-status'),
            'data-performance': element.getAttribute('data-performance')
          }
        });
      }
    }

    /**
     * Schedule a reprocess to catch any missed elements
     */
    scheduleReprocess() {
      if (this.reprocessTimeout) {
        clearTimeout(this.reprocessTimeout);
      }
      
      this.reprocessTimeout = setTimeout(() => {
        this.processExistingContent();
      }, 100); // Small delay to ensure DOM is stable
    }

    /**
     * Add a custom class rule
     */
    addClassRule(category, rules) {
      if (!this.classRules.has(category)) {
        this.classRules.set(category, {});
      }
      
      Object.assign(this.classRules.get(category), rules);
      console.log(`ContentClassManager: Added custom class rule for category '${category}'`);
    }

    /**
     * Remove a class rule
     */
    removeClassRule(category, ruleKey) {
      if (this.classRules.has(category) && this.classRules.get(category)[ruleKey]) {
        delete this.classRules.get(category)[ruleKey];
        console.log(`ContentClassManager: Removed class rule '${ruleKey}' from category '${category}'`);
      }
    }

    /**
     * Get all class rules
     */
    getClassRules() {
      const rules = {};
      this.classRules.forEach((value, key) => {
        rules[key] = { ...value };
      });
      return rules;
    }

    /**
     * Clean up observers and handlers when the manager is destroyed
     * Ensures proper memory cleanup and prevents memory leaks
     */
    destroy() {
      // Clear any pending timeouts to prevent memory leaks
      if (this.reprocessTimeout) {
        clearTimeout(this.reprocessTimeout);
        this.reprocessTimeout = null;
      }
      
      // Disconnect all mutation observers
      this.observers.forEach(observer => observer.disconnect());
      this.observers.clear();
      
      // Remove event listeners if they exist
      if (this.domReadyHandler) {
        document.removeEventListener('DOMContentLoaded', this.domReadyHandler);
        this.domReadyHandler = null;
      }
      
      // Reset initialization state
      this.initialized = false;
    }
  }

  /**
   * Factory function to create and initialize a ContentClassManager instance
   * Only initializes when running in a browser environment to prevent SSR issues
   * @returns {ContentClassManager|null} The initialized manager instance or null if not in browser
   */
  function createContentClassManager() {
    // Check if we're in a browser environment with DOM access
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return null;
    }
    
    const manager = new ContentClassManager();
    
    // Set up DOM ready handler for initialization
    if (document.readyState === 'loading') {
      manager.domReadyHandler = () => {
        manager.init();
      };
      document.addEventListener('DOMContentLoaded', manager.domReadyHandler);
    } else {
      // DOM is already ready, initialize immediately
      manager.init();
    }
    
    return manager;
  }

  /**
   * Get or create the global content class manager instance
   * Provides a singleton pattern while avoiding SSR issues
   * @returns {ContentClassManager|null} The global manager instance or null if not in browser
   */
  function getContentClassManager() {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return null;
    }
    
    // Create global instance if it doesn't exist
    if (!window.contentClassManager) {
      window.contentClassManager = createContentClassManager();
    }
    
    return window.contentClassManager;
  }

  // Make functions and class available globally
  window.ContentClassManager = ContentClassManager;
  window.createContentClassManager = createContentClassManager;
  window.getContentClassManager = getContentClassManager;

  // Auto-initialize if DOM is ready
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        getContentClassManager();
      });
    } else {
      getContentClassManager();
    }
  }

})(); // End IIFE
