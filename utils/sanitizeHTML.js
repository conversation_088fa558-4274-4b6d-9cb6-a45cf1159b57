/**
 * HTML Sanitization Utility
 * Provides secure HTML handling capabilities using DOMPurify
 */

// Import DOMPurify - will be available after package.json update
// For now, we'll create a fallback implementation
let DOMPurify;
try {
    // Try to import DOMPurify if available
    if (typeof window !== 'undefined' && window.DOMPurify) {
        DOMPurify = window.DOMPurify;
    }
} catch (e) {
    console.warn('DOMPurify not available, using fallback sanitization');
}

/**
 * Security policies for different content contexts
 */
const SECURITY_POLICIES = {
    // For general HTML content (tooltips, descriptions)
    general: {
        ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'span', 'div', 'ul', 'ol', 'li', 'a'],
        ALLOWED_ATTR: ['class', 'id', 'href', 'title', 'alt'],
        ALLOW_DATA_ATTR: false,
        FORBID_TAGS: ['script', 'object', 'embed', 'iframe', 'form', 'input'],
        FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur']
    },
    
    // For user input content (reviews, comments)
    userInput: {
        ALLOWED_TAGS: ['p', 'br', 'strong', 'em'],
        ALLOWED_ATTR: ['class'],
        ALLOW_DATA_ATTR: false,
        FORBID_TAGS: ['script', 'object', 'embed', 'iframe', 'form', 'input', 'a'],
        FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur', 'href']
    },
    
    // For chart and data visualization content
    charts: {
        ALLOWED_TAGS: ['svg', 'path', 'circle', 'rect', 'text', 'g', 'defs', 'use'],
        ALLOWED_ATTR: ['class', 'id', 'd', 'cx', 'cy', 'r', 'x', 'y', 'width', 'height', 'fill', 'stroke', 'viewBox'],
        ALLOW_DATA_ATTR: false,
        FORBID_TAGS: ['script', 'object', 'embed', 'iframe', 'form', 'input'],
        FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur']
    },
    
    // For trusted static content (templates, components)
    trusted: {
        // More permissive for trusted content, but still safe
        ALLOWED_TAGS: ['div', 'span', 'p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'img', 'svg', 'path', 'circle', 'rect', 'text', 'g'],
        ALLOWED_ATTR: ['class', 'id', 'href', 'src', 'alt', 'title', 'd', 'cx', 'cy', 'r', 'x', 'y', 'width', 'height', 'fill', 'stroke', 'viewBox'],
        ALLOW_DATA_ATTR: true,
        FORBID_TAGS: ['script', 'object', 'embed', 'iframe', 'form'],
        FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur']
    }
};

/**
 * Fallback sanitization for when DOMPurify is not available
 * Basic XSS protection by removing dangerous patterns
 */
function fallbackSanitize(html, policy = 'general') {
    if (typeof html !== 'string') {
        return '';
    }
    
    // Remove script tags and their content
    html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    
    // Remove dangerous event handlers
    html = html.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
    html = html.replace(/\s*on\w+\s*=\s*[^>\s]+/gi, '');
    
    // Remove javascript: URLs
    html = html.replace(/href\s*=\s*["']javascript:[^"']*["']/gi, '');
    html = html.replace(/src\s*=\s*["']javascript:[^"']*["']/gi, '');
    
    // Remove data: URLs for images (potential XSS vector)
    html = html.replace(/src\s*=\s*["']data:[^"']*["']/gi, '');
    
    // Remove dangerous tags
    const dangerousTags = ['object', 'embed', 'iframe', 'form', 'input', 'meta', 'link'];
    dangerousTags.forEach(tag => {
        const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
        html = html.replace(regex, '');
        const selfClosing = new RegExp(`<${tag}\\b[^>]*\\/?>`, 'gi');
        html = html.replace(selfClosing, '');
    });
    
    return html;
}

/**
 * Main sanitization function
 * @param {string} html - HTML content to sanitize
 * @param {string} policy - Security policy to apply ('general', 'userInput', 'charts', 'trusted')
 * @param {Object} options - Additional options
 * @returns {string} Sanitized HTML
 */
// Global HTML sanitization utility - compatible with existing script loading
(function() {
  'use strict';

function sanitizeHTML(html, policy = 'general', options = {}) {
    if (!html || typeof html !== 'string') {
        return '';
    }
    
    // If content is explicitly marked as trusted and we're in trusted policy, return as-is
    if (policy === 'trusted' && options.trusted === true) {
        return html;
    }
    
    const securityPolicy = SECURITY_POLICIES[policy] || SECURITY_POLICIES.general;
    
    if (DOMPurify) {
        try {
            return DOMPurify.sanitize(html, securityPolicy);
        } catch (error) {
            console.warn('DOMPurify sanitization failed, using fallback:', error);
            return fallbackSanitize(html, policy);
        }
    } else {
        return fallbackSanitize(html, policy);
    }
}

/**
 * Sanitize user input with strict policy
 * @param {string} input - User input to sanitize
 * @returns {string} Sanitized input
 */
function sanitizeUserInput(input) {
    return sanitizeHTML(input, 'userInput');
}

/**
 * Sanitize chart/SVG content
 * @param {string} svgContent - SVG content to sanitize
 * @returns {string} Sanitized SVG
 */
function sanitizeChartContent(svgContent) {
    return sanitizeHTML(svgContent, 'charts');
}

/**
 * Create a safe DOM element from HTML string
 * @param {string} html - HTML string
 * @param {string} policy - Security policy
 * @param {Object} options - Additional options
 * @returns {DocumentFragment} Safe DOM fragment
 */
function createSafeElement(html, policy = 'general', options = {}) {
    const sanitizedHTML = sanitizeHTML(html, policy, options);
    const template = document.createElement('template');
    template.innerHTML = sanitizedHTML;
    return template.content;
}

/**
 * Check if content contains potential XSS vectors
 * @param {string} content - Content to check
 * @returns {boolean} True if content appears safe
 */
function isContentSafe(content) {
    if (!content || typeof content !== 'string') {
        return true;
    }
    
    // Check for common XSS patterns
    const xssPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe/i,
        /<object/i,
        /<embed/i,
        /data:.*base64/i,
        /vbscript:/i,
        /expression\s*\(/i
    ];
    
    return !xssPatterns.some(pattern => pattern.test(content));
}

/**
 * Escape HTML entities for safe text display
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHTML(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }
    
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Strip all HTML tags and return plain text
 * @param {string} html - HTML content
 * @returns {string} Plain text
 */
function stripHTML(html) {
    if (!html || typeof html !== 'string') {
        return '';
    }
    
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
}

// Make functions globally available
window.sanitizeHTML = sanitizeHTML;
window.sanitizeUserInput = sanitizeUserInput;
window.sanitizeChartContent = sanitizeChartContent;
window.createSafeElement = createSafeElement;
window.isContentSafe = isContentSafe;
window.escapeHTML = escapeHTML;
window.stripHTML = stripHTML;
window.SECURITY_POLICIES = SECURITY_POLICIES;

})(); // End IIFE
