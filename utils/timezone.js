/**
 * Centralized timezone management utility for Snap Dashboard
 * Ensures consistent Pacific Time (America/Los_Angeles) usage across all components
 *
 * This file provides global functions for timezone management.
 * All functions are attached to the window object for global access.
 */

(function() {
  'use strict';

  /**
   * Get current date and time in Pacific Time (America/Los_Angeles)
   * This is the single source of truth for all Pacific Time calculations in the dashboard
   *
   * @returns {Date} A Date object representing the current Pacific Time
   */
  function getPacificTime() {
    const now = new Date();

    // Use Intl.DateTimeFormat for more reliable Pacific Time conversion
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: 'America/Los_Angeles',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });

    const parts = formatter.formatToParts(now);
    const year = parseInt(parts.find(p => p.type === 'year').value);
    const month = parseInt(parts.find(p => p.type === 'month').value) - 1; // Month is 0-based
    const day = parseInt(parts.find(p => p.type === 'day').value);
    const hour = parseInt(parts.find(p => p.type === 'hour').value);
    const minute = parseInt(parts.find(p => p.type === 'minute').value);
    const second = parseInt(parts.find(p => p.type === 'second').value);

    // Create Date object with Pacific Time components
    const pacificTime = new Date(year, month, day, hour, minute, second);

    if (window.SnapLogger) {
      window.SnapLogger.debug('🌐 Pacific Time calculated:', {
        utc: now.toISOString(),
        pacificComponents: { year, month: month + 1, day, hour, minute, second },
        pacific: pacificTime.toISOString(),
        formatted: pacificTime.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        })
      });
    }

    return pacificTime;
  }

  /**
   * Get current date in Pacific Time with time set to midnight
   * Useful for date-only comparisons and calculations
   *
   * @returns {Date} A Date object representing today's date in Pacific Time at midnight
   */
  function getPacificDate() {
    const pacificTime = getPacificTime();

    // Set time to midnight for date-only operations
    const pacificDate = new Date(pacificTime);
    pacificDate.setHours(0, 0, 0, 0);

    return pacificDate;
  }

  /**
   * Format a date for display using Pacific Time
   *
   * @param {Date} date - The date to format (should be a Pacific Time Date object)
   * @param {Object} options - Formatting options (same as toLocaleDateString options)
   * @returns {string} Formatted date string
   */
  function formatPacificDate(date, options = {}) {
    // Since the date is already in Pacific Time, format it directly
    // without timezone conversion to avoid double conversion
    return date.toLocaleDateString('en-US', options);
  }

  /**
   * Get a date relative to today in Pacific Time
   *
   * @param {number} daysOffset - Number of days to offset (negative for past dates)
   * @returns {Date} Date object representing the offset date in Pacific Time
   */
  function getPacificDateOffset(daysOffset) {
    const today = getPacificDate();
    const offsetDate = new Date(today);
    offsetDate.setDate(today.getDate() + daysOffset);

    return offsetDate;
  }

  /**
   * Check if a date is today in Pacific Time
   *
   * @param {Date} date - The date to check
   * @returns {boolean} True if the date is today in Pacific Time
   */
  function isPacificToday(date) {
    const today = getPacificDate();
    const checkDate = new Date(date);
    checkDate.setHours(0, 0, 0, 0);

    return checkDate.getTime() === today.getTime();
  }

  /**
   * Get month and day from Pacific Time for year-over-year comparisons
   *
   * @returns {Object} Object with month (0-based) and day properties
   */
  function getPacificMonthDay() {
    const pacificTime = getPacificTime();

    return {
      month: pacificTime.getMonth(), // 0-based month
      day: pacificTime.getDate()
    };
  }

  /**
   * Get current Pacific date as YYYY-MM-DD string
   * This is used by DateChangeManager for date comparison
   *
   * @returns {string} Date string in YYYY-MM-DD format
   */
  function getCurrentPacificDateString() {
    const pacificTime = getPacificTime();
    const year = pacificTime.getFullYear();
    const month = String(pacificTime.getMonth() + 1).padStart(2, '0');
    const day = String(pacificTime.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  /**
   * Validate timezone accuracy by comparing with multiple methods
   * This helps ensure our Pacific Time calculations are correct
   *
   * @returns {Object} Validation results
   */
  function validateTimezoneAccuracy() {
    try {
      const now = new Date();

      // Method 1: Our current implementation
      const ourPacificTime = getPacificTime();

      // Method 2: Direct toLocaleString approach
      const directPacific = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));

      // Method 3: UTC offset calculation
      const pacificOffset = now.getTimezoneOffset() + (now.getTimezoneOffset() > 0 ? 480 : 420); // PST/PDT
      const offsetPacific = new Date(now.getTime() - (pacificOffset * 60000));

      // Compare results (allow 1 second difference for execution time)
      const diff1 = Math.abs(ourPacificTime.getTime() - directPacific.getTime());
      const diff2 = Math.abs(ourPacificTime.getTime() - offsetPacific.getTime());

      const isAccurate = diff1 <= 1000 && diff2 <= 60000; // Allow larger diff for offset method

      return {
        isAccurate,
        ourMethod: ourPacificTime.toISOString(),
        directMethod: directPacific.toISOString(),
        offsetMethod: offsetPacific.toISOString(),
        differences: { direct: diff1, offset: diff2 },
        timestamp: now.toISOString()
      };
    } catch (error) {
      console.error('❌ Error validating timezone accuracy:', error);
      return {
        isAccurate: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Attach functions to window object for global access
  window.SnapTimezone = {
    getPacificTime,
    getPacificDate,
    formatPacificDate,
    getPacificDateOffset,
    isPacificToday,
    getPacificMonthDay,
    getCurrentPacificDateString,
    validateTimezoneAccuracy
  };

  if (window.SnapLogger) {
    window.SnapLogger.info('🌐 SnapTimezone utility loaded - Pacific Time management available globally');
  }

})();
