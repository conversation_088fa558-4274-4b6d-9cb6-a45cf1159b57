{"name": "snap-dashboard-dom-helpers", "version": "1.0.0", "description": "DOM Helper Functions for Snap Dashboard with comprehensive unit testing", "main": "utils/domHelpers.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose"}, "devDependencies": {"jest": "^29.7.0", "jsdom": "^23.0.1"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/utils/domHelpers.test.js"], "collectCoverageFrom": ["utils/domHelpers.js", "!**/node_modules/**", "!**/coverage/**"], "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"]}, "keywords": ["dom", "helpers", "security", "sanitization", "testing", "jest"], "author": "Snap Dashboard Team", "license": "MIT"}