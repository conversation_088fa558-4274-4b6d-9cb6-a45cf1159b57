/**
 * Virtual List Styles
 * Optimized CSS for virtual scrolling with massive datasets
 * Uses CSS containment and performance optimizations
 */

.virtual-list {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    contain: layout style paint;
}

/* Header Section */
.virtual-list-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
    border-radius: 8px 8px 0 0;
    flex-shrink: 0;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-results-count {
    font-size: 12px;
    color: #6b7280;
    white-space: nowrap;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-field {
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.sort-direction {
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.sort-direction:hover {
    background: #f3f4f6;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.marketplace-filter {
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

/* Viewport Section */
.virtual-list-viewport {
    flex: 1;
    position: relative;
    overflow: auto;
    contain: strict;
    will-change: scroll-position;
}

.virtual-list-scroll-container {
    position: relative;
    width: 100%;
    contain: layout style paint;
}

.virtual-list-items {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    contain: layout style paint;
    will-change: transform;
}

/* Individual Item Styles */
.virtual-list-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
    background: white;
    transition: background-color 0.15s ease;
    contain: layout style paint;
}

.virtual-list-item:hover {
    background: #f9fafb;
}

.virtual-list-item:last-child {
    border-bottom: none;
}

.item-content {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.item-image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    background: #f3f4f6;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.2s ease;
}

.item-image img[src="/placeholder.jpg"] {
    opacity: 0.5;
}

.item-details {
    flex: 1;
    min-width: 0;
}

.item-title {
    font-weight: 600;
    font-size: 14px;
    color: #111827;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.item-asin {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
    font-family: monospace;
}

.item-description {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.item-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 11px;
}

.marketplace {
    background: #dbeafe;
    color: #1e40af;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
}

.price {
    font-weight: 600;
    color: #059669;
}

.updated {
    color: #6b7280;
}

.item-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.item-actions button {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-view {
    color: #3b82f6;
    border-color: #3b82f6;
}

.btn-view:hover {
    background: #3b82f6;
    color: white;
}

.btn-edit {
    color: #6b7280;
}

.btn-edit:hover {
    background: #f3f4f6;
    color: #374151;
}

/* Search Highlighting */
mark {
    background: #fef3c7;
    color: #92400e;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Loading Indicator */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-indicator span {
    font-size: 14px;
    color: #6b7280;
}

/* Footer Section */
.virtual-list-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
    border-radius: 0 0 8px 8px;
    flex-shrink: 0;
}

.items-info {
    font-size: 12px;
    color: #6b7280;
}

.performance-stats {
    font-size: 11px;
    color: #9ca3af;
    font-family: monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
    .virtual-list-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .search-container {
        flex-direction: column;
        align-items: stretch;
    }

    .sort-controls,
    .filter-controls {
        justify-content: center;
    }

    .item-content {
        gap: 8px;
    }

    .item-image {
        width: 48px;
        height: 48px;
    }

    .item-actions {
        flex-direction: column;
        gap: 4px;
    }

    .item-actions button {
        padding: 4px 8px;
        font-size: 11px;
    }

    .virtual-list-footer {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .virtual-list-header {
        padding: 12px;
    }

    .virtual-list-item {
        padding: 8px 12px;
    }

    .item-title {
        font-size: 13px;
    }

    .item-description {
        display: none;
    }

    .item-meta {
        gap: 8px;
    }

    .virtual-list-footer {
        padding: 8px 12px;
    }
}

/* Performance Optimizations */
.virtual-list-viewport {
    /* Enable hardware acceleration */
    transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
}

.virtual-list-item {
    /* Optimize for frequent repaints */
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Skeleton Loading States */
.virtual-list-item.skeleton {
    pointer-events: none;
}

.virtual-list-item.skeleton .item-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.virtual-list-item.skeleton .item-title,
.virtual-list-item.skeleton .item-description,
.virtual-list-item.skeleton .item-asin {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    color: transparent;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .virtual-list {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .virtual-list {
        background: #1f2937;
        color: #f9fafb;
    }

    .virtual-list-header,
    .virtual-list-footer {
        background: #111827;
        border-color: #374151;
    }

    .virtual-list-item {
        background: #1f2937;
        border-color: #374151;
    }

    .virtual-list-item:hover {
        background: #374151;
    }

    .search-input,
    .sort-field,
    .sort-direction,
    .marketplace-filter {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .item-title {
        color: #f9fafb;
    }

    .item-asin,
    .item-description,
    .updated {
        color: #9ca3af;
    }

    .marketplace {
        background: #1e3a8a;
        color: #93c5fd;
    }

    .price {
        color: #34d399;
    }
}
