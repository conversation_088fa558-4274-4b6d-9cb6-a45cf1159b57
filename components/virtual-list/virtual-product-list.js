/**
 * Virtual Product List Component
 * Efficiently displays millions of products using virtual scrolling
 * Only renders visible items to maintain performance with 5M+ products
 */

class VirtualProductList {
    constructor(container, queryManager, options = {}) {
        this.container = container;
        this.queryManager = queryManager;

        // Configuration
        this.itemHeight = options.itemHeight || 80;
        this.bufferSize = options.bufferSize || 10;
        this.pageSize = options.pageSize || 100;
        this.searchDebounceMs = options.searchDebounceMs || 300;
        this.maxMemoryPages = options.maxMemoryPages || 5; // Keep only ±5 pages in memory
        this.recyclePoolSize = options.recyclePoolSize || 50; // DOM element pool size

        // State
        this.items = [];
        this.filteredItems = [];
        this.visibleItems = [];
        this.scrollTop = 0;
        this.containerHeight = 0;
        this.totalHeight = 0;
        this.totalCount = 0; // Total count from QueryManager
        this.startIndex = 0;
        this.endIndex = 0;
        this.isLoading = false;
        this.hasMore = true;
        this.currentFilter = {};
        this.currentSort = { field: 'title', direction: 'asc' };
        this.searchQuery = '';
        this.isSearchMode = false; // Track if we're in search mode
        this.originalTotalCount = 0; // Store original total count for restoring after search
        this.windowBaseIndex = 0; // Global offset for memory window tracking

        // Performance optimizations
        this.recyclePool = []; // Pool of reusable DOM elements
        this.activeElements = new Map(); // Map of index -> DOM element
        this.scrollRAF = null; // RequestAnimationFrame ID for scroll batching
        this.lastScrollTime = 0;
        this.memoryWindow = { start: 0, end: 0 }; // Current memory window

        // Windowed pagination support
        this.pageAnchors = new Map(); // Map of page number -> cursor key for bidirectional paging
        this.loadedPages = new Map(); // Map of page number -> data array
        this.loadingPages = new Set(); // Set of page numbers currently being loaded
        this.nextCursor = null; // Cursor for append-only loading (legacy support)

        // DOM elements
        this.viewport = null;
        this.scrollContainer = null;
        this.itemsContainer = null;
        this.loadingIndicator = null;
        this.searchInput = null;

        // Event handlers
        this.onScroll = this.onScroll.bind(this);
        this.onResize = this.onResize.bind(this);
        this.onSearch = this.debounce(this.handleSearch.bind(this), this.searchDebounceMs);

        // Initialize
        this.init();
    }

    /**
     * Initialize the virtual list component
     */
    init() {
        this.ensureStylesLoaded();
        this.createDOM();
        this.attachEventListeners();
        this.updateDimensions();

        // Attach instance to container for MemoryMonitor optimization
        this.container.classList.add('virtual-list');
        this.container.virtualListInstance = this;

        this.loadInitialData();
    }

    /**
     * Ensure virtual list styles are loaded
     */
    ensureStylesLoaded() {
        // Check if styles are already loaded to prevent duplicates
        if (document.querySelector('link[href*="virtual-list-styles.css"]') ||
            document.querySelector('style[data-virtual-list-styles]')) {
            return;
        }

        // Try to load external CSS file first
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'components/virtual-list/virtual-list-styles.css';
        link.onerror = () => {
            // Fallback: inject minimal critical styles inline
            this.injectFallbackStyles();
        };
        document.head.appendChild(link);
    }

    /**
     * Inject fallback styles if external CSS fails to load
     */
    injectFallbackStyles() {
        const style = document.createElement('style');
        style.setAttribute('data-virtual-list-styles', 'fallback');
        style.textContent = `
            .virtual-list {
                position: relative;
                height: 100%;
                overflow: hidden;
            }
            .virtual-list-viewport {
                height: 100%;
                overflow-y: auto;
                overflow-x: hidden;
            }
            .virtual-list-items {
                position: relative;
            }
            .virtual-list-item {
                position: absolute;
                left: 0;
                right: 0;
                box-sizing: border-box;
                border-bottom: 1px solid #eee;
                padding: 10px;
                background: white;
            }
            .virtual-list-loading {
                text-align: center;
                padding: 20px;
                color: #666;
            }
        `;
        document.head.appendChild(style);
        console.log('✅ Virtual list fallback styles injected');
    }

    /**
     * Create DOM structure for virtual list
     */
    createDOM() {
        this.container.innerHTML = `
            <div class="virtual-list">
                <div class="virtual-list-header">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search products..." />
                        <div class="search-results-count"></div>
                    </div>
                    <div class="sort-controls">
                        <select class="sort-field">
                            <option value="title">Title (DB)</option>
                            <option value="asin">ASIN (Client)</option>
                            <option value="price">Price (DB)</option>
                            <option value="lastUpdated">Last Updated (DB)</option>
                            <option value="status">Status (DB)</option>
                            <option value="marketplace">Marketplace (DB)</option>
                            <option value="category">Category (DB)</option>
                        </select>
                        <button class="sort-direction" data-direction="asc">↑</button>
                    </div>
                    <div class="filter-controls">
                        <select class="marketplace-filter">
                            <option value="">All Marketplaces</option>
                            <option value="amazon.com">Amazon.com</option>
                            <option value="amazon.co.uk">Amazon.co.uk</option>
                            <option value="amazon.de">Amazon.de</option>
                        </select>
                    </div>
                </div>
                <div class="virtual-list-viewport">
                    <div class="virtual-list-scroll-container">
                        <div class="virtual-list-items"></div>
                    </div>
                    <div class="loading-indicator">
                        <div class="spinner"></div>
                        <span>Loading products...</span>
                    </div>
                </div>
                <div class="virtual-list-footer">
                    <div class="items-info"></div>
                    <div class="performance-stats"></div>
                </div>
            </div>
        `;

        // Get DOM references
        this.viewport = this.container.querySelector('.virtual-list-viewport');
        this.scrollContainer = this.container.querySelector('.virtual-list-scroll-container');
        this.itemsContainer = this.container.querySelector('.virtual-list-items');
        this.loadingIndicator = this.container.querySelector('.loading-indicator');
        this.searchInput = this.container.querySelector('.search-input');
        this.sortField = this.container.querySelector('.sort-field');
        this.sortDirection = this.container.querySelector('.sort-direction');
        this.marketplaceFilter = this.container.querySelector('.marketplace-filter');
        this.resultsCount = this.container.querySelector('.search-results-count');
        this.itemsInfo = this.container.querySelector('.items-info');
        this.performanceStats = this.container.querySelector('.performance-stats');
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        this.viewport.addEventListener('scroll', this.onScroll);
        window.addEventListener('resize', this.onResize);
        
        this.searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.onSearch();
        });
        
        this.sortField.addEventListener('change', () => {
            this.currentSort.field = this.sortField.value;
            this.applySortAndFilter();
        });
        
        this.sortDirection.addEventListener('click', () => {
            const currentDirection = this.sortDirection.dataset.direction;
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            this.sortDirection.dataset.direction = newDirection;
            this.sortDirection.textContent = newDirection === 'asc' ? '↑' : '↓';
            this.currentSort.direction = newDirection;
            this.applySortAndFilter();
        });
        
        this.marketplaceFilter.addEventListener('change', () => {
            this.currentFilter.marketplace = this.marketplaceFilter.value || undefined;
            this.applySortAndFilter();
        });
    }

    /**
     * Update component dimensions
     */
    updateDimensions() {
        const rect = this.viewport.getBoundingClientRect();
        this.containerHeight = rect.height;
        this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.bufferSize * 2;
        this.updateScrollContainer();
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        this.showLoading(true);

        try {
            // Fetch total count for proper windowing
            this.totalCount = await this.queryManager.countProducts(this.currentFilter);
            this.updateTotalHeight();

            const result = await this.queryManager.getProductsPaginated(
                this.currentFilter,
                this.pageSize,
                null,
                this.currentSort
            );

            this.items = result.data;
            this.filteredItems = this._canSortByIndex() ? [...this.items] : this.sortItems([...this.items]);
            this.hasMore = result.hasMore;
            this.nextCursor = result.nextCursor;
            this.windowBaseIndex = 0; // Reset window base index for new data

            this.updateDisplay();
            this.updateInfo();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showError('Failed to load products');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Handle scroll events
     */
    onScroll() {
        // Cancel previous RAF if still pending
        if (this.scrollRAF) {
            cancelAnimationFrame(this.scrollRAF);
        }

        // Batch scroll updates using RAF
        this.scrollRAF = requestAnimationFrame(() => {
            this.scrollTop = this.viewport.scrollTop;
            this.lastScrollTime = performance.now();

            this.updateVisibleItems();

            // Check if we need to load pages based on viewport position
            this.checkAndLoadRequiredPages();

            this.scrollRAF = null;
        });
    }

    /**
     * Handle resize events
     */
    onResize() {
        this.updateDimensions();
        this.updateVisibleItems();
    }

    /**
     * Handle search input
     */
    async handleSearch() {
        if (this.searchQuery.trim() === '') {
            // Exiting search mode
            this.isSearchMode = false;
            this.filteredItems = [...this.items];

            // Restore original total count
            if (this.originalTotalCount > 0) {
                this.totalCount = this.originalTotalCount;
                this.updateTotalHeight();
            }
        } else {
            // Entering search mode
            if (!this.isSearchMode) {
                this.isSearchMode = true;
                this.originalTotalCount = this.totalCount; // Save original count
            }

            this.showLoading(true);

            try {
                const searchResults = await this.queryManager.searchProducts(
                    this.searchQuery,
                    this.currentFilter
                );
                this.filteredItems = searchResults;

                // Update total count for search results
                this.totalCount = this.filteredItems.length;
                this.updateTotalHeight();
            } catch (error) {
                console.error('Error searching products:', error);
                this.showError('Search failed');
            } finally {
                this.showLoading(false);
            }
        }

        this.updateDisplay();
        this.updateInfo();
    }

    /**
     * Apply sorting and filtering
     */
    async applySortAndFilter() {
        this.showLoading(true);

        try {
            // Reset data and reload with new filters
            this.items = [];
            this.filteredItems = [];
            this.hasMore = true;
            this.nextCursor = null;

            // Update total count for new filter
            this.totalCount = await this.queryManager.countProducts(this.currentFilter);
            this.updateTotalHeight();

            const result = await this.queryManager.getProductsPaginated(
                this.currentFilter,
                this.pageSize,
                null,
                this.currentSort
            );

            this.items = result.data;
            this.filteredItems = this._canSortByIndex() ? [...this.items] : this.sortItems([...this.items]);
            this.hasMore = result.hasMore;
            this.nextCursor = result.nextCursor;
            this.windowBaseIndex = 0; // Reset window base index for new filter

            this.updateDisplay();
            this.updateInfo();
        } catch (error) {
            console.error('Error applying sort and filter:', error);
            this.showError('Failed to apply filters');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Check viewport position and load required pages
     */
    async checkAndLoadRequiredPages() {
        if (this.totalCount === 0) return;

        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(this.containerHeight / this.itemHeight) + this.bufferSize,
            this.totalCount
        );

        // Calculate which pages we need
        const startPage = Math.floor(startIndex / this.pageSize);
        const endPage = Math.floor(endIndex / this.pageSize);

        // Load current page and prefetch neighbors
        const pagesToLoad = [];
        for (let page = Math.max(0, startPage - 1); page <= Math.min(endPage + 1, Math.ceil(this.totalCount / this.pageSize) - 1); page++) {
            if (!this.loadedPages.has(page) && !this.loadingPages.has(page)) {
                pagesToLoad.push(page);
            }
        }

        // Load pages in parallel
        const loadPromises = pagesToLoad.map(page => this.loadPage(page));
        if (loadPromises.length > 0) {
            await Promise.allSettled(loadPromises);
            this.updateFilteredItemsFromPages();
        }
    }

    /**
     * Load a specific page of data
     */
    async loadPage(pageNumber) {
        if (this.loadingPages.has(pageNumber) || this.loadedPages.has(pageNumber)) {
            return;
        }

        this.loadingPages.add(pageNumber);

        try {
            // Get anchor key for this page
            const anchorKey = this.pageAnchors.get(pageNumber);

            const result = await this.queryManager.getProductsPaginated(
                this.currentFilter,
                this.pageSize,
                anchorKey,
                this.currentSort
            );

            // Store page data
            this.loadedPages.set(pageNumber, result.data);

            // Store anchor for next page
            if (result.nextCursor && pageNumber + 1 < Math.ceil(this.totalCount / this.pageSize)) {
                this.pageAnchors.set(pageNumber + 1, result.nextCursor);
            }

            console.log(`📄 Loaded page ${pageNumber} with ${result.data.length} items`);
        } catch (error) {
            console.error(`Error loading page ${pageNumber}:`, error);
        } finally {
            this.loadingPages.delete(pageNumber);
        }
    }

    /**
     * Update filteredItems from loaded pages
     */
    updateFilteredItemsFromPages() {
        if (this.searchQuery.trim() !== '') {
            // Don't update from pages during search
            return;
        }

        // Create a sparse array with placeholders
        const newFilteredItems = new Array(this.totalCount);

        // Fill in data from loaded pages
        for (const [pageNumber, pageData] of this.loadedPages) {
            const startIndex = pageNumber * this.pageSize;
            for (let i = 0; i < pageData.length; i++) {
                newFilteredItems[startIndex + i] = pageData[i];
            }
        }

        this.filteredItems = newFilteredItems;
        this.updateVisibleItems();
    }

    /**
     * Legacy method for backward compatibility
     */
    async loadMoreData() {
        // Fallback to append-only loading for search results
        if (this.searchQuery.trim() !== '') {
            return this.loadMoreSearchResults();
        }

        // For normal browsing, use windowed pagination
        await this.checkAndLoadRequiredPages();
    }

    /**
     * Load more search results (append-only)
     */
    async loadMoreSearchResults() {
        if (this.isLoading || !this.hasMore) return;

        this.isLoading = true;
        this.showLoading(true);

        try {
            const result = await this.queryManager.getProductsPaginated(
                this.currentFilter,
                this.pageSize,
                this.nextCursor,
                this.currentSort
            );

            this.items.push(...result.data);
            this.filteredItems.push(...(this._canSortByIndex() ? result.data : this.sortItems(result.data)));

            this.hasMore = result.hasMore;
            this.nextCursor = result.nextCursor;

            this.updateVisibleItems();
            this.updateInfo();
        } catch (error) {
            console.error('Error loading more search results:', error);
            this.showError('Failed to load more products');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * Update total height based on total count
     */
    updateTotalHeight() {
        if (this.totalCount > 0) {
            this.totalHeight = this.totalCount * this.itemHeight;
            this.scrollContainer.style.height = `${this.totalHeight}px`;
        }
    }

    /**
     * Update visible items based on scroll position
     */
    updateVisibleItems() {
        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + this.visibleCount,
            this.totalCount || this.filteredItems.length
        );

        this.startIndex = Math.max(0, startIndex - this.bufferSize);
        this.endIndex = Math.min(endIndex + this.bufferSize, this.totalCount || this.filteredItems.length);

        this.renderVisibleItems();
        this.updatePerformanceStats();
    }

    /**
     * Render visible items using DOM recycling and windowing
     */
    renderVisibleItems() {
        // Implement windowing - only keep data for current memory window
        this.updateMemoryWindow();

        // Get currently visible range
        const newVisibleRange = new Set();
        for (let i = this.startIndex; i < this.endIndex; i++) {
            newVisibleRange.add(i);
        }

        // Remove elements that are no longer visible
        for (const [index, element] of this.activeElements.entries()) {
            if (!newVisibleRange.has(index)) {
                this.recycleElement(element);
                this.activeElements.delete(index);
            }
        }

        // Add or update elements for visible items
        for (let i = this.startIndex; i < this.endIndex; i++) {
            let item;

            // Handle sparse arrays (windowed pagination) vs dense arrays (search results)
            if (this.searchQuery.trim() === '' && this.filteredItems.length === this.totalCount) {
                // Sparse array from windowed pagination
                item = this.filteredItems[i];
            } else {
                // Dense array from search or legacy loading
                const localIndex = i - this.windowBaseIndex;
                if (localIndex < 0 || localIndex >= this.filteredItems.length) {
                    continue;
                }
                item = this.filteredItems[localIndex];
            }

            if (!item) {
                // Show placeholder for unloaded items
                this.renderPlaceholder(i);
                continue;
            }

            let element = this.activeElements.get(i);
            if (!element) {
                // Get element from recycle pool or create new one
                element = this.getRecycledElement();
                this.activeElements.set(i, element);
                this.itemsContainer.appendChild(element);
            }

            // Update element content
            this.updateItemElement(element, item, i);
        }

        // Update container positioning using global indices
        this.itemsContainer.style.transform = `translateY(${this.startIndex * this.itemHeight}px)`;
    }

    /**
     * Update memory window to limit data in memory
     */
    updateMemoryWindow() {
        const currentPage = Math.floor(this.startIndex / this.pageSize);
        const windowStart = Math.max(0, currentPage - this.maxMemoryPages);
        const windowEnd = Math.min(
            Math.ceil(this.totalCount / this.pageSize),
            currentPage + this.maxMemoryPages
        );

        // If window changed significantly, clean up old data
        if (Math.abs(windowStart - this.memoryWindow.start) > this.maxMemoryPages ||
            Math.abs(windowEnd - this.memoryWindow.end) > this.maxMemoryPages) {

            this.cleanupMemoryWindow(windowStart, windowEnd);
            this.memoryWindow = { start: windowStart, end: windowEnd };
        }
    }

    /**
     * Clean up data outside memory window
     */
    cleanupMemoryWindow(newStart, newEnd) {
        const startIndex = newStart * this.pageSize;
        const endIndex = newEnd * this.pageSize;

        // Calculate how many items will be dropped from the beginning
        const currentWindowStart = this.memoryWindow.start * this.pageSize;
        const itemsDroppedFromStart = Math.max(0, startIndex - currentWindowStart);

        // Update windowBaseIndex to track global offset
        this.windowBaseIndex += itemsDroppedFromStart;

        // Remove items outside the new window
        this.filteredItems = this.filteredItems.filter((item, index) => {
            const globalIndex = index + currentWindowStart;
            return globalIndex >= startIndex && globalIndex < endIndex;
        });

        // Trigger garbage collection hint
        if (window.gc) {
            setTimeout(() => window.gc(), 100);
        }
    }

    /**
     * Get a recycled DOM element or create a new one
     */
    getRecycledElement() {
        if (this.recyclePool.length > 0) {
            return this.recyclePool.pop();
        }

        // Create new element if pool is empty
        return this.createEmptyItemElement();
    }

    /**
     * Return element to recycle pool
     */
    recycleElement(element) {
        if (this.recyclePool.length < this.recyclePoolSize) {
            // Clear content but keep structure
            this.clearElementContent(element);
            this.recyclePool.push(element);
        } else {
            // Pool is full, remove element
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }
    }

    /**
     * Create empty DOM element structure for recycling
     */
    createEmptyItemElement() {
        const element = document.createElement('div');
        element.className = 'virtual-list-item';
        element.style.height = `${this.itemHeight}px`;

        // Create reusable structure
        const itemContent = document.createElement('div');
        itemContent.className = 'item-content';

        const itemImage = document.createElement('div');
        itemImage.className = 'item-image';
        const img = document.createElement('img');
        img.loading = 'lazy';
        itemImage.appendChild(img);

        const itemDetails = document.createElement('div');
        itemDetails.className = 'item-details';

        const titleDiv = document.createElement('div');
        titleDiv.className = 'item-title';

        const asinDiv = document.createElement('div');
        asinDiv.className = 'item-asin';

        const descDiv = document.createElement('div');
        descDiv.className = 'item-description';

        const metaDiv = document.createElement('div');
        metaDiv.className = 'item-meta';

        const marketplaceSpan = document.createElement('span');
        marketplaceSpan.className = 'marketplace';

        const priceSpan = document.createElement('span');
        priceSpan.className = 'price';

        const updatedSpan = document.createElement('span');
        updatedSpan.className = 'updated';

        metaDiv.appendChild(marketplaceSpan);
        metaDiv.appendChild(priceSpan);
        metaDiv.appendChild(updatedSpan);

        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'item-actions';

        const viewBtn = document.createElement('button');
        viewBtn.className = 'btn-view';
        viewBtn.textContent = 'View';

        const editBtn = document.createElement('button');
        editBtn.className = 'btn-edit';
        editBtn.textContent = 'Edit';

        actionsDiv.appendChild(viewBtn);
        actionsDiv.appendChild(editBtn);

        itemDetails.appendChild(titleDiv);
        itemDetails.appendChild(asinDiv);
        itemDetails.appendChild(descDiv);
        itemDetails.appendChild(metaDiv);

        itemContent.appendChild(itemImage);
        itemContent.appendChild(itemDetails);
        itemContent.appendChild(actionsDiv);

        element.appendChild(itemContent);

        return element;
    }

    /**
     * Update existing DOM element with new item data
     */
    updateItemElement(element, item, index) {
        element.dataset.index = index;

        // Update image
        const img = element.querySelector('.item-image img');
        img.src = this.escapeHtml(item.imageUrl || '/placeholder.jpg');
        img.alt = this.escapeHtml(item.title || 'Untitled');

        // Update title with search highlighting
        const titleDiv = element.querySelector('.item-title');
        this.setHighlightedContent(titleDiv, item.title || 'Untitled');

        // Update ASIN
        const asinDiv = element.querySelector('.item-asin');
        asinDiv.textContent = `ASIN: ${item.asin || 'Unknown'}`;

        // Update description with search highlighting
        const descDiv = element.querySelector('.item-description');
        const truncatedDesc = (item.description || '').substring(0, 100) + '...';
        this.setHighlightedContent(descDiv, truncatedDesc);

        // Update meta information
        const marketplaceSpan = element.querySelector('.marketplace');
        marketplaceSpan.textContent = item.marketplace || 'Unknown';

        const priceSpan = element.querySelector('.price');
        priceSpan.textContent = `$${(item.price || 0).toFixed(2)}`;

        const updatedSpan = element.querySelector('.updated');
        updatedSpan.textContent = this.formatDate(item.lastUpdated);

        // Update button event handlers
        const viewBtn = element.querySelector('.btn-view');
        const editBtn = element.querySelector('.btn-edit');

        // Remove old event listeners by cloning
        const newViewBtn = viewBtn.cloneNode(true);
        const newEditBtn = editBtn.cloneNode(true);

        viewBtn.parentNode.replaceChild(newViewBtn, viewBtn);
        editBtn.parentNode.replaceChild(newEditBtn, editBtn);

        // Add new event listeners
        newViewBtn.dataset.asin = item.asin || '';
        newEditBtn.dataset.asin = item.asin || '';

        newViewBtn.addEventListener('click', () => this.onItemView(item));
        newEditBtn.addEventListener('click', () => this.onItemEdit(item));
    }

    /**
     * Render placeholder for unloaded items
     */
    renderPlaceholder(index) {
        let element = this.activeElements.get(index);
        if (!element) {
            element = this.getRecycledElement();
            this.activeElements.set(index, element);
            this.itemsContainer.appendChild(element);
        }

        // Clear content and show loading state
        this.clearElementContent(element);

        // Add placeholder content
        element.classList.add('placeholder-item');
        const titleDiv = element.querySelector('.item-title');
        if (titleDiv) {
            titleDiv.innerHTML = '<div class="placeholder-text">Loading...</div>';
        }

        const descDiv = element.querySelector('.item-description');
        if (descDiv) {
            descDiv.innerHTML = '<div class="placeholder-text">Loading product details...</div>';
        }
    }

    /**
     * Clear element content for recycling
     */
    clearElementContent(element) {
        // Clear dynamic content but keep structure
        const img = element.querySelector('.item-image img');
        if (img) {
            img.src = '';
            img.alt = '';
        }

        const titleDiv = element.querySelector('.item-title');
        if (titleDiv) titleDiv.innerHTML = '';

        const asinDiv = element.querySelector('.item-asin');
        if (asinDiv) asinDiv.textContent = '';

        const descDiv = element.querySelector('.item-description');
        if (descDiv) descDiv.innerHTML = '';

        const marketplaceSpan = element.querySelector('.marketplace');
        if (marketplaceSpan) marketplaceSpan.textContent = '';

        const priceSpan = element.querySelector('.price');
        if (priceSpan) priceSpan.textContent = '';

        const updatedSpan = element.querySelector('.updated');
        if (updatedSpan) updatedSpan.textContent = '';

        // Clear dataset
        delete element.dataset.index;

        const viewBtn = element.querySelector('.btn-view');
        const editBtn = element.querySelector('.btn-edit');
        if (viewBtn) delete viewBtn.dataset.asin;
        if (editBtn) delete editBtn.dataset.asin;
    }

    /**
     * Create DOM element for a single item (legacy method for compatibility)
     */
    createItemElement(item, index) {
        const element = document.createElement('div');
        element.className = 'virtual-list-item';
        element.style.height = `${this.itemHeight}px`;
        element.dataset.index = index;

        // Create structure using DOM methods to prevent XSS
        const itemContent = document.createElement('div');
        itemContent.className = 'item-content';

        // Image section
        const itemImage = document.createElement('div');
        itemImage.className = 'item-image';
        const img = document.createElement('img');
        img.src = this.escapeHtml(item.imageUrl || '/placeholder.jpg');
        img.alt = this.escapeHtml(item.title || 'Untitled');
        img.loading = 'lazy';
        itemImage.appendChild(img);

        // Details section
        const itemDetails = document.createElement('div');
        itemDetails.className = 'item-details';

        // Title with search highlighting
        const titleDiv = document.createElement('div');
        titleDiv.className = 'item-title';
        this.setHighlightedContent(titleDiv, item.title || 'Untitled');

        // ASIN
        const asinDiv = document.createElement('div');
        asinDiv.className = 'item-asin';
        asinDiv.textContent = `ASIN: ${item.asin || 'Unknown'}`;

        // Description with search highlighting
        const descDiv = document.createElement('div');
        descDiv.className = 'item-description';
        const truncatedDesc = (item.description || '').substring(0, 100) + '...';
        this.setHighlightedContent(descDiv, truncatedDesc);

        // Meta information
        const metaDiv = document.createElement('div');
        metaDiv.className = 'item-meta';

        const marketplaceSpan = document.createElement('span');
        marketplaceSpan.className = 'marketplace';
        marketplaceSpan.textContent = item.marketplace || 'Unknown';

        const priceSpan = document.createElement('span');
        priceSpan.className = 'price';
        priceSpan.textContent = `$${(item.price || 0).toFixed(2)}`;

        const updatedSpan = document.createElement('span');
        updatedSpan.className = 'updated';
        updatedSpan.textContent = this.formatDate(item.lastUpdated);

        metaDiv.appendChild(marketplaceSpan);
        metaDiv.appendChild(priceSpan);
        metaDiv.appendChild(updatedSpan);

        // Actions section
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'item-actions';

        const viewBtn = document.createElement('button');
        viewBtn.className = 'btn-view';
        viewBtn.dataset.asin = item.asin || '';
        viewBtn.textContent = 'View';
        viewBtn.addEventListener('click', () => this.onItemView(item));

        const editBtn = document.createElement('button');
        editBtn.className = 'btn-edit';
        editBtn.dataset.asin = item.asin || '';
        editBtn.textContent = 'Edit';
        editBtn.addEventListener('click', () => this.onItemEdit(item));

        actionsDiv.appendChild(viewBtn);
        actionsDiv.appendChild(editBtn);

        // Assemble the structure
        itemDetails.appendChild(titleDiv);
        itemDetails.appendChild(asinDiv);
        itemDetails.appendChild(descDiv);
        itemDetails.appendChild(metaDiv);

        itemContent.appendChild(itemImage);
        itemContent.appendChild(itemDetails);
        itemContent.appendChild(actionsDiv);

        element.appendChild(itemContent);

        return element;
    }

    /**
     * Update scroll container height using total count for proper virtualization
     */
    updateScrollContainer() {
        // Use totalCount for proper virtualization, not loaded items
        if (this.totalCount > 0) {
            this.totalHeight = this.totalCount * this.itemHeight;
            this.scrollContainer.style.height = `${this.totalHeight}px`;
        }
    }

    /**
     * Update display after data changes
     */
    updateDisplay() {
        this.updateScrollContainer();
        this.updateVisibleItems();
        // Scroll reset removed to prevent infinite scroll jumping to top
    }

    /**
     * Check if current sort can be handled by database indexes
     */
    _canSortByIndex() {
        const indexedSortFields = ['title', 'price', 'lastUpdated', 'status', 'marketplace', 'category'];
        return indexedSortFields.includes(this.currentSort.field);
    }

    /**
     * Sort items based on current sort settings (client-side fallback)
     */
    sortItems(items) {
        if (this._canSortByIndex()) {
            console.warn(`⚠️ Client-side sorting for indexed field '${this.currentSort.field}'. Consider using DB-level sorting.`);
        }

        return items.sort((a, b) => {
            const aVal = a[this.currentSort.field] || '';
            const bVal = b[this.currentSort.field] || '';

            let comparison = 0;
            if (typeof aVal === 'string') {
                comparison = aVal.localeCompare(bVal);
            } else {
                comparison = aVal - bVal;
            }

            return this.currentSort.direction === 'desc' ? -comparison : comparison;
        });
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Escape regex special characters
     */
    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * Set content with safe search term highlighting
     */
    setHighlightedContent(element, text) {
        if (!this.searchQuery.trim()) {
            element.textContent = text;
            return;
        }

        const terms = this.searchQuery.trim().split(' ').filter(term => term.length > 0);
        let currentText = text;
        const fragments = [];

        // Find all matches and their positions
        const matches = [];
        terms.forEach(term => {
            const escapedTerm = this.escapeRegExp(term);
            const regex = new RegExp(`(${escapedTerm})`, 'gi');
            let match;
            while ((match = regex.exec(currentText)) !== null) {
                matches.push({
                    start: match.index,
                    end: match.index + match[0].length,
                    text: match[0]
                });
            }
        });

        // Sort matches by position and merge overlapping ones
        matches.sort((a, b) => a.start - b.start);
        const mergedMatches = [];
        for (const match of matches) {
            const last = mergedMatches[mergedMatches.length - 1];
            if (last && match.start <= last.end) {
                last.end = Math.max(last.end, match.end);
            } else {
                mergedMatches.push(match);
            }
        }

        // Build the highlighted content
        element.innerHTML = '';
        let lastIndex = 0;

        for (const match of mergedMatches) {
            // Add text before match
            if (match.start > lastIndex) {
                const textNode = document.createTextNode(currentText.substring(lastIndex, match.start));
                element.appendChild(textNode);
            }

            // Add highlighted match
            const mark = document.createElement('mark');
            mark.textContent = currentText.substring(match.start, match.end);
            element.appendChild(mark);

            lastIndex = match.end;
        }

        // Add remaining text
        if (lastIndex < currentText.length) {
            const textNode = document.createTextNode(currentText.substring(lastIndex));
            element.appendChild(textNode);
        }
    }

    /**
     * Format date for display
     */
    formatDate(date) {
        if (!date) return 'Unknown';
        return new Date(date).toLocaleDateString();
    }

    /**
     * Update info display
     */
    updateInfo() {
        const total = this.totalCount || 0;
        const showing = Math.min(this.endIndex - this.startIndex, total);

        if (this.isSearchMode) {
            // Show search results count and original total
            const originalTotal = this.originalTotalCount || 0;
            this.resultsCount.textContent = `${total.toLocaleString()} of ${originalTotal.toLocaleString()} products found`;
            this.itemsInfo.textContent = `Showing ${showing} of ${total.toLocaleString()} search results`;
        } else {
            // Show normal counts
            this.resultsCount.textContent = `${total.toLocaleString()} products found`;
            this.itemsInfo.textContent = `Showing ${showing} of ${total.toLocaleString()} products`;
        }
    }

    /**
     * Update performance statistics
     */
    updatePerformanceStats() {
        const renderedItems = this.endIndex - this.startIndex;
        const memoryUsage = this.estimateMemoryUsage();
        
        this.performanceStats.innerHTML = `
            Rendered: ${renderedItems} | 
            Memory: ~${memoryUsage}MB | 
            FPS: ${this.getFPS()}
        `;
    }

    /**
     * Show/hide loading indicator
     */
    showLoading(show) {
        this.loadingIndicator.style.display = show ? 'flex' : 'none';
    }

    /**
     * Show error message
     */
    showError(message) {
        // Implementation for error display
        console.error(message);
    }

    /**
     * Handle item view action
     */
    onItemView(item) {
        // Emit custom event for item view
        this.container.dispatchEvent(new CustomEvent('itemView', { detail: item }));
    }

    /**
     * Handle item edit action
     */
    onItemEdit(item) {
        // Emit custom event for item edit
        this.container.dispatchEvent(new CustomEvent('itemEdit', { detail: item }));
    }

    /**
     * Estimate memory usage
     */
    estimateMemoryUsage() {
        const itemSize = 2; // Estimated KB per rendered item
        const renderedItems = this.endIndex - this.startIndex;
        return ((renderedItems * itemSize) / 1024).toFixed(1);
    }

    /**
     * Get current FPS (simplified)
     */
    getFPS() {
        return '60'; // Placeholder - would need actual FPS monitoring
    }

    /**
     * Debounce utility function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Destroy the component and clean up
     */
    destroy() {
        // Cancel any pending RAF
        if (this.scrollRAF) {
            cancelAnimationFrame(this.scrollRAF);
            this.scrollRAF = null;
        }

        // Remove event listeners
        this.viewport.removeEventListener('scroll', this.onScroll);
        window.removeEventListener('resize', this.onResize);

        // Clean up DOM attachment for MemoryMonitor
        if (this.container) {
            this.container.classList.remove('virtual-list');
            delete this.container.virtualListInstance;
        }

        // Clear all data structures
        this.items = [];
        this.filteredItems = [];
        this.visibleItems = [];
        this.activeElements.clear();
        this.recyclePool = [];

        // Clear container
        this.container.innerHTML = '';

        // Trigger garbage collection hint
        if (window.gc) {
            setTimeout(() => window.gc(), 100);
        }
    }

    /**
     * Clear internal caches for memory optimization
     * Called by MemoryMonitor when memory pressure is detected
     */
    clearCache() {
        console.log('🧹 VirtualProductList: Clearing caches for memory optimization');

        // Clear page caches beyond current view
        const currentPage = Math.floor(this.scrollTop / (this.itemHeight * this.pageSize));
        const keepPages = new Set();

        // Keep current page and adjacent pages
        for (let i = Math.max(0, currentPage - 1); i <= currentPage + 1; i++) {
            keepPages.add(i);
        }

        // Clear non-essential pages
        for (const [pageNum] of this.loadedPages) {
            if (!keepPages.has(pageNum)) {
                this.loadedPages.delete(pageNum);
            }
        }

        // Clear recycled DOM elements beyond minimum
        const minPoolSize = Math.min(10, this.recyclePoolSize * 0.3);
        if (this.recyclePool.length > minPoolSize) {
            this.recyclePool.splice(minPoolSize);
        }

        // Clear search cache if it exists
        if (this.searchCache) {
            this.searchCache.clear();
        }

        console.log(`🧹 VirtualProductList: Cache cleared, kept ${keepPages.size} pages, ${this.recyclePool.length} pooled elements`);
    }

    /**
     * Optimize buffer size for memory efficiency
     * Called by MemoryMonitor during memory pressure
     */
    optimizeBufferSize() {
        const originalBufferSize = this.bufferSize;

        // Reduce buffer size but maintain minimum for smooth scrolling
        this.bufferSize = Math.max(3, Math.floor(this.bufferSize * 0.7));

        if (this.bufferSize !== originalBufferSize) {
            console.log(`📊 VirtualProductList: Buffer size optimized from ${originalBufferSize} to ${this.bufferSize}`);

            // Trigger re-render with new buffer size
            this.updateVisibleRange();
        }
    }

    /**
     * Get memory usage statistics for monitoring
     */
    getMemoryStats() {
        return {
            loadedPages: this.loadedPages.size,
            totalItems: this.totalCount,
            visibleItems: this.visibleItems.length,
            recyclePoolSize: this.recyclePool.length,
            bufferSize: this.bufferSize,
            activeElements: this.activeElements.size
        };
    }
}

// Make VirtualProductList available globally
window.VirtualProductList = VirtualProductList;
