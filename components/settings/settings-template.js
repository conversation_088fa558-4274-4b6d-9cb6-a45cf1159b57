/**
 * Settings Component HTML Templates
 * Modular HTML templates for the settings page
 */

(function() {
  'use strict';

  // Settings page template
  const settingsPageTemplate = `
    <div class="settings-page">
        <div class="settings-header">
            <h1>Settings</h1>
            <div class="settings-actions">
                <button class="btn-export">Export Settings</button>
                <button class="btn-import">Import Settings</button>
                <button class="btn-reset">Reset to Defaults</button>
                <button class="btn-save" disabled>Save Changes</button>
            </div>
        </div>
        
        <div class="settings-content">
            <div class="settings-tabs">
                <button class="settings-tab active" data-tab="general">General</button>
                <button class="settings-tab" data-tab="performance">Performance</button>
                <button class="settings-tab" data-tab="data">Data Management</button>
                <button class="settings-tab" data-tab="ui">User Interface</button>
                <button class="settings-tab" data-tab="backup">Backup & Sync</button>
                <button class="settings-tab" data-tab="advanced">Advanced</button>
            </div>
            
            <div class="settings-panel" id="settings-panel">
                <!-- Tab content will be dynamically loaded here -->
            </div>
        </div>
    </div>
  `;

  // General tab template
  const generalTabTemplate = `
    <div class="settings-section">
        <h3>General Settings</h3>
        <div class="settings-group">
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Theme</h4>
                    <p>Choose your preferred color theme</p>
                </div>
                <div class="setting-control">
                    <select id="theme-select">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="auto">Auto (System)</option>
                    </select>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Language</h4>
                    <p>Select your preferred language</p>
                </div>
                <div class="setting-control">
                    <select id="language-select">
                        <option value="en">English</option>
                        <option value="es">Español</option>
                        <option value="fr">Français</option>
                        <option value="de">Deutsch</option>
                    </select>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Timezone</h4>
                    <p>Your local timezone for date/time display</p>
                </div>
                <div class="setting-control">
                    <select id="timezone-select">
                        <option value="auto">Auto-detect</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="UTC">UTC</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
  `;

  // Performance tab template
  const performanceTabTemplate = `
    <div class="settings-section">
        <h3>Performance Settings</h3>
        <div class="settings-group">
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Enable Performance Monitoring</h4>
                    <p>Monitor memory usage and performance metrics</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="enable-monitoring" />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Memory Monitoring Interval</h4>
                    <p>How often to check memory usage (seconds)</p>
                </div>
                <div class="setting-control">
                    <input type="number" id="monitoring-interval" min="5" max="300" value="30" />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Virtual List Page Size</h4>
                    <p>Number of items to render in virtual lists</p>
                </div>
                <div class="setting-control">
                    <input type="number" id="virtual-page-size" min="50" max="1000" value="200" />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Cache Size Limit</h4>
                    <p>Maximum number of cached queries</p>
                </div>
                <div class="setting-control">
                    <input type="number" id="cache-size-limit" min="50" max="1000" value="100" />
                </div>
            </div>
        </div>
    </div>
  `;

  // Data tab template
  const dataTabTemplate = `
    <div class="settings-section">
        <h3>Data Management</h3>
        <div class="settings-group">
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Data Sync Frequency</h4>
                    <p>How often to sync data with Amazon (minutes)</p>
                </div>
                <div class="setting-control">
                    <select id="sync-frequency">
                        <option value="5">5 minutes</option>
                        <option value="15">15 minutes</option>
                        <option value="30">30 minutes</option>
                        <option value="60">1 hour</option>
                        <option value="240">4 hours</option>
                    </select>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Data Retention Period</h4>
                    <p>How long to keep historical data (days)</p>
                </div>
                <div class="setting-control">
                    <select id="retention-period">
                        <option value="30">30 days</option>
                        <option value="90">90 days</option>
                        <option value="365">1 year</option>
                        <option value="1095">3 years</option>
                        <option value="-1">Forever</option>
                    </select>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Auto-cleanup Old Data</h4>
                    <p>Automatically remove old data based on retention period</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="auto-cleanup" checked />
                </div>
            </div>
        </div>
    </div>
  `;

  // UI tab template
  const uiTabTemplate = `
    <div class="settings-section">
        <h3>User Interface</h3>
        <div class="settings-group">
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Compact Mode</h4>
                    <p>Use smaller spacing and fonts to fit more content</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="compact-mode" />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Preferred List View</h4>
                    <p>Default view for product lists</p>
                </div>
                <div class="setting-control">
                    <select id="preferred-list-view">
                        <option value="legacy">Legacy List</option>
                        <option value="virtual">Virtual List</option>
                    </select>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Show Tooltips</h4>
                    <p>Display helpful tooltips on hover</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="show-tooltips" checked />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Animation Speed</h4>
                    <p>Speed of UI animations and transitions</p>
                </div>
                <div class="setting-control">
                    <select id="animation-speed">
                        <option value="slow">Slow</option>
                        <option value="normal">Normal</option>
                        <option value="fast">Fast</option>
                        <option value="none">No animations</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
  `;

  // Backup tab template
  const backupTabTemplate = `
    <div class="settings-section">
        <h3>Backup & Sync</h3>
        <div class="settings-group">
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Auto Backup</h4>
                    <p>Automatically backup your data</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="auto-backup" />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Backup Frequency</h4>
                    <p>How often to create automatic backups</p>
                </div>
                <div class="setting-control">
                    <select id="backup-frequency">
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                    </select>
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Backup Retention</h4>
                    <p>Number of backup files to keep</p>
                </div>
                <div class="setting-control">
                    <input type="number" id="backup-retention" min="1" max="50" value="5" />
                </div>
            </div>
        </div>
    </div>
  `;

  // Advanced tab template
  const advancedTabTemplate = `
    <div class="settings-section">
        <h3>Advanced Settings</h3>
        <div class="settings-group">
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Debug Mode</h4>
                    <p>Enable detailed logging for troubleshooting</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="debug-mode" />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Worker Threads</h4>
                    <p>Use web workers for heavy computations</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="use-workers" checked />
                </div>
            </div>
            
            <div class="setting-item">
                <div class="setting-label">
                    <h4>Experimental Features</h4>
                    <p>Enable experimental features (may be unstable)</p>
                </div>
                <div class="setting-control">
                    <input type="checkbox" id="experimental-features" />
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-section">
        <h3>System Diagnostics</h3>
        <div class="diagnostics-section">
            <div class="diagnostics-grid" id="diagnostics-grid">
                <!-- Diagnostics will be populated dynamically -->
            </div>
        </div>
    </div>
  `;

  // Make templates globally available
  window.settingsPageTemplate = settingsPageTemplate;
  window.generalTabTemplate = generalTabTemplate;
  window.performanceTabTemplate = performanceTabTemplate;
  window.dataTabTemplate = dataTabTemplate;
  window.uiTabTemplate = uiTabTemplate;
  window.backupTabTemplate = backupTabTemplate;
  window.advancedTabTemplate = advancedTabTemplate;
})();
