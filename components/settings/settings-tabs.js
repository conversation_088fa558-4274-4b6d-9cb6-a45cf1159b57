/**
 * Settings Component Tab Handlers
 * Modular tab handling logic for the settings page
 */

(function() {
  'use strict';

  // Get templates from global scope (loaded by settings-template.js)
  const generalTabTemplate = window.generalTabTemplate || '<div>General tab template not loaded</div>';
  const performanceTabTemplate = window.performanceTabTemplate || '<div>Performance tab template not loaded</div>';
  const dataTabTemplate = window.dataTabTemplate || '<div>Data tab template not loaded</div>';
  const uiTabTemplate = window.uiTabTemplate || '<div>UI tab template not loaded</div>';
  const backupTabTemplate = window.backupTabTemplate || '<div>Backup tab template not loaded</div>';
  const advancedTabTemplate = window.advancedTabTemplate || '<div>Advanced tab template not loaded</div>';

  class SettingsTabManager {
    constructor(settingsComponent) {
        this.settingsComponent = settingsComponent;
        this.settingsManager = settingsComponent.settingsManager;
        this.activeTab = 'general';
    }

    /**
     * Handle tab change
     */
    handleTabChange(tabName) {
        if (this.activeTab === tabName) return;

        // Update tab buttons
        const tabs = document.querySelectorAll('.settings-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // Load tab content
        this.loadTabContent(tabName);
        this.activeTab = tabName;
    }

    /**
     * Load content for specific tab
     */
    loadTabContent(tabName) {
        const panel = document.getElementById('settings-panel');
        if (!panel) return;

        let template = '';
        switch (tabName) {
            case 'general':
                template = generalTabTemplate;
                break;
            case 'performance':
                template = performanceTabTemplate;
                break;
            case 'data':
                template = dataTabTemplate;
                break;
            case 'ui':
                template = uiTabTemplate;
                break;
            case 'backup':
                template = backupTabTemplate;
                break;
            case 'advanced':
                template = advancedTabTemplate;
                break;
            default:
                template = '<div class="settings-error">Tab not found</div>';
        }

        panel.innerHTML = template;
        this.loadTabSettings(tabName);
        this.attachTabEventListeners(tabName);
    }

    /**
     * Load current settings for the active tab
     */
    loadTabSettings(tabName) {
        switch (tabName) {
            case 'general':
                this.loadGeneralSettings();
                break;
            case 'performance':
                this.loadPerformanceSettings();
                break;
            case 'data':
                this.loadDataSettings();
                break;
            case 'ui':
                this.loadUISettings();
                break;
            case 'backup':
                this.loadBackupSettings();
                break;
            case 'advanced':
                this.loadAdvancedSettings();
                break;
        }
    }

    /**
     * Attach event listeners for the active tab
     */
    attachTabEventListeners(tabName) {
        switch (tabName) {
            case 'general':
                this.attachGeneralListeners();
                break;
            case 'performance':
                this.attachPerformanceListeners();
                break;
            case 'data':
                this.attachDataListeners();
                break;
            case 'ui':
                this.attachUIListeners();
                break;
            case 'backup':
                this.attachBackupListeners();
                break;
            case 'advanced':
                this.attachAdvancedListeners();
                break;
        }
    }

    // General Settings
    loadGeneralSettings() {
        const themeSelect = document.getElementById('theme-select');
        const languageSelect = document.getElementById('language-select');
        const timezoneSelect = document.getElementById('timezone-select');

        if (themeSelect) themeSelect.value = this.settingsManager.get('ui.theme') || 'light';
        if (languageSelect) languageSelect.value = this.settingsManager.get('general.language') || 'en';
        if (timezoneSelect) timezoneSelect.value = this.settingsManager.get('general.timezone') || 'auto';
    }

    attachGeneralListeners() {
        const themeSelect = document.getElementById('theme-select');
        const languageSelect = document.getElementById('language-select');
        const timezoneSelect = document.getElementById('timezone-select');

        if (themeSelect) {
            themeSelect.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('ui.theme', e.target.value);
            });
        }

        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('general.language', e.target.value);
            });
        }

        if (timezoneSelect) {
            timezoneSelect.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('general.timezone', e.target.value);
            });
        }
    }

    // Performance Settings
    loadPerformanceSettings() {
        const enableMonitoring = document.getElementById('enable-monitoring');
        const monitoringInterval = document.getElementById('monitoring-interval');
        const virtualPageSize = document.getElementById('virtual-page-size');
        const cacheSizeLimit = document.getElementById('cache-size-limit');

        if (enableMonitoring) enableMonitoring.checked = this.settingsManager.get('performance.enableMonitoring') !== false;
        if (monitoringInterval) monitoringInterval.value = this.settingsManager.get('performance.monitoringInterval') || 30;
        if (virtualPageSize) virtualPageSize.value = this.settingsManager.get('virtualList.pageSize') || 200;
        if (cacheSizeLimit) cacheSizeLimit.value = this.settingsManager.get('queryCache.maxSize') || 100;
    }

    attachPerformanceListeners() {
        const enableMonitoring = document.getElementById('enable-monitoring');
        const monitoringInterval = document.getElementById('monitoring-interval');
        const virtualPageSize = document.getElementById('virtual-page-size');
        const cacheSizeLimit = document.getElementById('cache-size-limit');

        if (enableMonitoring) {
            enableMonitoring.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('performance.enableMonitoring', e.target.checked);
            });
        }

        if (monitoringInterval) {
            monitoringInterval.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('performance.monitoringInterval', parseInt(e.target.value));
            });
        }

        if (virtualPageSize) {
            virtualPageSize.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('virtualList.pageSize', parseInt(e.target.value));
            });
        }

        if (cacheSizeLimit) {
            cacheSizeLimit.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('queryCache.maxSize', parseInt(e.target.value));
            });
        }
    }

    // Data Settings
    loadDataSettings() {
        const syncFrequency = document.getElementById('sync-frequency');
        const retentionPeriod = document.getElementById('retention-period');
        const autoCleanup = document.getElementById('auto-cleanup');

        if (syncFrequency) syncFrequency.value = this.settingsManager.get('dataSync.fetchFrequency') || 30;
        if (retentionPeriod) retentionPeriod.value = this.settingsManager.get('data.retentionDays') || 365;
        if (autoCleanup) autoCleanup.checked = this.settingsManager.get('data.autoCleanup') !== false;
    }

    attachDataListeners() {
        const syncFrequency = document.getElementById('sync-frequency');
        const retentionPeriod = document.getElementById('retention-period');
        const autoCleanup = document.getElementById('auto-cleanup');

        if (syncFrequency) {
            syncFrequency.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('dataSync.fetchFrequency', parseInt(e.target.value) * 60000); // Convert to ms
            });
        }

        if (retentionPeriod) {
            retentionPeriod.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('data.retentionDays', parseInt(e.target.value));
            });
        }

        if (autoCleanup) {
            autoCleanup.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('data.autoCleanup', e.target.checked);
            });
        }
    }

    // UI Settings
    loadUISettings() {
        const compactMode = document.getElementById('compact-mode');
        const preferredListView = document.getElementById('preferred-list-view');
        const showTooltips = document.getElementById('show-tooltips');
        const animationSpeed = document.getElementById('animation-speed');

        if (compactMode) compactMode.checked = this.settingsManager.get('ui.compactMode') || false;
        if (preferredListView) preferredListView.value = this.settingsManager.get('ui.preferredListView') || 'legacy';
        if (showTooltips) showTooltips.checked = this.settingsManager.get('ui.showTooltips') !== false;
        if (animationSpeed) animationSpeed.value = this.settingsManager.get('ui.animationSpeed') || 'normal';
    }

    attachUIListeners() {
        const compactMode = document.getElementById('compact-mode');
        const preferredListView = document.getElementById('preferred-list-view');
        const showTooltips = document.getElementById('show-tooltips');
        const animationSpeed = document.getElementById('animation-speed');

        if (compactMode) {
            compactMode.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('ui.compactMode', e.target.checked);
            });
        }

        if (preferredListView) {
            preferredListView.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('ui.preferredListView', e.target.value);
            });
        }

        if (showTooltips) {
            showTooltips.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('ui.showTooltips', e.target.checked);
            });
        }

        if (animationSpeed) {
            animationSpeed.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('ui.animationSpeed', e.target.value);
            });
        }
    }

    // Backup Settings
    loadBackupSettings() {
        const autoBackup = document.getElementById('auto-backup');
        const backupFrequency = document.getElementById('backup-frequency');
        const backupRetention = document.getElementById('backup-retention');

        if (autoBackup) autoBackup.checked = this.settingsManager.get('backup.autoBackup') || false;
        if (backupFrequency) backupFrequency.value = this.settingsManager.get('backup.frequency') || 'weekly';
        if (backupRetention) backupRetention.value = this.settingsManager.get('backup.retention') || 5;
    }

    attachBackupListeners() {
        const autoBackup = document.getElementById('auto-backup');
        const backupFrequency = document.getElementById('backup-frequency');
        const backupRetention = document.getElementById('backup-retention');

        if (autoBackup) {
            autoBackup.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('backup.autoBackup', e.target.checked);
            });
        }

        if (backupFrequency) {
            backupFrequency.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('backup.frequency', e.target.value);
            });
        }

        if (backupRetention) {
            backupRetention.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('backup.retention', parseInt(e.target.value));
            });
        }
    }

    // Advanced Settings
    loadAdvancedSettings() {
        const debugMode = document.getElementById('debug-mode');
        const useWorkers = document.getElementById('use-workers');
        const experimentalFeatures = document.getElementById('experimental-features');

        if (debugMode) debugMode.checked = this.settingsManager.get('advanced.debugMode') || false;
        if (useWorkers) useWorkers.checked = this.settingsManager.get('advanced.useWorkers') !== false;
        if (experimentalFeatures) experimentalFeatures.checked = this.settingsManager.get('advanced.experimentalFeatures') || false;

        // Load diagnostics
        this.loadDiagnostics();
    }

    attachAdvancedListeners() {
        const debugMode = document.getElementById('debug-mode');
        const useWorkers = document.getElementById('use-workers');
        const experimentalFeatures = document.getElementById('experimental-features');

        if (debugMode) {
            debugMode.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('advanced.debugMode', e.target.checked);
            });
        }

        if (useWorkers) {
            useWorkers.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('advanced.useWorkers', e.target.checked);
            });
        }

        if (experimentalFeatures) {
            experimentalFeatures.addEventListener('change', (e) => {
                this.settingsComponent.handleSettingChange('advanced.experimentalFeatures', e.target.checked);
            });
        }
    }

    loadDiagnostics() {
        const diagnosticsGrid = document.getElementById('diagnostics-grid');
        if (!diagnosticsGrid) return;

        const diagnostics = this.settingsComponent.getDiagnostics();
        diagnosticsGrid.innerHTML = diagnostics.map(item => `
            <div class="diagnostic-item">
                <h5>${item.label}</h5>
                <div class="diagnostic-value ${item.status || ''}">${item.value}</div>
            </div>
        `).join('');
    }
  }

  // Export the class to make it globally available
  window.SettingsTabManager = SettingsTabManager;
})();
