/**
 * Settings Page Component
 * UI for managing all user preferences and massive scale configurations
 * Integrates with SettingsManager for persistence
 *
 * Modular architecture with separate files for:
 * - CSS styles (settings.css)
 * - HTML templates (settings-template.js)
 * - Tab handlers (settings-tabs.js)
 */

// Global settings component - compatible with existing script loading
(function() {
  'use strict';

  // Get templates and tab manager from global scope (loaded by other scripts)
  const settingsPageTemplate = window.settingsPageTemplate || '<div>Settings template not loaded</div>';
  const SettingsTabManager = window.SettingsTabManager || class DummyTabManager { constructor() {} };

  class SettingsComponent {
    constructor(container, settingsManager) {
        this.container = container;
        this.settingsManager = settingsManager;
        this.activeTab = 'general';
        this.unsavedChanges = false;

        // Initialize tab manager
        this.tabManager = new SettingsTabManager(this);

        // Bind methods
        this.handleTabChange = this.handleTabChange.bind(this);
        this.handleSettingChange = this.handleSettingChange.bind(this);
        this.handleSave = this.handleSave.bind(this);
        this.handleReset = this.handleReset.bind(this);
        this.handleExport = this.handleExport.bind(this);
        this.handleImport = this.handleImport.bind(this);

        this.init();
    }

    /**
     * Initialize the settings component
     */
    init() {
        this.createDOM();
        this.attachEventListeners();
        this.loadCurrentSettings();
        this.updateDiagnostics();
    }

    /**
     * Create DOM structure for settings page
     */
    createDOM() {
        // Use modular template
        this.container.innerHTML = settingsPageTemplate;



        // Get DOM references
        this.tabButtons = this.container.querySelectorAll('.settings-tab');
        this.saveButton = this.container.querySelector('.btn-save');
        this.resetButton = this.container.querySelector('.btn-reset');
        this.exportButton = this.container.querySelector('.btn-export');
        this.importButton = this.container.querySelector('.btn-import');

        // Initialize with general tab
        this.tabManager.loadTabContent('general');
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Tab navigation
        this.tabButtons.forEach(button => {
            button.addEventListener('click', this.handleTabChange);
        });

        // Setting changes
        this.settingInputs.forEach(input => {
            input.addEventListener('change', this.handleSettingChange);
            input.addEventListener('input', this.handleSettingChange);
        });

        // Action buttons
        this.saveButton.addEventListener('click', this.handleSave);
        this.resetButton.addEventListener('click', this.handleReset);
        this.exportButton.addEventListener('click', this.handleExport);
        this.importButton.addEventListener('click', this.handleImport);

        // Diagnostic actions
        const refreshBtn = this.container.querySelector('.btn-refresh-diagnostics');
        const clearCacheBtn = this.container.querySelector('.btn-clear-cache');
        const cleanupBtn = this.container.querySelector('.btn-run-cleanup');

        if (refreshBtn) refreshBtn.addEventListener('click', () => this.updateDiagnostics());
        if (clearCacheBtn) clearCacheBtn.addEventListener('click', () => this.clearCache());
        if (cleanupBtn) cleanupBtn.addEventListener('click', () => this.runCleanup());

        // Range input updates
        this.container.querySelectorAll('input[type="range"]').forEach(range => {
            range.addEventListener('input', (e) => {
                const valueSpan = e.target.parentElement.querySelector('.threshold-value');
                if (valueSpan) {
                    valueSpan.textContent = Math.round(e.target.value * 100) + '%';
                }
            });
        });
    }

    /**
     * Load current settings into form
     */
    loadCurrentSettings() {
        this.settingInputs.forEach(input => {
            const settingPath = input.name;
            if (!settingPath) return;

            const value = this.settingsManager.get(settingPath);
            
            if (input.type === 'checkbox') {
                input.checked = Boolean(value);
            } else if (input.type === 'range') {
                input.value = value;
                const valueSpan = input.parentElement.querySelector('.threshold-value');
                if (valueSpan) {
                    valueSpan.textContent = Math.round(value * 100) + '%';
                }
            } else if (settingPath.includes('Age')) {
                // Convert milliseconds to days for display
                input.value = Math.floor(value / 86400000);
            } else if (settingPath === 'dataSync.fetchFrequency') {
                // Convert milliseconds to minutes for display
                input.value = Math.floor(value / 60000);
            } else {
                input.value = value;
            }
        });
    }

    /**
     * Handle tab change
     */
    handleTabChange(event) {
        const tabName = event.target.dataset.tab;
        if (!tabName) return;

        // Use tab manager to handle tab change
        this.tabManager.handleTabChange(tabName);
        this.activeTab = tabName;
    }

    /**
     * Handle setting change
     */
    handleSettingChange(event) {
        this.unsavedChanges = true;
        this.saveButton.disabled = false;
        this.unsavedIndicator.style.display = 'block';
    }

    /**
     * Handle save button click
     */
    handleSave() {
        const updates = {};
        
        this.settingInputs.forEach(input => {
            const settingPath = input.name;
            if (!settingPath) return;

            let value;
            
            if (input.type === 'checkbox') {
                value = input.checked;
            } else if (input.type === 'number') {
                value = parseFloat(input.value);
                
                // Convert display values back to internal format
                if (settingPath.includes('Age')) {
                    value = value * 86400000; // Days to milliseconds
                } else if (settingPath === 'dataSync.fetchFrequency') {
                    value = value * 60000; // Minutes to milliseconds
                }
            } else if (input.type === 'range') {
                value = parseFloat(input.value);
            } else {
                value = input.value;
            }
            
            updates[settingPath] = value;
        });
        
        this.settingsManager.updateMultiple(updates);
        
        this.unsavedChanges = false;
        this.saveButton.disabled = true;
        this.unsavedIndicator.style.display = 'none';
        
        this.showNotification('Settings saved successfully', 'success');
    }

    /**
     * Handle reset button click
     */
    handleReset() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            this.settingsManager.reset();
            this.loadCurrentSettings();
            this.unsavedChanges = false;
            this.saveButton.disabled = true;
            this.unsavedIndicator.style.display = 'none';
            this.showNotification('Settings reset to defaults', 'info');
        }
    }

    /**
     * Handle export button click
     */
    handleExport() {
        const exportData = this.settingsManager.exportSettings();
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `snap-dashboard-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('Settings exported successfully', 'success');
    }

    /**
     * Handle import button click
     */
    handleImport() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const importData = JSON.parse(e.target.result);
                    const success = this.settingsManager.importSettings(importData);
                    
                    if (success) {
                        this.loadCurrentSettings();
                        this.showNotification('Settings imported successfully', 'success');
                    } else {
                        this.showNotification('Failed to import settings', 'error');
                    }
                } catch (error) {
                    this.showNotification('Invalid settings file', 'error');
                }
            };
            reader.readAsText(file);
        };
        
        input.click();
    }

    /**
     * Update diagnostics display
     */
    updateDiagnostics() {
        // This would integrate with actual system monitoring
        // For now, showing placeholder values
        
        const diagnostics = {
            'current-memory': '256 MB',
            'db-size': '1.2 GB',
            'products-count': '2.5M',
            'sales-count': '8.7M',
            'avg-query-time': '150ms',
            'cache-hit-rate': '94%',
            'error-rate': '0.02%',
            'uptime': '2d 14h 32m'
        };
        
        Object.entries(diagnostics).forEach(([id, value]) => {
            const element = this.container.querySelector(`#${id}`);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * Clear cache
     */
    clearCache() {
        if (confirm('Are you sure you want to clear all cached data?')) {
            // This would integrate with actual cache clearing
            this.showNotification('Cache cleared successfully', 'success');
        }
    }

    /**
     * Run cleanup
     */
    runCleanup() {
        if (confirm('Are you sure you want to run data cleanup?')) {
            // This would integrate with actual cleanup process
            this.showNotification('Cleanup completed successfully', 'success');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Simple notification implementation
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
  }

  // Settings Component HTML
  const settingsHTML = `
<div class="settings-page">
  <div class="settings-header">
    <h1>
      <span class="settings-icon">⚙️</span>
      Settings
    </h1>
    <div class="settings-actions">
      <button class="btn-export">
        <span>📤</span>
        Export Settings
      </button>
      <button class="btn-import">
        <span>📥</span>
        Import Settings
      </button>
      <button class="btn-reset">
        <span>🔄</span>
        Reset to Defaults
      </button>
      <button class="btn-save" disabled>
        <span>💾</span>
        Save Changes
      </button>
    </div>
  </div>

  <div class="settings-content">
    <div class="settings-tabs">
      <button class="tab-button active" data-tab="general">
        <span class="tab-icon">⚙️</span>
        General
      </button>
      <button class="tab-button" data-tab="performance">
        <span class="tab-icon">🚀</span>
        Performance
      </button>
      <button class="tab-button" data-tab="data">
        <span class="tab-icon">💾</span>
        Data Management
      </button>
      <button class="tab-button" data-tab="ui">
        <span class="tab-icon">🎨</span>
        User Interface
      </button>
      <button class="tab-button" data-tab="notifications">
        <span class="tab-icon">🔔</span>
        Notifications
      </button>
      <button class="tab-button" data-tab="advanced">
        <span class="tab-icon">🔧</span>
        Advanced
      </button>
      <button class="tab-button" data-tab="diagnostics">
        <span class="tab-icon">📊</span>
        Diagnostics
      </button>
    </div>

    <div class="settings-panels">
      <!-- General Settings -->
      <div class="settings-panel active" data-panel="general">
        <h2>General Settings</h2>

        <div class="setting-group">
          <h3>Data Synchronization</h3>
          <div class="setting-item">
            <label>Fetch Frequency (minutes)</label>
            <input type="number" name="dataSync.fetchFrequency" min="1" max="60" />
            <span class="setting-help">How often to check for new data</span>
          </div>
          <div class="setting-item">
            <label>Enable Real-time Updates</label>
            <input type="checkbox" name="dataSync.enableRealTime" />
            <span class="setting-help">Automatically update data in real-time</span>
          </div>
          <div class="setting-item">
            <label>Auto Backup</label>
            <input type="checkbox" name="dataSync.autoBackup" />
            <span class="setting-help">Automatically backup data daily</span>
          </div>
        </div>

        <div class="setting-group">
          <h3>Marketplace Preferences</h3>
          <div class="setting-item">
            <label>Default Marketplace</label>
            <select name="marketplace.defaultMarketplace">
              <option value="all">All Marketplaces</option>
              <option value="amazon.com">Amazon.com</option>
              <option value="amazon.co.uk">Amazon.co.uk</option>
              <option value="amazon.de">Amazon.de</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Hide Inactive Marketplaces</label>
            <input type="checkbox" name="marketplace.hideInactiveMarketplaces" />
          </div>
        </div>
      </div>

      <!-- Performance Settings -->
      <div class="settings-panel" data-panel="performance">
        <h2>Performance Settings</h2>

        <div class="setting-group">
          <h3>Memory Management</h3>
          <div class="setting-item">
            <label>Maximum Memory Usage (MB)</label>
            <input type="number" name="memory.maxMemoryUsage" min="128" max="2048" step="64" />
            <span class="setting-help">Maximum memory for the application</span>
          </div>
          <div class="setting-item">
            <label>Cleanup Threshold (%)</label>
            <input type="range" name="memory.cleanupThreshold" min="0.5" max="1" step="0.05" />
            <span class="threshold-value">80%</span>
          </div>
          <div class="setting-item">
            <label>Aggressive Cleanup</label>
            <input type="checkbox" name="memory.aggressiveCleanup" />
            <span class="setting-help">More aggressive memory cleanup for low-memory devices</span>
          </div>
        </div>

        <div class="setting-group">
          <h3>Virtual List Performance</h3>
          <div class="setting-item">
            <label>Item Height (px)</label>
            <input type="number" name="virtualList.itemHeight" min="40" max="200" />
          </div>
          <div class="setting-item">
            <label>Page Size</label>
            <input type="number" name="virtualList.pageSize" min="10" max="1000" />
            <span class="setting-help">Number of items to load per page</span>
          </div>
          <div class="setting-item">
            <label>Buffer Size</label>
            <input type="number" name="virtualList.bufferSize" min="5" max="50" />
            <span class="setting-help">Extra items to render outside viewport</span>
          </div>
        </div>
      </div>

      <!-- Data Management Settings -->
      <div class="settings-panel" data-panel="data">
        <h2>Data Management</h2>

        <div class="setting-group">
          <h3>Data Retention</h3>
          <div class="setting-item">
            <label>Product Data Retention (days)</label>
            <input type="number" name="dataRetention.maxProductAge" min="1" max="365" />
          </div>
          <div class="setting-item">
            <label>Sales Data Retention (days)</label>
            <input type="number" name="dataRetention.maxSalesAge" min="7" max="1095" />
          </div>
          <div class="setting-item">
            <label>Enable Auto Cleanup</label>
            <input type="checkbox" name="dataRetention.enableAutoCleanup" />
          </div>
        </div>

        <div class="setting-group">
          <h3>Export Settings</h3>
          <div class="setting-item">
            <label>Default Export Format</label>
            <select name="export.defaultFormat">
              <option value="json">JSON</option>
              <option value="csv">CSV</option>
              <option value="xlsx">Excel</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Compress Exports</label>
            <input type="checkbox" name="export.compressExports" />
          </div>
        </div>
      </div>

      <!-- UI Settings -->
      <div class="settings-panel" data-panel="ui">
        <h2>User Interface</h2>

        <div class="setting-group">
          <h3>Appearance</h3>
          <div class="setting-item">
            <label>Theme</label>
            <select name="ui.theme">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Compact Mode</label>
            <input type="checkbox" name="ui.compactMode" />
            <span class="setting-help">Reduce spacing for more content</span>
          </div>
        </div>

        <div class="setting-group">
          <h3>Localization</h3>
          <div class="setting-item">
            <label>Date Format</label>
            <select name="ui.dateFormat">
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
            </select>
          </div>
          <div class="setting-item">
            <label>Currency</label>
            <select name="ui.currency">
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Notifications Settings -->
      <div class="settings-panel" data-panel="notifications">
        <h2>Notifications</h2>

        <div class="setting-group">
          <h3>Alert Types</h3>
          <div class="setting-item">
            <label>Desktop Notifications</label>
            <input type="checkbox" name="notifications.enableDesktop" />
          </div>
          <div class="setting-item">
            <label>Sound Alerts</label>
            <input type="checkbox" name="notifications.enableSound" />
          </div>
          <div class="setting-item">
            <label>Performance Alerts</label>
            <input type="checkbox" name="notifications.enablePerformanceAlerts" />
          </div>
        </div>

        <div class="setting-group">
          <h3>Alert Thresholds</h3>
          <div class="setting-item">
            <label>Memory Usage Alert (%)</label>
            <input type="range" name="notifications.alertThresholds.memoryUsage" min="0.5" max="1" step="0.05" />
            <span class="threshold-value">85%</span>
          </div>
          <div class="setting-item">
            <label>Slow Query Alert (seconds)</label>
            <input type="number" name="notifications.alertThresholds.queryTime" min="1" max="30" />
          </div>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="settings-panel" data-panel="advanced">
        <h2>Advanced Settings</h2>

        <div class="setting-group">
          <h3>Developer Options</h3>
          <div class="setting-item">
            <label>Enable Debug Mode</label>
            <input type="checkbox" name="advanced.enableDebugMode" />
            <span class="setting-help">Show debug information in console</span>
          </div>
          <div class="setting-item">
            <label>Verbose Logging</label>
            <input type="checkbox" name="advanced.enableVerboseLogging" />
          </div>
          <div class="setting-item">
            <label>Performance Profiling</label>
            <input type="checkbox" name="advanced.enablePerformanceProfiling" />
          </div>
        </div>

        <div class="setting-group">
          <h3>Experimental Features</h3>
          <div class="setting-item">
            <label>Enable Experimental Features</label>
            <input type="checkbox" name="advanced.enableExperimentalFeatures" />
            <span class="setting-help">⚠️ May cause instability</span>
          </div>
          <div class="setting-item">
            <label>Max Concurrent Queries</label>
            <input type="number" name="advanced.maxConcurrentQueries" min="1" max="20" />
          </div>
        </div>
      </div>

      <!-- Diagnostics Panel -->
      <div class="settings-panel" data-panel="diagnostics">
        <h2>System Diagnostics</h2>

        <div class="diagnostic-section">
          <h3>Current Status</h3>
          <div class="diagnostic-grid">
            <div class="diagnostic-item">
              <label>Memory Usage</label>
              <span class="diagnostic-value" id="current-memory">--</span>
            </div>
            <div class="diagnostic-item">
              <label>IndexedDB Size</label>
              <span class="diagnostic-value" id="db-size">--</span>
            </div>
            <div class="diagnostic-item">
              <label>Products Loaded</label>
              <span class="diagnostic-value" id="products-count">--</span>
            </div>
            <div class="diagnostic-item">
              <label>Sales Records</label>
              <span class="diagnostic-value" id="sales-count">--</span>
            </div>
          </div>
        </div>

        <div class="diagnostic-section">
          <h3>Performance Metrics</h3>
          <div class="diagnostic-grid">
            <div class="diagnostic-item">
              <label>Average Query Time</label>
              <span class="diagnostic-value" id="avg-query-time">--</span>
            </div>
            <div class="diagnostic-item">
              <label>Cache Hit Rate</label>
              <span class="diagnostic-value" id="cache-hit-rate">--</span>
            </div>
            <div class="diagnostic-item">
              <label>Error Rate</label>
              <span class="diagnostic-value" id="error-rate">--</span>
            </div>
            <div class="diagnostic-item">
              <label>Uptime</label>
              <span class="diagnostic-value" id="uptime">--</span>
            </div>
          </div>
        </div>

        <div class="diagnostic-actions">
          <button class="btn-refresh-diagnostics">Refresh</button>
          <button class="btn-clear-cache">Clear Cache</button>
          <button class="btn-run-cleanup">Run Cleanup</button>
        </div>
      </div>
    </div>
  </div>

  <div class="settings-footer">
    <div class="unsaved-indicator" style="display: none;">
      <span>⚠️ You have unsaved changes</span>
    </div>
  </div>
</div>
`;

  // Settings Component CSS - Matching existing UI style
  const settingsCSS = `
.settings-page {
  width: 100%;
  min-width: 1024px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  font-family: 'Amazon Ember', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .settings-header {
  background: var(--bg-primary);
  border: none;
}

.settings-header h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

[data-theme="dark"] .settings-header h1 {
  color: #FFFFFF;
}

.settings-icon {
  font-size: 28px;
}

.settings-actions {
  display: flex;
  gap: 12px;
}

.settings-actions button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 42px;
  padding: 0 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

[data-theme="dark"] .settings-actions button {
  background: #292E38;
  border-color: var(--border-color);
  color: var(--text-primary);
}

.settings-actions button:hover {
  background: var(--btn-hover);
}

[data-theme="dark"] .settings-actions button:hover {
  background: #3A4048;
}

.btn-save {
  background: #470CED !important;
  color: white !important;
  border-color: #470CED !important;
}

.btn-save:hover {
  background: #3A0BC4 !important;
}

.btn-save:disabled {
  background: var(--action-btn-disabled) !important;
  border-color: var(--action-btn-disabled) !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.settings-content {
  display: flex;
  gap: 16px;
}

.settings-tabs {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 220px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 16px;
  height: fit-content;
}

[data-theme="dark"] .settings-tabs {
  background: var(--bg-primary);
  border: none;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background: var(--btn-hover);
}

[data-theme="dark"] .tab-button:hover {
  background: #292E38;
}

.tab-button.active {
  background: #470CED;
  color: white;
  font-weight: 600;
}

.tab-icon {
  font-size: 16px;
  margin-right: 8px;
}

.settings-panels {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.settings-panel {
  display: none;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 24px;
}

[data-theme="dark"] .settings-panel {
  background: var(--bg-primary);
  border: none;
}

.settings-panel.active {
  display: block;
}

.settings-panel h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

[data-theme="dark"] .settings-panel h2 {
  color: #FFFFFF;
}

.setting-group {
  margin-bottom: 24px;
  padding: 0;
}

.setting-group h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .setting-group h3 {
  color: #FFFFFF;
  border-bottom-color: #2A2D35;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  padding: 16px;
  background: var(--bg-primary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .setting-item {
  background: #292E38;
  border-color: #3A3D4A;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  min-width: 200px;
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

[data-theme="dark"] .setting-item label {
  color: #B4B9C5;
}

.setting-item input,
.setting-item select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  min-width: 120px;
  transition: border-color 0.2s ease;
}

[data-theme="dark"] .setting-item input,
[data-theme="dark"] .setting-item select {
  background: var(--bg-primary);
  border-color: #3A3D4A;
  color: #FFFFFF;
}

.setting-item input:focus,
.setting-item select:focus {
  outline: none;
  border-color: #470CED;
}

.setting-item input[type="checkbox"] {
  min-width: auto;
  width: 18px;
  height: 18px;
  accent-color: #470CED;
}

.setting-item input[type="range"] {
  min-width: 150px;
  accent-color: #470CED;
}

.setting-help {
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
  margin-left: auto;
}

[data-theme="dark"] .setting-help {
  color: #9AA3AB;
}

.threshold-value {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 600;
  min-width: 40px;
  background: var(--bg-primary);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .threshold-value {
  color: #FFFFFF;
  background: #1A1D23;
  border-color: #3A3D4A;
}

.diagnostic-section {
  margin-bottom: 24px;
}

.diagnostic-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .diagnostic-section h3 {
  color: #FFFFFF;
  border-bottom-color: #2A2D35;
}

.diagnostic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.diagnostic-item {
  padding: 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

[data-theme="dark"] .diagnostic-item {
  background: #292E38;
  border-color: #3A3D4A;
}

.diagnostic-item label {
  display: block;
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

[data-theme="dark"] .diagnostic-item label {
  color: #9AA3AB;
}

.diagnostic-value {
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

[data-theme="dark"] .diagnostic-value {
  color: #FFFFFF;
}

.diagnostic-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.diagnostic-actions button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 42px;
  padding: 0 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

[data-theme="dark"] .diagnostic-actions button {
  background: #292E38;
  border-color: var(--border-color);
  color: var(--text-primary);
}

.diagnostic-actions button:hover {
  background: var(--btn-hover);
}

[data-theme="dark"] .diagnostic-actions button:hover {
  background: #3A4048;
}

.settings-footer {
  margin-top: 24px;
  padding: 16px 24px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .settings-footer {
  background: var(--bg-primary);
  border: none;
}

.unsaved-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #FFF3CD;
  border: 1px solid #FFEAA7;
  border-radius: 6px;
  color: #856404;
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
}

[data-theme="dark"] .unsaved-indicator {
  background: #3D2914;
  border-color: #5D4037;
  color: #FFB74D;
}

.notification {
  position: fixed;
  top: 80px;
  right: 20px;
  padding: 16px 20px;
  border-radius: 8px;
  color: white;
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease;
  max-width: 400px;
}

.notification-success {
  background: #28A745;
  border: 1px solid #1E7E34;
}

.notification-error {
  background: #DC3545;
  border: 1px solid #C82333;
}

.notification-info {
  background: #470CED;
  border: 1px solid #3A0BC4;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .settings-page {
    min-width: auto;
    padding: 16px;
  }

  .settings-content {
    flex-direction: column;
    gap: 16px;
  }

  .settings-tabs {
    flex-direction: row;
    overflow-x: auto;
    min-width: auto;
    padding: 12px;
  }

  .tab-button {
    white-space: nowrap;
    min-width: 120px;
  }

  .diagnostic-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .settings-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }

  .settings-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .setting-item label {
    min-width: auto;
  }

  .notification {
    right: 16px;
    left: 16px;
    max-width: none;
  }
}
`;

  // Load required scripts for settings functionality
  async function loadRequiredScripts() {
    const scriptsToLoad = [
      'src/settings/settings-manager.js'
    ];

    const loadPromises = scriptsToLoad.map(scriptPath => {
      return new Promise((resolve, reject) => {
        // Check if script is already loaded
        if (document.querySelector(`script[src="${scriptPath}"]`)) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = scriptPath;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    });

    await Promise.all(loadPromises);
  }

  // Initialize settings component
  function initSettings() {
    console.log('🔧 Settings component loaded - start initialization');

    // Get main content container
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) {
      console.error('❌ Main content container not found');
      return;
    }

    // Inject CSS
    let styleElement = document.getElementById('settings-styles');
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'settings-styles';
      styleElement.textContent = settingsCSS;
      document.head.appendChild(styleElement);
    }

    // Render HTML
    mainContent.innerHTML = settingsHTML;

    // Load required scripts if not already loaded
    loadRequiredScripts().then(() => {
      // Initialize settings manager if available
      if (typeof SettingsManager !== 'undefined') {
        if (!window.settingsManager) {
          window.settingsManager = new SettingsManager();
        }

        // Initialize settings component
        const settingsContainer = document.querySelector('.settings-page');
        if (settingsContainer) {
          window.settingsComponentInstance = new SettingsComponent(settingsContainer, window.settingsManager);
          console.log('✅ Settings component initialized with SettingsManager');
        }
      } else {
        console.warn('⚠️ SettingsManager not available, using basic settings UI');
        initBasicSettings();
      }
    }).catch(error => {
      console.error('❌ Error loading settings dependencies:', error);
      initBasicSettings();
    });

    console.log('✅ Settings component initialization completed');
  }

  // Basic settings initialization without SettingsManager
  function initBasicSettings() {
    // Basic tab switching
    const tabButtons = document.querySelectorAll('.tab-button');
    const panels = document.querySelectorAll('.settings-panel');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabName = button.dataset.tab;

        // Update active tab
        tabButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Update active panel
        panels.forEach(panel => panel.classList.remove('active'));
        const targetPanel = document.querySelector(`[data-panel="${tabName}"]`);
        if (targetPanel) {
          targetPanel.classList.add('active');
        }
      });
    });

    // Basic diagnostics
    updateBasicDiagnostics();

    // Refresh diagnostics button
    const refreshBtn = document.querySelector('.btn-refresh-diagnostics');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', updateBasicDiagnostics);
    }
  }

  function updateBasicDiagnostics() {
    // Basic diagnostic values
    const diagnostics = {
      'current-memory': '256 MB',
      'db-size': '1.2 GB',
      'products-count': '2.5M',
      'sales-count': '8.7M',
      'avg-query-time': '150ms',
      'cache-hit-rate': '94%',
      'error-rate': '0.02%',
      'uptime': '2d 14h 32m'
    };

    Object.entries(diagnostics).forEach(([id, value]) => {
      const element = document.querySelector(`#${id}`);
      if (element) {
        element.textContent = value;
      }
    });
  }

  // Export component for the navigation system
  window.settingsComponent = {
    render: initSettings,
    html: settingsHTML,
    css: settingsCSS
  };
})();
