/**
 * Settings Component Styles
 * Modular CSS for the settings page component
 */

.settings-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-header h1 {
    margin: 0;
    color: var(--text-accent);
    font-size: 1.75rem;
    font-weight: 600;
}

.settings-actions {
    display: flex;
    gap: 0.75rem;
}

.settings-actions button {
    padding: 0.5rem 1rem;
    border: 1px solid var(--btn-border);
    background: var(--bg-primary);
    color: var(--text-primary);
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-actions button:hover {
    background: var(--btn-hover);
}

.settings-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.settings-actions .btn-save {
    background: var(--action-btn-bg);
    color: var(--action-btn-text);
    border-color: var(--action-btn-bg);
}

.settings-actions .btn-save:hover:not(:disabled) {
    background: var(--action-btn-hover);
}

.settings-content {
    display: flex;
    gap: 2rem;
}

.settings-tabs {
    flex: 0 0 200px;
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1rem 0;
}

.settings-tab {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: var(--text-primary);
    text-align: left;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-tab:hover {
    background: var(--btn-hover);
}

.settings-tab.active {
    background: var(--bg-primary);
    color: var(--text-accent);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.settings-panel {
    flex: 1;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 1.5rem;
}

.settings-section {
    margin-bottom: 2rem;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-accent);
    font-size: 1.125rem;
    font-weight: 600;
}

.settings-group {
    margin-bottom: 1.5rem;
}

.settings-group:last-child {
    margin-bottom: 0;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    flex: 1;
    margin-right: 1rem;
}

.setting-label h4 {
    margin: 0 0 0.25rem 0;
    color: var(--text-accent);
    font-size: 0.875rem;
    font-weight: 500;
}

.setting-label p {
    margin: 0;
    color: var(--text-primary);
    font-size: 0.75rem;
    line-height: 1.4;
}

.setting-control {
    flex: 0 0 auto;
}

.setting-control input,
.setting-control select {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-accent);
    font-size: 0.875rem;
}

.setting-control input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.setting-control input[type="range"] {
    width: 120px;
}

.diagnostics-section {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.diagnostics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.diagnostic-item {
    background: var(--bg-primary);
    border-radius: 6px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
}

.diagnostic-item h5 {
    margin: 0 0 0.5rem 0;
    color: var(--text-accent);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.diagnostic-value {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
}

.diagnostic-value.warning {
    color: var(--color-danger);
}

.diagnostic-value.success {
    color: var(--color-success);
}

/* Dark theme support */
[data-theme="dark"] .settings-page {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .settings-tabs {
    background: var(--bg-secondary);
}

[data-theme="dark"] .settings-tab.active {
    background: var(--bg-primary);
}

[data-theme="dark"] .settings-panel {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .diagnostics-section {
    background: var(--bg-secondary);
}

[data-theme="dark"] .diagnostic-item {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

/* Responsive design */
@media (max-width: 768px) {
    .settings-content {
        flex-direction: column;
    }
    
    .settings-tabs {
        flex: none;
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
    }
    
    .settings-tab {
        white-space: nowrap;
        flex: 0 0 auto;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .setting-control {
        width: 100%;
    }
}

/* Animation for tab transitions */
.settings-panel {
    animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading states */
.settings-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-primary);
}

.settings-error {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
}

/* Unsaved changes indicator */
.settings-header.has-changes::after {
    content: "•";
    color: var(--color-danger);
    font-size: 1.5rem;
    margin-left: 0.5rem;
}
