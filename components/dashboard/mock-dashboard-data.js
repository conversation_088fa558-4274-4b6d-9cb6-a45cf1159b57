(function() {
  // Centralized dashboard mock/static data. All fields are optional.
  // Populate to override hardcoded strings in the rendered dashboard without editing component code.
  // This is used for both demo content and externalized static text.
  window.DashboardMockData = window.DashboardMockData || {
    fourCards: {
      currentMonth: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      lastMonth: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      currentYear: { title: null, date: null, current: 0, previous: 0, hideComparison: true },
      lastYear: { title: null, date: null, current: 0, previous: 0, hideComparison: true }
    },
    lastWeek: { title: null, date: null },
    todayVsPreviousYears: { date: null },
    monthlySales: { date: null },
    yearlySales: { date: null },
    lifetimeInsights: {
      // If provided, these override the top numbers via runtime text replacement
      // Demo defaults moved here so component stays free of inline mock values
      salesCount: 217223,
      royalties: '$511,933.0',
      returned: '(-17,099)',
      cancelled: '2,231',
      ads: '65,000'
    }
    ,
    // Listings Status Overview mock numbers per marketplace.
    // Keys: 'all', 'us', 'uk', 'de', 'fr', 'it', 'es', 'jp'
    // Values: live, draft, auto, review, processing, translating, locked, timeout, rejected, silentRemovals
    listingsStatus: {
      all:      { live: 1233432, draft: 12, auto: 12432, review: 0, processing: 0, translating: 3, locked: 132, timeout: 132, rejected: 43, silentRemovals: 54 },
      us:       { live: 843211, draft: 5,  auto: 8032,  review: 0, processing: 0, translating: 2, locked: 71,  timeout: 80,  rejected: 21, silentRemovals: 23 },
      uk:       { live: 142233, draft: 2,  auto: 1500,  review: 0, processing: 0, translating: 0, locked: 10,  timeout: 8,   rejected: 6,  silentRemovals: 9 },
      de:       { live: 156701, draft: 1,  auto: 1432,  review: 0, processing: 0, translating: 0, locked: 12,  timeout: 10,  rejected: 5,  silentRemovals: 7 },
      fr:       { live: 111090, draft: 1,  auto: 1100,  review: 0, processing: 0, translating: 0, locked: 9,   timeout: 7,   rejected: 4,  silentRemovals: 6 },
      it:       { live: 95542,  draft: 1,  auto: 900,   review: 0, processing: 0, translating: 0, locked: 8,   timeout: 7,   rejected: 3,  silentRemovals: 4 },
      es:       { live: 101223, draft: 1,  auto: 968,   review: 0, processing: 0, translating: 1, locked: 7,   timeout: 6,   rejected: 3,  silentRemovals: 3 },
      jp:       { live: 82132,  draft: 1,  auto: 500,   review: 0, processing: 0, translating: 0, locked: 5,   timeout: 4,   rejected: 0,  silentRemovals: 0 }
    }
    ,
    // Payout mock data (numeric net + display fields) for Next and Previous
    payout: {
      currencyToUSD: { USD: 1, GBP: 1.27, EUR: 1.10, JPY: 0.0065 },
      next: {
        us: { currency: 'USD', currencySymbol: '$', net: 31130.51,
          grossEarnings: '$344.18', refunds: '-$13.67', netEarnings: '$31,130.51', afterTaxes: '$29,130.51', unitsCharged: '188', unitsRefunded: '7' },
        uk: { currency: 'GBP', currencySymbol: '£', net: 1409.0,
          grossEarnings: '£298.45', refunds: '-£11.23', netEarnings: '£1,409.0', afterTaxes: '£1,409.0', unitsCharged: '165', unitsRefunded: '5' },
        de: { currency: 'EUR', currencySymbol: '€', net: 2933.0,
          grossEarnings: '€312.67', refunds: '-€15.34', netEarnings: '€2,933.0', afterTaxes: '€2,933.0', unitsCharged: '201', unitsRefunded: '8' },
        fr: { currency: 'EUR', currencySymbol: '€', net: 2933.0,
          grossEarnings: '€287.89', refunds: '-€12.45', netEarnings: '€2,933.0', afterTaxes: '€2,933.0', unitsCharged: '178', unitsRefunded: '6' },
        it: { currency: 'EUR', currencySymbol: '€', net: 2933.0,
          grossEarnings: '€298.12', refunds: '-€14.67', netEarnings: '€2,933.0', afterTaxes: '€2,933.0', unitsCharged: '189', unitsRefunded: '7' },
        es: { currency: 'EUR', currencySymbol: '€', net: 2933.0,
          grossEarnings: '€276.34', refunds: '-€13.21', netEarnings: '€2,933.0', afterTaxes: '€2,933.0', unitsCharged: '167', unitsRefunded: '6' },
        jp: { currency: 'JPY', currencySymbol: '¥', net: 159500,
          grossEarnings: '¥165,000', refunds: '-¥5,500', netEarnings: '¥159,500', afterTaxes: '¥159,500', unitsCharged: '172', unitsRefunded: '6' }
      },
      previous: {
        us: { currency: 'USD', currencySymbol: '$', net: 28945.33,
          grossEarnings: '$298.45', refunds: '-$11.23', netEarnings: '$28,945.33', afterTaxes: '$26,945.33', unitsCharged: '165', unitsRefunded: '5' },
        uk: { currency: 'GBP', currencySymbol: '£', net: 1205.5,
          grossEarnings: '£267.89', refunds: '-£9.87', netEarnings: '£1,205.5', afterTaxes: '£1,205.5', unitsCharged: '142', unitsRefunded: '4' },
        de: { currency: 'EUR', currencySymbol: '€', net: 2567.8,
          grossEarnings: '€289.34', refunds: '-€12.56', netEarnings: '€2,567.8', afterTaxes: '€2,567.8', unitsCharged: '178', unitsRefunded: '6' },
        fr: { currency: 'EUR', currencySymbol: '€', net: 2234.2,
          grossEarnings: '€256.78', refunds: '-€10.34', netEarnings: '€2,234.2', afterTaxes: '€2,234.2', unitsCharged: '156', unitsRefunded: '5' },
        it: { currency: 'EUR', currencySymbol: '€', net: 1987.6,
          grossEarnings: '€234.56', refunds: '-€11.89', netEarnings: '€1,987.6', afterTaxes: '€1,987.6', unitsCharged: '145', unitsRefunded: '5' },
        es: { currency: 'EUR', currencySymbol: '€', net: 1654.9,
          grossEarnings: '€198.23', refunds: '-€8.76', netEarnings: '€1,654.9', afterTaxes: '€1,654.9', unitsCharged: '123', unitsRefunded: '3' },
        jp: { currency: 'JPY', currencySymbol: '¥', net: 0,
          grossEarnings: '¥0', refunds: '-¥0', netEarnings: '¥0', afterTaxes: '¥0', unitsCharged: '0', unitsRefunded: '0' }
      }
    }
  };
})();


