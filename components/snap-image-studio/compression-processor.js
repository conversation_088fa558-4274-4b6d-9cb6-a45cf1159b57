// Compression Processor Module

// Compression level constants with PNG quality parameters
const COMPRESSION_LEVELS = {
  ADAPTIVE: 'adaptive',
  LIGHT: { pngLevel: 1024, quality: 0.9 },
  BALANCED: { pngLevel: 512, quality: 0.7 },
  AGGRESSIVE: { pngLevel: 128, quality: 0.5 }
};

// Main compression processor class
class CompressionProcessor {
  constructor() {
    // Initialize any required properties
    this.isProcessing = false;
    this.shouldStop = false;
  }

  /**
   * Process an image with the specified compression level
   * @param {File} file - The image file to process
   * @param {string|Object} compressionLevel - The compression level to apply
   * @param {Function} progressCallback - Callback for progress updates
   * @returns {Promise<Blob>} - The compressed image as a Blob
   */
  async processImage(file, compressionLevel, progressCallback) {
    try {
      // Update progress
      progressCallback?.({ stage: 'loading', progress: 0 });

      // Load the image
      const imageData = await this.loadImage(file);
      progressCallback?.({ stage: 'loading', progress: 100 });

      // Apply compression based on level
      progressCallback?.({ stage: 'compression', progress: 0 });
      let compressedBlob;

      switch (compressionLevel) {
        case COMPRESSION_LEVELS.ADAPTIVE:
          compressedBlob = await this.applyAdaptiveCompression(imageData);
          break;
        case COMPRESSION_LEVELS.LIGHT:
          compressedBlob = await this.applyFixedCompression(imageData, COMPRESSION_LEVELS.LIGHT);
          break;
        case COMPRESSION_LEVELS.BALANCED:
          compressedBlob = await this.applyFixedCompression(imageData, COMPRESSION_LEVELS.BALANCED);
          break;
        case COMPRESSION_LEVELS.AGGRESSIVE:
          compressedBlob = await this.applyFixedCompression(imageData, COMPRESSION_LEVELS.AGGRESSIVE);
          break;
        default:
          throw new Error('Invalid compression level');
      }

      // Apply quality compression step before output (NEW PIPELINE STEP)
      progressCallback?.({ stage: 'quality_compression', progress: 0 });
      const finalBlob = await this.applyFinalQualityCompression(compressedBlob, compressionLevel);
      progressCallback?.({ stage: 'quality_compression', progress: 100 });

      progressCallback?.({ stage: 'compression', progress: 100 });
      progressCallback?.({ stage: 'complete', progress: 100 });

      return finalBlob;
    } catch (error) {
      if (window.SnapLogger) {
        window.SnapLogger.error('Error processing image:', error);
      } else {
        console.error('Error processing image:', error);
      }
      throw error;
    }
  }

  /**
   * Load an image file and return its data
   * @param {File} file - The image file to load
   * @returns {Promise<Blob>} - The image as a Blob
   */
  async loadImage(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        canvas.toBlob(resolve, 'image/png', 1.0);
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Apply PNG quality compression to reduce file size while maintaining visual quality
   * @param {HTMLCanvasElement} canvas - The canvas containing the image
   * @param {number} quality - Quality level (0.1 to 1.0)
   * @returns {Promise<Blob>} - The quality-compressed PNG image as a Blob
   */
  async applyQualityCompression(canvas, quality) {
    try {
      // Apply PNG quality compression
      const compressedBlob = await this.compressToPNG(canvas, quality);
      
      if (window.SnapLogger) {
        window.SnapLogger.debug(`PNG quality compression applied: quality=${quality}, size=${compressedBlob.size} bytes`);
      }
      
      return compressedBlob;
    } catch (error) {
      console.error('PNG quality compression failed:', error);
      // Fallback to default PNG quality
      return new Promise((resolve) => {
        canvas.toBlob(resolve, 'image/png', 0.8);
      });
    }
  }









  /**
   * Compress image to PNG format with quality control
   * @param {HTMLCanvasElement} canvas - The canvas containing the image
   * @param {number} quality - Quality level (0.1 to 1.0)
   * @returns {Promise<Blob>} - The PNG compressed image as a Blob
   */
  async compressToPNG(canvas, quality) {
    return new Promise((resolve) => {
      // PNG quality is primarily controlled by color depth and compression
      // For PNG, we'll use the quality parameter to determine compression level
      const compressionLevel = Math.floor((1 - quality) * 9); // 0-9 scale
      canvas.toBlob(resolve, 'image/png', quality);
    });
  }

  /**
   * Apply final PNG quality compression step before output
   * @param {Blob} blob - The compressed image blob
   * @param {string|Object} compressionLevel - The compression level used
   * @returns {Promise<Blob>} - The final quality-compressed PNG image as a Blob
   */
  async applyFinalQualityCompression(blob, compressionLevel) {
    try {
      // Extract quality parameter
      let quality = 1.0;
      
      if (typeof compressionLevel === 'object' && compressionLevel !== COMPRESSION_LEVELS.ADAPTIVE) {
        quality = compressionLevel.quality || 1.0;
      } else if (compressionLevel === COMPRESSION_LEVELS.ADAPTIVE) {
        // For adaptive compression, use balanced quality settings
        quality = 0.8;
      }
      
      // Convert blob to canvas for quality processing
      const canvas = await this.blobToCanvas(blob);
      
      // Apply PNG quality compression
      const finalBlob = await this.applyQualityCompression(canvas, quality);
      
      if (window.SnapLogger) {
        window.SnapLogger.debug(`Final PNG quality compression applied: quality=${quality}, final_size=${finalBlob.size} bytes`);
      }
      
      return finalBlob;
    } catch (error) {
      console.error('Final PNG quality compression failed:', error);
      // Return original blob if quality compression fails
      return blob;
    }
  }

  /**
   * Convert a blob to canvas for processing
   * @param {Blob} blob - The image blob to convert
   * @returns {Promise<HTMLCanvasElement>} - The canvas containing the image
   */
  async blobToCanvas(blob) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        resolve(canvas);
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(blob);
    });
  }

  /**
   * Apply adaptive compression based on file size
   * @param {Blob} blob - The image blob to compress
   * @returns {Promise<Blob>} - The compressed image as a Blob
   */
  async applyAdaptiveCompression(blob) {
    try {
      if (window.SnapLogger) {
        window.SnapLogger.debug(`Starting adaptive compression: Original size=${blob.size} bytes`);
      }
      
      // Skip if blob size <= 10MB
      if (blob.size <= 10485760) {
        console.log(`Skipping compression: Size=${blob.size} bytes`);
        return blob;
      }
      
      // Define realistic target size: 15MB or 75% of original, whichever is smaller
      const targetSize = Math.min(15728640, Math.floor(blob.size * 0.75));
      
      // Decode the image once before the loop to avoid repeated processing
      const arrayBuffer = await blob.arrayBuffer();
      const pngData = UPNG.decode(arrayBuffer);
      const rgba8Data = UPNG.toRGBA8(pngData)[0];
      
      // Start with highest compression level
      let compressionLevel = 1024;
      let compressedBlob = null;
      let attempts = 0;
      const MAX_ATTEMPTS = 5;
      let previousSize = blob.size;
      
      while (attempts < MAX_ATTEMPTS) {
        attempts++;
        
        // Check if processing should stop early
        if (this.shouldStop) {
          console.log('Compression stopped early by user');
          break;
        }
        
        // Re-encode with current compression level using pre-decoded data
        const compressedData = UPNG.encode([rgba8Data], pngData.width, pngData.height, compressionLevel);
        
        // Create new blob from compressed data
        compressedBlob = new Blob([compressedData], { type: 'image/png' });
        
        console.log(`Compression attempt ${attempts} with level ${compressionLevel}, size: ${compressedBlob.size} bytes`);
        
        // Check if target size is met
        if (compressedBlob.size <= targetSize) {
          console.log(`Target size reached: ${compressedBlob.size} bytes <= ${targetSize} bytes`);
          break;
        }
        
        // Check if we've reached minimum compression level
        if (compressionLevel <= 128) {
          console.log(`Minimum compression level reached: ${compressionLevel}`);
          break;
        }
        
        // Check if compression is actually improving (avoid no-op compressions)
        if (compressedBlob.size >= previousSize) {
          console.log(`No improvement in compression, stopping at level ${compressionLevel}`);
          break;
        }
        
        // Update previous size for next iteration
        previousSize = compressedBlob.size;
        
        // Reduce compression level for next attempt (use more granular steps)
        compressionLevel = Math.floor(compressionLevel / 1.5);
      }
      
      console.log(`Final compressed size: ${compressedBlob.size} bytes (${Math.round(compressedBlob.size / blob.size * 100)}% of original)`);
      return compressedBlob;
    } catch (error) {
      console.error('Adaptive compression failed:', error);
      return blob; // Return original if compression fails
    }
  }

  /**
   * Apply fixed level compression
   * @param {Blob} blob - The image blob to compress
   * @param {Object} levelConfig - The compression level configuration
   * @returns {Promise<Blob>} - The compressed image as a Blob
   */
  async applyFixedCompression(blob, levelConfig) {
    try {
      // Handle both old numeric format and new object format for backward compatibility
      const pngLevel = typeof levelConfig === 'number' ? levelConfig : levelConfig.pngLevel;
      
      console.log(`Starting fixed compression (level ${pngLevel}): Original size=${blob.size} bytes`);
      
      // Convert blob to arrayBuffer for UPNG processing
      const arrayBuffer = await blob.arrayBuffer();
      
      // Decode PNG using UPNG
      const pngData = UPNG.decode(arrayBuffer);
      
      // Get RGBA8 data
      const rgba8Data = UPNG.toRGBA8(pngData)[0];
      
      // Re-encode with compression
      const compressedData = UPNG.encode([rgba8Data], pngData.width, pngData.height, pngLevel);
      
      // Create new blob from compressed data
      const compressedBlob = new Blob([compressedData], { type: 'image/png' });
      
      console.log(`Final compressed size: ${compressedBlob.size} bytes (${Math.round(compressedBlob.size / blob.size * 100)}% of original)`);
      return compressedBlob;
    } catch (error) {
      console.error(`Fixed compression failed:`, error);
      return blob; // Return original if compression fails
    }
  }

  /**
   * Stop the current processing operation
   */
  stop() {
    this.shouldStop = true;
  }

  /**
   * Reset the processor state
   */
  reset() {
    this.isProcessing = false;
    this.shouldStop = false;
  }

  /**
   * Set custom PNG compression parameters
   * @param {Object} params - Custom compression parameters
   * @param {number} params.quality - Quality level (0.1 to 1.0)
   * @param {number} params.pngLevel - PNG compression level (0-1024)
   */
  setCustomParameters(params) {
    if (params.quality !== undefined) {
      this.customQuality = Math.max(0.1, Math.min(1.0, params.quality));
    }
    if (params.pngLevel !== undefined) {
      this.customPngLevel = Math.max(0, Math.min(1024, params.pngLevel));
    }
    
    if (window.SnapLogger) {
      window.SnapLogger.debug(`Custom PNG parameters set: quality=${this.customQuality}, pngLevel=${this.customPngLevel}`);
    }
  }

  /**
   * Get current custom PNG parameters
   * @returns {Object} - Current custom parameters
   */
  getCustomParameters() {
    return {
      quality: this.customQuality || 1.0,
      pngLevel: this.customPngLevel || 512
    };
  }
}

// Export the processor instance
window.compressionProcessor = new CompressionProcessor(); 