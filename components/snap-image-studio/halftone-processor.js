// Halftone Processor - Core functionality for Snap Image Studio Halftone Processing
// Adapted from snap-halftone implementation for batch processing

// Constants for halftone processing
const DPI = 300;
const FINAL_WIDTH = 4500;
const FINAL_HEIGHT = 5400;
const MARGIN = 50; // margin in pixels
const HALFTONE_ANGLE = 20 * (Math.PI / 180); // 20 degrees in radians
const HALFTONE_FREQUENCY = 32; // Lines per inch (LPI)
const CELL_SIZE = Math.round(DPI / HALFTONE_FREQUENCY);
const BLACK_THRESHOLD = 25; // Values <= 25 will be solid black
const WHITE_THRESHOLD = 230; // Values > 230 will be white with no dots
const USE_WHITE_DOTS = true; // White dots on black background

// Pixel classification constants for better readability
const PIXEL_UNKNOWN = 0;    // Unprocessed pixel
const PIXEL_SOLID = 1;      // Solid color pixel (black or white)
const PIXEL_MASK = 3;       // Mask pixel (no dots)

// Create CRC32 table for PNG chunk validation
const crc32Table = (() => {
  let table;
  const crcTable = [];
  for (let i = 0; i < 256; i++) {
    table = i;
    for (let j = 0; j < 8; j++) {
      table = table & 1 ? 3988292384 ^ (table >>> 1) : table >>> 1;
    }
    crcTable[i] = table;
  }
  return crcTable;
})();

// Calculate CRC32 for PNG chunks
function calculateCRC32(data) {
  let crc = -1;
  for (let i = 0; i < data.length; i++) {
    crc = (crc >>> 8) ^ crc32Table[(crc ^ data[i]) & 0xFF];
  }
  return (~crc >>> 0);
}

// Add DPI information to PNG file
async function setDPI(blob, dpi) {
  return new Promise(resolve => {
    const img = new Image();
    const objectUrl = URL.createObjectURL(blob);
    img.crossOrigin = "Anonymous";
    img.src = objectUrl;
    img.onload = async () => {
      URL.revokeObjectURL(objectUrl);
      
      const buffer = await blob.arrayBuffer();
      const data = new Uint8Array(buffer);
      
      // Verify PNG signature
      const pngSignature = [137, 80, 78, 71, 13, 10, 26, 10];
      for (let i = 0; i < pngSignature.length; i++) {
        if (data[i] !== pngSignature[i]) {
          return resolve(blob); // Not a PNG file, return original
        }
      }
      
      // Convert DPI to pixels per meter (which is what PNG uses)
      const dpiValue = Math.round(dpi * 39.3701); // 39.3701 inches per meter
      
      // Create pHYs chunk data (X DPI, Y DPI, unit specifier - 1 means meters)
      const pHYsData = new Uint8Array([
        dpiValue >>> 24 & 255, dpiValue >>> 16 & 255, dpiValue >>> 8 & 255, dpiValue & 255,
        dpiValue >>> 24 & 255, dpiValue >>> 16 & 255, dpiValue >>> 8 & 255, dpiValue & 255,
        1 // 1 means physical unit is meters
      ]);
      
      // Create pHYs chunk type identifier
      const chunkType = new Uint8Array([112, 72, 89, 115]); // "pHYs" in ASCII
      
      // Calculate CRC for the chunk
      const crc = calculateCRC32([...chunkType, ...pHYsData]);
      
      // Create the full chunk (length + type + data + CRC)
      const chunk = new Uint8Array([
        0, 0, 0, 9, // Length of data (9 bytes)
        ...chunkType,
        ...pHYsData,
        crc >>> 24 & 255, crc >>> 16 & 255, crc >>> 8 & 255, crc & 255
      ]);
      
      // Insert the pHYs chunk after IHDR (position 33)
      const newData = new Uint8Array(data.length + chunk.length);
      newData.set(data.slice(0, 33), 0); // Header + IHDR
      newData.set(chunk, 33); // Insert pHYs chunk
      newData.set(data.slice(33), 33 + chunk.length); // Rest of the file
      
      // Create new blob with DPI information
      const newBlob = new Blob([newData], { type: "image/png" });
      resolve(newBlob);
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(objectUrl);
      resolve(blob); // Return original if error
    };
  });
}

// Photoshop-style Levels adjustment
function photoshopLevels(data, inputBlack, inputMid, inputWhite, outputBlack, outputWhite) {
  // Convert input values from 0-255 range to 0-1 for calculations
  inputBlack = inputBlack / 255;
  inputMid = inputMid; // Gamma value is already in 0-1 range
  inputWhite = inputWhite / 255;
  outputBlack = outputBlack / 255;
  outputWhite = outputWhite / 255;
  
  // Calculate gamma from the middle input value (Photoshop formula)
  const gamma = 1 / inputMid;
  
  for (let i = 0; i < data.length; i += 4) {
    // Get normalized grayscale value (0-1)
    let v = data[i] / 255;
    
    // Apply input levels (black and white points)
    // Map to 0-1 range based on input black/white points
    v = (v - inputBlack) / (inputWhite - inputBlack);
    v = Math.max(0, Math.min(1, v)); // Clamp to 0-1 range
    
    // Apply gamma correction
    v = Math.pow(v, gamma);
    
    // Map to output range
    v = outputBlack + v * (outputWhite - outputBlack);
    v = Math.max(0, Math.min(1, v)); // Clamp to 0-1 range
    
    // Convert back to 0-255 range and set all RGB channels (keeping gray)
    const pixelValue = Math.round(v * 255);
    data[i] = data[i+1] = data[i+2] = pixelValue;
    // Alpha channel remains unchanged
  }
}

// Adjust saturation of RGB image data
function adjustSaturation(data, amount) {
  // amount: positive increases saturation, negative decreases
  // normalized amount between -100 and 100
  const saturation = 1 + (amount / 100);
  
  for (let i = 0; i < data.length; i += 4) {
    // Get RGB values
    const r = data[i];
    const g = data[i+1];
    const b = data[i+2];
    
    // Calculate luminance (gray) using the same formula as grayscale conversion
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
    
    // Adjust saturation by pushing colors away from gray
    data[i] = Math.max(0, Math.min(255, gray + saturation * (r - gray)));
    data[i+1] = Math.max(0, Math.min(255, gray + saturation * (g - gray)));
    data[i+2] = Math.max(0, Math.min(255, gray + saturation * (b - gray)));
    // Alpha channel remains unchanged
  }
}

// Draw a crisp dot (no anti-aliasing, perfectly round)
function drawCrispDot(ctx, centerX, centerY, radius) {
  // Round to integers to ensure crisp edges
  const cx = Math.round(centerX);
  const cy = Math.round(centerY);
  const r = Math.max(1, radius); // Ensure minimum radius of 1

  if (r <= 1) {
    // For very small dots, use ImageData approach for better performance
    const imageData = ctx.createImageData(1, 1);
    const data = imageData.data;
    
    // Set pixel color (assuming current fillStyle is set)
    const currentColor = ctx.fillStyle;
    if (currentColor === '#ffffff' || currentColor === 'white') {
      data[0] = data[1] = data[2] = 255; // White
    } else if (currentColor === '#000000' || currentColor === 'black') {
      data[0] = data[1] = data[2] = 0;   // Black
    } else {
      // Parse hex color or use default
      data[0] = data[1] = data[2] = 0;   // Default to black
    }
    data[3] = 255; // Full alpha
    
    // Put the single pixel
    ctx.putImageData(imageData, cx, cy);
    return;
  }

  // Use path-based drawing for perfectly round dots (anti-aliased)
  ctx.save();
  ctx.beginPath();
  ctx.arc(cx, cy, r, 0, Math.PI * 2, false);
  ctx.fill();
  ctx.restore();
}

// Get coordinates for rotated grid
function getRotatedCellCoordinates(x, y, cellSize, angle) {
  // Transform to rotated coordinate system
  // First, find which cell this pixel belongs to in the rotated grid
  const cosAngle = Math.cos(angle);
  const sinAngle = Math.sin(angle);
  
  // Rotate the point to find its position in the rotated grid
  const rotX = x * cosAngle + y * sinAngle;
  const rotY = -x * sinAngle + y * cosAngle;
  
  // Find the nearest cell center in the rotated grid
  const cellX = Math.floor(rotX / cellSize) * cellSize + cellSize / 2;
  const cellY = Math.floor(rotY / cellSize) * cellSize + cellSize / 2;
  
  // Rotate back to get the cell center in the original coordinate system
  return {
    centerX: cellX * cosAngle - cellY * sinAngle,
    centerY: cellX * sinAngle + cellY * cosAngle
  };
}

// Helper function for periodic yielding and abort checking
async function periodicYield(iteration, step = 32) {
  if ((iteration & (step - 1)) === 0) {
    await new Promise(r => (typeof requestAnimationFrame !== 'undefined' ? requestAnimationFrame(r) : setTimeout(r, 0)));
  }
}

// Web Worker for intensive pixel processing
const createPixelProcessingWorker = () => {
  const workerCode = `
    // Pixel processing worker for halftone image processing
    self.onmessage = function(e) {
      const { type, brightnessMap, USE_WHITE_DOTS, BLACK_THRESHOLD, WHITE_THRESHOLD } = e.data;
      
      if (type === 'classifyPixels') {
        // Process pixels in batches for better performance
        const batchSize = 1000;
        const results = new Uint8Array(brightnessMap.length);
        
        for (let i = 0; i < brightnessMap.length; i += batchSize) {
          const batchEnd = Math.min(i + batchSize, brightnessMap.length);
          
          for (let j = i; j < batchEnd; j++) {
            const brightness = brightnessMap[j];
            let pixelType = 0; // PIXEL_UNKNOWN
            
            if (USE_WHITE_DOTS) {
              // For white dots on black, invert the logic
              if (brightness >= 255 - BLACK_THRESHOLD) {
                pixelType = 1; // PIXEL_SOLID
              } else if (brightness < 255 - WHITE_THRESHOLD) {
                pixelType = 3; // PIXEL_MASK
              }
            } else {
              // Original logic for black dots on white
              if (brightness <= BLACK_THRESHOLD) {
                pixelType = 1; // PIXEL_SOLID
              } else if (brightness > WHITE_THRESHOLD) {
                pixelType = 3; // PIXEL_MASK
              }
            }
            
            results[j] = pixelType;
          }
          
          // Yield control back to main thread periodically
          if (i % (batchSize * 10) === 0) {
            self.postMessage({ type: 'progress', processed: i, total: brightnessMap.length });
          }
        }
        
        self.postMessage({ type: 'complete', results: results });
      }
    };
  `;
  
  const blob = new Blob([workerCode], { type: 'application/javascript' });
  return new Worker(URL.createObjectURL(blob));
};

// Optimized pixel classification with Web Worker support
async function classifyPixelsOptimized(brightnessMap, USE_WHITE_DOTS, BLACK_THRESHOLD, WHITE_THRESHOLD, progressCallback) {
  // Check if Web Workers are supported and if the image is large enough to benefit
  if (typeof Worker !== 'undefined' && brightnessMap.length > 10000) {
    try {
      // Use Web Worker for large images
      return new Promise((resolve, reject) => {
        const worker = createPixelProcessingWorker();
        let timeoutId = null;
        
        worker.onmessage = function(e) {
          if (e.data.type === 'progress') {
            if (progressCallback) {
              const progress = Math.round((e.data.processed / e.data.total) * 100);
              // Ensure progress is a valid number before calling callback
              if (typeof progress === 'number' && !isNaN(progress) && progress >= 0 && progress <= 100) {
                progressCallback({ stage: 'pixel-classification', progress: 30 + Math.floor(progress * 0.1) });
              }
            }
          } else if (e.data.type === 'complete') {
            // Clear timeout since worker completed successfully
            if (timeoutId) {
              clearTimeout(timeoutId);
              timeoutId = null;
            }
            worker.terminate();
            resolve(e.data.results);
          }
        };
        
        worker.onerror = function(error) {
          // Clear timeout since worker failed
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          worker.terminate();
          console.warn('Web Worker failed, falling back to main thread processing:', error);
          // Fall back to main thread processing
          resolve(classifyPixelsMainThread(brightnessMap, USE_WHITE_DOTS, BLACK_THRESHOLD, WHITE_THRESHOLD, progressCallback));
        };
        
        // Send data to worker
        worker.postMessage({
          type: 'classifyPixels',
          brightnessMap: brightnessMap,
          USE_WHITE_DOTS: USE_WHITE_DOTS,
          BLACK_THRESHOLD: BLACK_THRESHOLD,
          WHITE_THRESHOLD: WHITE_THRESHOLD
        });
        
        // Add timeout to prevent hanging
        timeoutId = setTimeout(() => {
          timeoutId = null;
          worker.terminate();
          console.warn('Web Worker timeout, falling back to main thread processing');
          resolve(classifyPixelsMainThread(brightnessMap, USE_WHITE_DOTS, BLACK_THRESHOLD, WHITE_THRESHOLD, progressCallback));
        }, 30000); // 30 second timeout
      });
    } catch (error) {
      console.warn('Web Worker creation failed, falling back to main thread processing:', error);
      return classifyPixelsMainThread(brightnessMap, USE_WHITE_DOTS, BLACK_THRESHOLD, WHITE_THRESHOLD, progressCallback);
    }
  } else {
    // Use main thread processing for smaller images or when Web Workers aren't available
    return classifyPixelsMainThread(brightnessMap, USE_WHITE_DOTS, BLACK_THRESHOLD, WHITE_THRESHOLD, progressCallback);
  }
}

// Main thread pixel classification fallback
async function classifyPixelsMainThread(brightnessMap, USE_WHITE_DOTS, BLACK_THRESHOLD, WHITE_THRESHOLD, progressCallback) {
  const pixelMap = new Uint8Array(brightnessMap.length);
  const batchSize = 1000;
  
  for (let i = 0; i < brightnessMap.length; i += batchSize) {
    const batchEnd = Math.min(i + batchSize, brightnessMap.length);
    
    // Process batch
    for (let j = i; j < batchEnd; j++) {
      const brightness = brightnessMap[j];
      
      if (USE_WHITE_DOTS) {
        // For white dots on black, invert the logic
        if (brightness >= 255 - BLACK_THRESHOLD) {
          pixelMap[j] = 1; // PIXEL_SOLID
        } else if (brightness < 255 - WHITE_THRESHOLD) {
          pixelMap[j] = 3; // PIXEL_MASK
        }
      } else {
        // Original logic for black dots on white
        if (brightness <= BLACK_THRESHOLD) {
          pixelMap[j] = 1; // PIXEL_SOLID
        } else if (brightness > WHITE_THRESHOLD) {
          pixelMap[j] = 3; // PIXEL_MASK
        }
      }
    }
    
    // Update progress every few batches
    if (i % (batchSize * 5) === 0 && progressCallback) {
      const progress = Math.round((i / brightnessMap.length) * 100);
      // Ensure progress is a valid number before calling callback
      if (typeof progress === 'number' && !isNaN(progress) && progress >= 0 && progress <= 100) {
        progressCallback({ stage: 'pixel-classification', progress: 30 + Math.floor(progress * 0.1) });
      }
    }
    
    // Yield control periodically
    if (i % (batchSize * 10) === 0) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }
  
  return pixelMap;
}

// Core halftone processing function
async function processHalftoneImage(imageFile, progressCallback, abortSignal) {
  return new Promise(async (resolve, reject) => {
    let isSettled = false;
    const safeResolve = (value) => {
      if (!isSettled) {
        isSettled = true;
        resolve(value);
      }
    };
    const safeReject = (error) => {
      if (!isSettled) {
        isSettled = true;
        reject(error);
      }
    };
    try {
      // Cooperative scheduling helpers
      let lastYieldTs = (typeof performance !== 'undefined' ? performance.now() : Date.now());
      const maybeYield = async () => {
        const now = (typeof performance !== 'undefined' ? performance.now() : Date.now());
        if (now - lastYieldTs > 8) {
          await new Promise(r => (typeof requestAnimationFrame !== 'undefined' ? requestAnimationFrame(r) : setTimeout(r, 0)));
          lastYieldTs = (typeof performance !== 'undefined' ? performance.now() : Date.now());
        }
      };
      const checkAbort = () => {
        if (abortSignal && abortSignal.aborted) {
          throw new DOMException('Aborted', 'AbortError');
        }
      };

      // Update progress
      if (progressCallback) {
        progressCallback({ stage: 'loading', progress: 0 });
      }
      
      // Convert file to image
      const imageUrl = URL.createObjectURL(imageFile);
      const img = new Image();
      
      if (abortSignal) {
        abortSignal.addEventListener('abort', () => {
          try { URL.revokeObjectURL(imageUrl); } catch (_) {}
          // Attempt to stop further processing quickly
          try { img.src = ''; } catch (_) {}
          safeReject(new DOMException('Aborted', 'AbortError'));
        }, { once: true });
      }

      img.onload = async () => {
        try {
          URL.revokeObjectURL(imageUrl);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'preparing', progress: 10 });
        }
        
        // Create reusable offscreen canvas for intermediate processing
        const sharedCanvas = document.createElement('canvas');
        sharedCanvas.width = FINAL_WIDTH;
        sharedCanvas.height = FINAL_HEIGHT;
        const sharedCtx = sharedCanvas.getContext('2d', { willReadFrequently: true });
        
        // Fill with black background
        sharedCtx.fillStyle = '#000000';
        sharedCtx.fillRect(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        
        // Calculate dimensions for centered image with margin
        let aspectRatio = img.width / img.height;
        let renderWidth = FINAL_WIDTH - (MARGIN * 2);
        let renderHeight = renderWidth / aspectRatio;
        
        if (renderHeight > FINAL_HEIGHT - (MARGIN * 2)) {
          renderHeight = FINAL_HEIGHT - (MARGIN * 2);
          renderWidth = renderHeight * aspectRatio;
        }
        
        // Calculate offsets: center horizontally, align to top with margin
        const offsetX = (FINAL_WIDTH - renderWidth) / 2;
        const offsetY = MARGIN; // Just use top margin instead of centering vertically
        
        // Draw image at the top with horizontal centering
        sharedCtx.drawImage(img, offsetX, offsetY, renderWidth, renderHeight);
        
        // Store original color image for final masking
        const originalColorCanvas = document.createElement('canvas');
        originalColorCanvas.width = FINAL_WIDTH;
        originalColorCanvas.height = FINAL_HEIGHT;
        const originalColorCtx = originalColorCanvas.getContext('2d', { willReadFrequently: true });
        originalColorCtx.drawImage(sharedCanvas, 0, 0);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'enhancing', progress: 20 });
        }
        
        // Get image data once and perform all modifications before putImageData
        let imageData = sharedCtx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        let data = imageData.data;
        
        // Apply saturation adjustment (+25)
        adjustSaturation(data, 25);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'grayscale', progress: 30 });
        }
        
        // Convert to grayscale on the same data buffer
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i], g = data[i+1], b = data[i+2];
          // Using standard luminosity formula
          const gray = Math.round(0.299*r + 0.587*g + 0.114*b);
          data[i] = data[i+1] = data[i+2] = gray;
        }
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'levels', progress: 40 });
        }
        
        // Apply Levels adjustment on the same data buffer
        photoshopLevels(data, 0, 0.78, 65, 0, 255);
        
        // Put modified image data only once after all modifications
        sharedCtx.putImageData(imageData, 0, 0);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'halftone', progress: 50 });
        }
        
        // Get processed grayscale data
        let sourceData = sharedCtx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT).data;
        
        // Create brightnessMap array to avoid repeated imageData.data access
        let brightnessMap = new Uint8Array(FINAL_WIDTH * FINAL_HEIGHT);
        for (let i = 0; i < sourceData.length; i += 4) {
          brightnessMap[i / 4] = sourceData[i]; // R channel for brightness
        }
        
        // Create integral image (summed-area table) for efficient local brightness calculation
        let integralImage = new Uint32Array((FINAL_WIDTH + 1) * (FINAL_HEIGHT + 1));
        
        /**
         * Build integral image (summed-area table) for O(1) local brightness calculation.
         * This replaces the inefficient O(n²) pixel-by-pixel sampling with constant-time
         * region sum calculations, significantly improving performance for large images.
         * The integral image stores cumulative sums allowing any rectangular region's
         * brightness sum to be calculated in constant time using 4 array lookups.
         */
        // Build integral image row by row
        for (let y = 0; y < FINAL_HEIGHT; y++) {
          let rowSum = 0;
          for (let x = 0; x < FINAL_WIDTH; x++) {
            const idx = y * FINAL_WIDTH + x;
            const brightness = brightnessMap[idx];
            rowSum += brightness;
            
            const integralIdx = (y + 1) * (FINAL_WIDTH + 1) + (x + 1);
            integralImage[integralIdx] = integralImage[y * (FINAL_WIDTH + 1) + (x + 1)] + rowSum;
          }
        }
        
        /**
         * Helper function to get sum of brightness in a rectangular region using integral image.
         * Time complexity: O(1) - constant time regardless of region size.
         * Uses the summed-area table to calculate region sums with 4 array lookups.
         */
        // Helper function to get sum of brightness in a rectangular region using integral image
        const getRegionBrightnessSum = (x1, y1, x2, y2) => {
          const x1_clamped = Math.max(0, x1);
          const y1_clamped = Math.max(0, y1);
          const x2_clamped = Math.min(FINAL_WIDTH - 1, x2);
          const y2_clamped = Math.min(FINAL_HEIGHT - 1, y2);
          
          const topLeft = integralImage[y1_clamped * (FINAL_WIDTH + 1) + x1_clamped];
          const topRight = integralImage[y1_clamped * (FINAL_WIDTH + 1) + (x2_clamped + 1)];
          const bottomLeft = integralImage[(y2_clamped + 1) * (FINAL_WIDTH + 1) + x1_clamped];
          const bottomRight = integralImage[(y2_clamped + 1) * (FINAL_WIDTH + 1) + (x2_clamped + 1)];
          
          return bottomRight - bottomLeft - topRight + topLeft;
        };
        
        /**
         * Helper function to get average brightness in a circular region using integral image approximation.
         * Time complexity: O(1) - constant time regardless of region size.
         * Approximates circular sampling using bounding square for performance optimization.
         * For more accuracy, true circular sampling could be implemented, but this provides excellent results.
         */
        // Helper function to get average brightness in a circular region using integral image approximation
        const getCircularRegionBrightness = (centerX, centerY, radius) => {
          const x1 = Math.floor(centerX - radius);
          const y1 = Math.floor(centerY - radius);
          const x2 = Math.floor(centerX + radius);
          const y2 = Math.floor(centerY + radius);
          
          const totalSum = getRegionBrightnessSum(x1, y1, x2, y2);
          const regionArea = (x2 - x1 + 1) * (y2 - y1 + 1);
          
          // Approximate circular region by using bounding square (this is a performance optimization)
          // For more accuracy, we could implement true circular sampling, but this provides good results
          return totalSum / regionArea;
        };
        

        
        // Progress throttling mechanism to reduce UI overhead
        let lastProgressUpdate = 0;
        let lastProgressValue = 0;
        const PROGRESS_THROTTLE_MS = 100; // Update progress at most every 100ms
        const PROGRESS_MIN_CHANGE = 1; // Minimum 1% change required for update
        
        /**
         * Throttled progress update function to reduce UI overhead and improve responsiveness.
         * Progress updates are limited to:
         * - Maximum frequency: every 100ms (PROGRESS_THROTTLE_MS)
         * - Minimum change: 1% progress difference (PROGRESS_MIN_CHANGE)
         * This ensures smooth UI updates without excessive callback overhead during intensive processing loops.
         */
        const throttledProgressUpdate = (stageOrProgress, progress) => {
          let stage, progressValue;
          
          // Handle both parameter styles: (stage, progress) or (progressObject)
          if (typeof stageOrProgress === 'object' && stageOrProgress !== null) {
            // Called with progress object: throttledProgressUpdate({ stage: 'x', progress: y })
            stage = stageOrProgress.stage;
            progressValue = stageOrProgress.progress;
          } else {
            // Called with separate parameters: throttledProgressUpdate('stage', progress)
            stage = stageOrProgress;
            progressValue = progress;
          }
          
          // Safety check: ensure progress is a valid number
          if (typeof progressValue !== 'number' || isNaN(progressValue) || progressValue < 0 || progressValue > 100) {
            console.warn('Invalid progress value:', progressValue, 'for stage:', stage, 'Parameters received:', { stageOrProgress, progress });
            return;
          }
          
          const now = Date.now();
          const progressChanged = Math.abs(progressValue - lastProgressValue) >= PROGRESS_MIN_CHANGE;
          
          if (progressCallback && (now - lastProgressUpdate >= PROGRESS_THROTTLE_MS || progressChanged)) {
            progressCallback({ stage, progress: progressValue });
            lastProgressUpdate = now;
            lastProgressValue = progressValue;
          }
        };
        
        // Create a pixel classification map using optimized processing
        let pixelMap = await classifyPixelsOptimized(
          brightnessMap, 
          USE_WHITE_DOTS, 
          BLACK_THRESHOLD, 
          WHITE_THRESHOLD, 
          throttledProgressUpdate
        );
        
        // Reset shared canvas for halftone processing
        sharedCanvas.width = FINAL_WIDTH;
        sharedCanvas.height = FINAL_HEIGHT;
        const halftoneCtx = sharedCanvas.getContext('2d', { willReadFrequently: true });
        
        // Disable anti-aliasing
        halftoneCtx.imageSmoothingEnabled = false;
        
        // Fill with background color
        if (USE_WHITE_DOTS) {
          halftoneCtx.fillStyle = '#000000';
        } else {
          halftoneCtx.fillStyle = '#ffffff';
        }
        halftoneCtx.fillRect(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        
        // Draw solid areas using ImageData approach for better performance
        // Create ImageData object to set pixel values directly in the buffer
        const solidAreasImageData = halftoneCtx.createImageData(FINAL_WIDTH, FINAL_HEIGHT);
        const solidAreasData = solidAreasImageData.data;
        
        // Set background color for all pixels first
        const backgroundColor = USE_WHITE_DOTS ? 0 : 255; // Black for white dots, white for black dots
        for (let i = 0; i < solidAreasData.length; i += 4) {
          solidAreasData[i] = backgroundColor;     // R
          solidAreasData[i + 1] = backgroundColor; // G
          solidAreasData[i + 2] = backgroundColor; // B
          solidAreasData[i + 3] = 255;            // Alpha
        }
        
        // Set solid area pixels in the data buffer
        for (let y = 0; y < FINAL_HEIGHT; y++) {
          await periodicYield(y, 64);
          checkAbort();
          
          // Update progress every 100 rows using throttled mechanism
          if (y % 100 === 0) {
            const solidProgress = 50 + Math.floor((y / FINAL_HEIGHT) * 5);
            throttledProgressUpdate('solid-areas', solidProgress);
          }
          
          // Cache row offset calculation outside inner loop
          const rowOffset = y * FINAL_WIDTH;
          
          for (let x = 0; x < FINAL_WIDTH; x++) {
            if (pixelMap[rowOffset + x] === PIXEL_SOLID) {
              const pixelIndex = (y * FINAL_WIDTH + x) * 4;
              const solidColor = USE_WHITE_DOTS ? 255 : 0; // White for white dots, black for black dots
              
              // Set pixel color directly in the data buffer
              solidAreasData[pixelIndex] = solidColor;     // R
              solidAreasData[pixelIndex + 1] = solidColor; // G
              solidAreasData[pixelIndex + 2] = solidColor; // B
              solidAreasData[pixelIndex + 3] = 255;        // Alpha
            }
          }
        }
        
        // Render all solid areas at once using a single putImageData call
        halftoneCtx.putImageData(solidAreasImageData, 0, 0);
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'dots', progress: 60 });
        }
        
        // Set dot color
        if (USE_WHITE_DOTS) {
          halftoneCtx.fillStyle = '#ffffff';
        } else {
          halftoneCtx.fillStyle = '#000000';
        }
        
        // Track drawn dots using precise numeric keys for better performance
        const drawnDots = new Set();
        
        // Process each pixel for halftone dots
        for (let y = 0; y < FINAL_HEIGHT; y++) {
          // Update progress periodically using throttled mechanism
          if (y % 100 === 0) {
            const dotProgress = 60 + Math.floor((y / FINAL_HEIGHT) * 20);
            throttledProgressUpdate('dots', dotProgress);
          }
          
          await periodicYield(y, 32);
          checkAbort();
          
          // Cache row offset calculation outside inner loop
          const rowOffset = y * FINAL_WIDTH;
          
          for (let x = 0; x < FINAL_WIDTH; x++) {
            const pixelIdx = rowOffset + x;
            
            // Skip solid areas
            if (pixelMap[pixelIdx] === PIXEL_SOLID || pixelMap[pixelIdx] === PIXEL_MASK) continue;
            
            // Get rotated cell coordinates
            const cellCoords = getRotatedCellCoordinates(x, y, CELL_SIZE, HALFTONE_ANGLE);
            // Use precise coordinates to avoid key collisions while maintaining performance
            const cellKey = Math.round(cellCoords.centerX * 1000) + Math.round(cellCoords.centerY * 1000) * 1000000;
            
            // Skip if already processed
            if (drawnDots.has(cellKey)) continue;
            drawnDots.add(cellKey);
            
            const centerX = cellCoords.centerX;
            const centerY = cellCoords.centerY;
            
            // Skip if outside boundaries
            if (centerX < 0 || centerX >= FINAL_WIDTH || centerY < 0 || centerY >= FINAL_HEIGHT) continue;
            
            // Sample area for brightness using integral image for constant-time calculation
            const sampleRadius = Math.floor(CELL_SIZE / 2);
            const avgBrightness = getCircularRegionBrightness(centerX, centerY, sampleRadius);
            
            // Skip based on threshold
            if (USE_WHITE_DOTS) {
              if (avgBrightness < 255 - WHITE_THRESHOLD || avgBrightness >= 255 - BLACK_THRESHOLD) continue;
            } else {
              if (avgBrightness > WHITE_THRESHOLD) continue;
            }
            
            // Calculate dot size
            let normalizedSize;
            if (USE_WHITE_DOTS) {
              normalizedSize = avgBrightness / 255;
            } else {
              normalizedSize = 1 - (avgBrightness / 255);
            }
            
            // Enhance contrast
            normalizedSize = Math.pow(normalizedSize, 0.65);
            
            // Calculate radius
            const maxRadius = CELL_SIZE * 0.55;
            let radius = maxRadius * normalizedSize;
            
            // Skip tiny dots
            if (radius < 1.5) continue;
            
            // Draw dot
            drawCrispDot(halftoneCtx, centerX, centerY, radius);
          }
        }
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'masking', progress: 80 });
        }
        
        // Create final output canvas
        const finalCanvas = document.createElement('canvas');
        finalCanvas.width = FINAL_WIDTH;
        finalCanvas.height = FINAL_HEIGHT;
        const finalCtx = finalCanvas.getContext('2d', { willReadFrequently: true });
        
        // Get halftone pattern data
        const halftoneData = halftoneCtx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        let halftonePixels = halftoneData.data;
        
        // Get original color data
        const colorData = originalColorCtx.getImageData(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        const colorPixels = colorData.data;
        
        // Create final image data
        const finalData = finalCtx.createImageData(FINAL_WIDTH, FINAL_HEIGHT);
        const finalPixels = finalData.data;
        
        // Initialize boundary tracking variables
        let topEdge = FINAL_HEIGHT;
        let leftEdge = FINAL_WIDTH;
        let rightEdge = 0;
        let bottomEdge = 0;
        
        /**
         * Apply masking and track content boundaries simultaneously in a single pass.
         * This optimization eliminates the need for a separate bounding scan loop by
         * tracking topEdge, leftEdge, rightEdge, and bottomEdge during the masking process.
         * Performance improvement: O(n) instead of O(2n) where n = total pixels.
         */
        // Apply masking and track boundaries simultaneously in a single pass
        for (let i = 0; i < halftonePixels.length; i += 4) {
          const maskValue = halftonePixels[i];
          
          const isVisible = USE_WHITE_DOTS ? 
            (maskValue > 127) : 
            (maskValue <= 127);
          
          if (isVisible) {
            // Copy color with full alpha
            finalPixels[i] = colorPixels[i];         // R
            finalPixels[i + 1] = colorPixels[i + 1]; // G
            finalPixels[i + 2] = colorPixels[i + 2]; // B
            finalPixels[i + 3] = 255;                // Full alpha
            
            // Track boundaries during this loop (no separate scan needed)
            const pixelIndex = i / 4;
            const y = Math.floor(pixelIndex / FINAL_WIDTH);
            const x = pixelIndex % FINAL_WIDTH;
            
            if (y < topEdge) topEdge = y;
            if (y > bottomEdge) bottomEdge = y;
            if (x < leftEdge) leftEdge = x;
            if (x > rightEdge) rightEdge = x;
          } else {
            // Set transparent
            finalPixels[i] = 0;
            finalPixels[i + 1] = 0; 
            finalPixels[i + 2] = 0;
            finalPixels[i + 3] = 0;                  // Transparent
          }
        }
        
        // Put final image data
        finalCtx.putImageData(finalData, 0, 0);
        
        // Calculate content width and height using tracked boundaries
        const contentWidth = rightEdge - leftEdge;
        const contentHeight = bottomEdge - topEdge;
        const horizontalOffset = Math.max(0, Math.floor((FINAL_WIDTH - contentWidth) / 2));
        
        // Create a temporary canvas for the repositioned image
        const positionedCanvas = document.createElement('canvas');
        positionedCanvas.width = FINAL_WIDTH;
        positionedCanvas.height = FINAL_HEIGHT;
        const positionedCtx = positionedCanvas.getContext('2d');
        
        // Clear the canvas (make it transparent)
        positionedCtx.clearRect(0, 0, FINAL_WIDTH, FINAL_HEIGHT);
        
        // Draw the image content starting from the detected edges, with centering and top margin
        positionedCtx.drawImage(
          finalCanvas,
          leftEdge, topEdge,                    // Source x, y
          contentWidth, contentHeight,           // Source width, height
          horizontalOffset, MARGIN,              // Destination x, y
          contentWidth, contentHeight            // Destination width, height
        );
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'finalizing', progress: 90 });
        }
        
        // Convert to blob using the positioned canvas
        checkAbort();
        await maybeYield();
        const blob = await new Promise(resolve => {
          positionedCanvas.toBlob(blob => resolve(blob), 'image/png', 1.0);
        });
        
        // Add DPI metadata
        checkAbort();
        const finalBlob = await setDPI(blob, DPI);
        
        // Ensure the final blob has PNG MIME type
        checkAbort();
        const pngBlob = new Blob([await finalBlob.arrayBuffer()], {type: 'image/png'});
        
        // Update progress
        if (progressCallback) {
          progressCallback({ stage: 'complete', progress: 100 });
        }
        
        // Memory cleanup: set large objects to null and reset canvas dimensions
        sourceData = null;
        halftonePixels = null;
        pixelMap = null;
        brightnessMap = null;
        integralImage = null;
        drawnDots.clear();
        
        /**
         * Memory cleanup optimizations:
         * 1. Set large data arrays to null to release references and aid garbage collection
         * 2. Reset canvas dimensions to 0 to help browsers free GPU memory
         * 3. Clear Set objects to release memory
         * These optimizations are crucial for processing large images without memory leaks.
         */
        // Reset canvas dimensions to release GPU memory
        sharedCanvas.width = 0;
        sharedCanvas.height = 0;
        originalColorCanvas.width = 0;
        originalColorCanvas.height = 0;
        finalCanvas.width = 0;
        finalCanvas.height = 0;
        positionedCanvas.width = 0;
        positionedCanvas.height = 0;
        
          // Return processed image
          safeResolve(pngBlob);
        } catch (err) {
          safeReject(err);
        }
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(imageUrl);
        safeReject(new Error('Failed to load image'));
      };
      
      img.src = imageUrl;
    } catch (error) {
      safeReject(error);
    }
  });
}

// Apply adaptive compression to image blob
async function applyAdaptiveCompression(blob, applyCompression, abortSignal) {
  // Skip if compression not requested or blob size <= 10MB
  if (!applyCompression || blob.size <= 10485760) {
    if (window.SnapLogger) {
      window.SnapLogger.debug(`Skipping compression: Apply=${applyCompression}, Size=${blob.size} bytes`);
    }
    return blob;
  }
  
  try {
    if (window.SnapLogger) {
      window.SnapLogger.debug(`Starting compression: Original size=${blob.size} bytes`);
    }
    
    // Start with highest compression level
    let compressionLevel = 1024;
    let compressedBlob = null;
    let attempts = 0;
    const MAX_ATTEMPTS = 5;
    const checkAbort = () => {
      if (abortSignal && abortSignal.aborted) {
        throw new DOMException('Aborted', 'AbortError');
      }
    };
    const maybeYield = async () => {
      await new Promise(r => (typeof requestAnimationFrame !== 'undefined' ? requestAnimationFrame(r) : setTimeout(r, 0)));
    };
    
    while (attempts < MAX_ATTEMPTS) {
      attempts++;
      
      // Convert blob to arrayBuffer for UPNG processing
      checkAbort();
      const arrayBuffer = await blob.arrayBuffer();
      
      // Decode PNG using UPNG
      const pngData = UPNG.decode(arrayBuffer);
      
      // Get RGBA8 data
      const rgba8Data = UPNG.toRGBA8(pngData)[0];
      
      // Re-encode with compression
      checkAbort();
      const compressedData = UPNG.encode([rgba8Data], pngData.width, pngData.height, compressionLevel);
      
      // Create new blob from compressed data
      compressedBlob = new Blob([compressedData], { type: 'image/png' });
      
      console.log(`Compression attempt ${attempts} with level ${compressionLevel}, size: ${compressedBlob.size} bytes`);
      
      // Check if size is acceptable or we've reached minimum compression
      if (compressedBlob.size <= 20971520 || compressionLevel <= 128) {
        break;
      }
      
      // Reduce compression level for next attempt
      compressionLevel = Math.floor(compressionLevel / 2);
      await maybeYield();
    }
    
    // Re-apply DPI information to the compressed blob
    checkAbort();
    const finalBlob = await setDPI(compressedBlob, DPI);
    
    console.log(`Final compressed size: ${finalBlob.size} bytes (${Math.round(finalBlob.size / blob.size * 100)}% of original)`);
    return finalBlob;
  } catch (error) {
    console.error('Compression failed:', error);
    return blob; // Return original if compression fails
  }
}

// Process multiple files in a batch
async function processHalftoneBatch(files, options, progressCallback) {
  const results = [];
  const applyCompression = options?.applyCompression || false;
  const abortSignal = options?.abortSignal;
  
  for (let i = 0; i < files.length; i++) {
    try {
      // Update batch progress
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'starting',
          progress: 0
        });
      }
      
      // Process current file with progress updates
      const processedBlob = await processHalftoneImage(
        files[i],
        (progress) => {
          if (progressCallback) {
            progressCallback({
              file: {
                current: i + 1,
                total: files.length,
                name: files[i].name
              },
              ...progress
            });
          }
        },
        abortSignal
      );
      
      // Apply compression if needed and requested
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'compression',
          progress: 95
        });
      }
      
      const finalBlob = await applyAdaptiveCompression(processedBlob, applyCompression, abortSignal);
      
      // Add to results
      const result = {
        original: files[i],
        processed: finalBlob,
        name: files[i].name,
        success: true
      };
      
      results.push(result);
      
      // Final progress update for this file
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'complete',
          progress: 100
        });
      }
    } catch (error) {
      console.error(`Error processing file ${files[i].name}:`, error);
      
      // Add failed result
      results.push({
        original: files[i],
        processed: null,
        name: files[i].name,
        success: false,
        error: error.message
      });
      
      // Error progress update
      if (progressCallback) {
        progressCallback({
          file: {
            current: i + 1,
            total: files.length,
            name: files[i].name
          },
          stage: 'error',
          progress: 0,
          error: error.message
        });
      }
    }
  }
  
  return results;
}

// Export processor functions
window.halftoneProcessor = {
  processHalftoneImage,
  processHalftoneBatch,
  applyAdaptiveCompression
}; 