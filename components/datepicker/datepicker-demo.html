<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapDatepicker Demo - Custom Datepicker with Exact Design</title>
    
    <!-- Load the main CSS for design system -->
    <link rel="stylesheet" href="../../snapapp.css">
    <link rel="stylesheet" href="snap-datepicker.css">
    
    <style>
        body {
            padding: 40px;
            background: var(--bg-secondary);
            font-family: 'Amazon Ember', Arial, sans-serif;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: 14px;
            padding: 32px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding-bottom: 32px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .demo-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-accent);
            margin-bottom: 8px;
        }
        
        .demo-subtitle {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-accent);
            margin-bottom: 16px;
        }
        
        .demo-description {
            color: var(--text-primary);
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .demo-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .demo-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-accent);
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: var(--action-btn-bg);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .theme-toggle:hover {
            background: var(--action-btn-hover);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 32px;
            margin-top: 24px;
        }
        
        .comparison-item {
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-secondary);
        }
        
        .comparison-title {
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-accent);
        }
        
        .value-display {
            margin-top: 12px;
            padding: 8px 12px;
            background: var(--bg-secondary);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            color: var(--text-primary);
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Toggle Dark Mode</button>
    
    <div class="demo-container">
        <div class="demo-section">
            <h1 class="demo-title">SnapDatepicker Demo</h1>
            <p class="demo-description">
                Custom datepicker with exact design specifications featuring 424px container, 48px day cells,
                month/year dropdowns, and precise color matching. Includes dark/light theme support,
                keyboard navigation, and seamless integration with existing forms.
            </p>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-subtitle">Single Date Selection</h2>
            <p class="demo-description">
                Custom datepicker with exact design specifications: 424px container, month/year dropdowns,
                48px day cells with precise states (default, selected, hover, disabled).
            </p>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <label class="demo-label">Custom Datepicker</label>
                    <div id="custom-datepicker-1"></div>
                    <div class="value-display" id="custom-value-1">No date selected</div>
                </div>
                
                <div class="demo-item">
                    <label class="demo-label">With Placeholder</label>
                    <div id="custom-datepicker-2"></div>
                    <div class="value-display" id="custom-value-2">No date selected</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-subtitle">Date Range Selection</h2>
            <p class="demo-description">Date range picker for selecting start and end dates with automatic validation.</p>
            
            <div class="demo-item">
                <label class="demo-label">Date Range Picker</label>
                <div id="date-range-picker"></div>
                <div class="value-display" id="range-value">No range selected</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-subtitle">Comparison: Native vs Custom</h2>
            <p class="demo-description">Side-by-side comparison showing the difference between native and custom datepickers.</p>
            
            <div class="comparison-grid">
                <div class="comparison-item">
                    <div class="comparison-title">Native Date Input</div>
                    <input type="date" id="native-date" style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 6px;">
                    <div class="value-display" id="native-value">No date selected</div>
                </div>
                
                <div class="comparison-item">
                    <div class="comparison-title">Custom SnapDatepicker</div>
                    <div id="custom-comparison"></div>
                    <div class="value-display" id="custom-comparison-value">No date selected</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-subtitle">Integration Example</h2>
            <p class="demo-description">Example showing how the datepicker integrates with existing forms and maintains native input compatibility.</p>
            
            <form id="demo-form">
                <div class="demo-grid">
                    <div class="demo-item">
                        <label class="demo-label">Start Date</label>
                        <input type="date" name="startDate" id="form-start-date">
                    </div>
                    
                    <div class="demo-item">
                        <label class="demo-label">End Date</label>
                        <input type="date" name="endDate" id="form-end-date">
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <button type="button" onclick="showFormValues()" style="padding: 10px 20px; background: var(--action-btn-bg); color: white; border: none; border-radius: 6px; cursor: pointer;">
                        Show Form Values
                    </button>
                </div>
                
                <div class="value-display" id="form-values" style="margin-top: 16px;">Form values will appear here</div>
            </form>
        </div>
    </div>
    
    <!-- Load JavaScript -->
    <script src="snap-datepicker.js"></script>
    <script src="datepicker-integration.js"></script>
    
    <script>
        // Initialize custom datepickers
        document.addEventListener('DOMContentLoaded', function() {
            // Single datepickers
            const datepicker1 = new SnapDatepicker('#custom-datepicker-1', {
                placeholder: 'Select a date'
            });
            
            const datepicker2 = new SnapDatepicker('#custom-datepicker-2', {
                placeholder: 'Choose your date',
                value: '2024-01-15'
            });
            
            // Date range picker
            const rangePicker = new SnapDateRangePicker('#date-range-picker');
            
            // Comparison datepicker
            const comparisonPicker = new SnapDatepicker('#custom-comparison', {
                placeholder: 'Select a date'
            });
            
            // Event listeners for value display
            datepicker1.on('change', (value) => {
                document.getElementById('custom-value-1').textContent = value || 'No date selected';
            });
            
            datepicker2.on('change', (value) => {
                document.getElementById('custom-value-2').textContent = value || 'No date selected';
            });
            
            rangePicker.on('change', (values) => {
                const rangeText = values.start && values.end 
                    ? `${values.start} to ${values.end}`
                    : values.start 
                        ? `From ${values.start}`
                        : values.end 
                            ? `Until ${values.end}`
                            : 'No range selected';
                document.getElementById('range-value').textContent = rangeText;
            });
            
            comparisonPicker.on('change', (value) => {
                document.getElementById('custom-comparison-value').textContent = value || 'No date selected';
            });
            
            // Native date input listener
            document.getElementById('native-date').addEventListener('change', function() {
                document.getElementById('native-value').textContent = this.value || 'No date selected';
            });
            
            // Auto-replace form date inputs
            DatepickerIntegration.autoReplaceInContainer(document.getElementById('demo-form'));
        });
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
        }
        
        function showFormValues() {
            const formData = new FormData(document.getElementById('demo-form'));
            const values = {};
            for (let [key, value] of formData.entries()) {
                values[key] = value;
            }
            
            document.getElementById('form-values').textContent = JSON.stringify(values, null, 2);
        }
    </script>
</body>
</html>
