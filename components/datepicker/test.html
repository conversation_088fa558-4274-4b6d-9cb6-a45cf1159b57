<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapDatepicker Test - Updated Compact Design</title>
    
    <link rel="stylesheet" href="snap-datepicker.css">
    
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            padding: 40px;
            background: #111216;
            color: #FFFFFF;
            margin: 0;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #1A1D23;
            border-radius: 8px;
            border: 1px solid #2F3341;
        }
        
        .test-title {
            font-size: 24px;
            margin-bottom: 16px;
            color: #FFFFFF;
        }
        
        .test-item {
            margin-bottom: 20px;
        }
        
        .test-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #FFFFFF;
        }
        
        .value-display {
            margin-top: 8px;
            padding: 8px 12px;
            background: #2F3341;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            color: #FFFFFF;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: #470CED;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .theme-toggle:hover {
            background: #2A00A0;
        }
        
        /* Light theme overrides */
        [data-theme="light"] body {
            background: #F1F3F4;
            color: #1B1D21;
        }
        
        [data-theme="light"] .test-section {
            background: #FFFFFF;
            border-color: #DCE0E5;
        }
        
        [data-theme="light"] .test-title,
        [data-theme="light"] .test-label {
            color: #1B1D21;
        }
        
        [data-theme="light"] .value-display {
            background: #F1F3F4;
            color: #1B1D21;
        }
    </style>
</head>
<body data-theme="dark">
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Toggle Light Mode</button>
    
    <div class="test-container">
        <div class="test-section">
            <h1 class="test-title">SnapDatepicker Test</h1>
            <p>Testing the updated compact datepicker: 328px container, 40px day cells, existing snap-dropdown integration.</p>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">Basic Datepicker</h2>
            
            <div class="test-item">
                <label class="test-label">Default Datepicker</label>
                <div id="datepicker-1"></div>
                <div class="value-display" id="value-1">No date selected</div>
            </div>
            
            <div class="test-item">
                <label class="test-label">With Initial Value</label>
                <div id="datepicker-2"></div>
                <div class="value-display" id="value-2">No date selected</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">Date Range Picker</h2>
            
            <div class="test-item">
                <label class="test-label">Date Range Selection</label>
                <div id="daterange-1"></div>
                <div class="value-display" id="range-value-1">No range selected</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">Integration Test</h2>
            
            <div class="test-item">
                <label class="test-label">Native Input Replacement</label>
                <input type="date" id="native-input" value="2024-01-15">
                <div class="value-display" id="native-value">2024-01-15</div>
            </div>
        </div>
    </div>
    
    <script src="snap-datepicker.js"></script>
    <script src="datepicker-integration.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Test 1: Basic datepicker
            const datepicker1 = new SnapDatepicker('#datepicker-1', {
                placeholder: 'Select a date'
            });
            
            datepicker1.on('change', (value) => {
                document.getElementById('value-1').textContent = value || 'No date selected';
            });
            
            // Test 2: Datepicker with initial value
            const datepicker2 = new SnapDatepicker('#datepicker-2', {
                placeholder: 'Choose date',
                value: '2024-01-15'
            });
            
            datepicker2.on('change', (value) => {
                document.getElementById('value-2').textContent = value || 'No date selected';
            });
            
            // Test 3: Date range picker
            const rangePicker = new SnapDateRangePicker('#daterange-1');
            
            rangePicker.on('change', (values) => {
                const rangeText = values.start && values.end 
                    ? `${values.start} to ${values.end}`
                    : values.start 
                        ? `From ${values.start}`
                        : values.end 
                            ? `Until ${values.end}`
                            : 'No range selected';
                document.getElementById('range-value-1').textContent = rangeText;
            });
            
            // Test 4: Native input replacement
            const nativeInput = document.getElementById('native-input');
            DatepickerIntegration.replaceNativeInput(nativeInput);
            
            nativeInput.addEventListener('change', function() {
                document.getElementById('native-value').textContent = this.value || 'No date selected';
            });
        });
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            document.body.setAttribute('data-theme', newTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
        }
    </script>
</body>
</html>
