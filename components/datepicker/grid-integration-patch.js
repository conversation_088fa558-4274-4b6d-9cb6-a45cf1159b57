/**
 * Grid Integration Patch for SnapDatepicker
 * This file provides the necessary modifications to integrate custom datepickers
 * into the SnapGrid component, replacing native date inputs.
 * 
 * Usage: Include this file after snap-grid.js and snap-datepicker.js
 */

(function() {
    'use strict';

    // Store original SnapGrid prototype methods that we'll modify
    const originalCreateColumnMenu = SnapGrid.prototype.createColumnMenu;
    const originalUpdateFilterInputs = SnapGrid.prototype.updateFilterInputs;
    const originalToggleDropdownMenu = SnapGrid.prototype.toggleDropdownMenu;

    /**
     * Enhanced createColumnMenu with custom datepicker support
     */
    SnapGrid.prototype.createColumnMenu = function(column) {
        // Call original method
        const menu = originalCreateColumnMenu.call(this, column);
        
        // Add custom datepicker initialization for date columns
        if (column.type === 'date') {
            // Wait for menu to be added to DOM, then initialize datepickers
            setTimeout(() => {
                this.initializeDatepickersInMenu(menu, column);
            }, 0);
        }
        
        return menu;
    };

    /**
     * Initialize custom datepickers in column menu
     */
    SnapGrid.prototype.initializeDatepickersInMenu = function(menu, column) {
        const dateInputs = menu.querySelectorAll('input[type="date"]');

        dateInputs.forEach(input => {
            // Skip if already converted or converting
            if (input.dataset.converting === 'true' || input.dataset.converted === 'true' || input._snapDatepicker || input.style.display === 'none') {
                return;
            }
            input.dataset.converting = 'true';

            // Get properties before removing the native input
            const inputValue = input.value;
            const inputName = input.name;
            const inputId = input.id;
            const inputClassName = input.className;
            const parentNode = input.parentNode;

            // Create wrapper for the datepicker (completely invisible)
            const wrapper = document.createElement('div');
            wrapper.className = 'snap-grid-datepicker-wrapper';
            wrapper.style.cssText = 'position: absolute; width: 0; height: 0; overflow: hidden; opacity: 0; pointer-events: none;';

            // Create hidden input to maintain form functionality
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = inputName;
            hiddenInput.id = inputId;
            hiddenInput.value = inputValue;
            hiddenInput.className = inputClassName;
            hiddenInput.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important;';

            // Insert wrapper and hidden input before the native input
            parentNode.insertBefore(wrapper, input);
            parentNode.insertBefore(hiddenInput, input);

            // Remove the native input completely from DOM
            parentNode.removeChild(input);

            // Determine placeholder based on original input
            let placeholder = 'Select date';
            if (input.placeholder) {
                placeholder = input.placeholder;
            } else if (inputClassName.includes('first-filter-input')) {
                placeholder = 'From date';
            } else if (inputClassName.includes('second-filter-input')) {
                placeholder = 'To date';
            }

            // Create custom datepicker in picker-only mode (no input)
            const datepicker = new SnapDatepicker(wrapper, {
                value: inputValue,
                placeholder: placeholder,
                closeOnSelect: true
                // withInput: false is default - no input created
            });

            // Create a trigger button to open the datepicker
            const triggerBtn = document.createElement('button');
            triggerBtn.type = 'button';
            triggerBtn.className = inputClassName + ' snap-datepicker-trigger';
            triggerBtn.style.cssText = 'width: 100%; padding: 8px 32px 8px 8px; border: 1px solid var(--border-color); border-radius: 4px; background: var(--bg-primary); color: var(--text-primary); font-size: 12px; text-align: left; cursor: pointer;';
            triggerBtn.textContent = inputValue || placeholder;

            // Insert trigger button where the original input was
            parentNode.insertBefore(triggerBtn, wrapper);

            // Open datepicker when trigger is clicked
            triggerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // Close any other open datepickers first
                this.closeAllDatepickers();

                const rect = triggerBtn.getBoundingClientRect();
                // Pass the trigger button as follow element for scroll tracking
                datepicker.openAt(rect.left, rect.bottom + 4, triggerBtn);
            });

            // Sync values and events with hidden input
            datepicker.on('change', (value) => {
                hiddenInput.value = value;
                triggerBtn.textContent = value || placeholder;

                // Trigger change event on hidden input
                const changeEvent = new Event('change', { bubbles: true });
                hiddenInput.dispatchEvent(changeEvent);

                // Update filter if this is a filter input
                if (hiddenInput.classList.contains('filter-input')) {
                    this.handleFilterInputChange(column, hiddenInput);
                }
            });

            // Store reference and finalize flags on hidden input
            hiddenInput._snapDatepicker = datepicker;
            hiddenInput._snapDatepickerTrigger = triggerBtn;
            hiddenInput.dataset.converted = 'true';
        });
    };

    /**
     * Enhanced updateFilterInputs with custom datepicker support
     */
    SnapGrid.prototype.updateFilterInputs = function(column, selectedOperator, firstInput, secondInput, firstFilterWrapper, secondFilterWrapper, logicSection, secondDropdown) {
        // Call original method first
        if (originalUpdateFilterInputs) {
            originalUpdateFilterInputs.call(this, column, selectedOperator, firstInput, secondInput, firstFilterWrapper, secondFilterWrapper, logicSection, secondDropdown);
        }

        const isDateOnly = column.type === 'date';
        
        if (isDateOnly) {
            // Convert native date inputs to custom datepickers
            setTimeout(() => {
                this.convertDateInputsToCustom(firstInput, secondInput, selectedOperator);
            }, 0);
        }
    };

    /**
     * Convert native date inputs to custom datepickers
     */
    SnapGrid.prototype.convertDateInputsToCustom = function(firstInput, secondInput, selectedOperator) {
        // Convert first input if it's a date type
        if (firstInput && firstInput.type === 'date' && !firstInput._snapDatepicker && firstInput.dataset.converted !== 'true') {
            this.convertSingleDateInput(firstInput);
        }

        // Convert second input if it's a date type and visible
        if (secondInput && secondInput.type === 'date' && !secondInput._snapDatepicker && secondInput.dataset.converted !== 'true' &&
            secondInput.parentElement.style.display !== 'none') {
            this.convertSingleDateInput(secondInput);
        }
    };

    /**
     * Convert a single date input to custom datepicker
     */
    SnapGrid.prototype.convertSingleDateInput = function(input) {
        if (!input || input._snapDatepicker) return;

        // Get properties before removing the native input
        const inputValue = input.value;
        const inputName = input.name;
        const inputId = input.id;
        const inputClassName = input.className;
        const inputPlaceholder = input.placeholder || 'Select date';
        const parentNode = input.parentNode;

        // Create wrapper (invisible)
        const wrapper = document.createElement('div');
        wrapper.className = 'snap-grid-datepicker-wrapper';
        wrapper.style.cssText = 'position: absolute; width: 0; height: 0; overflow: hidden; opacity: 0; pointer-events: none;';

        // Create hidden input to maintain form functionality
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = inputName;
        hiddenInput.id = inputId;
        hiddenInput.value = inputValue;
        hiddenInput.className = inputClassName;
        hiddenInput.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important;';

        // Insert wrapper and hidden input before the native input
        parentNode.insertBefore(wrapper, input);
        parentNode.insertBefore(hiddenInput, input);

        // Create a trigger button to open the datepicker
        const triggerBtn = document.createElement('button');
        triggerBtn.type = 'button';
        triggerBtn.className = inputClassName + ' snap-datepicker-trigger';
        triggerBtn.style.cssText = 'width: 100%; padding: 8px 32px 8px 8px; border: 1px solid var(--border-color); border-radius: 4px; background: var(--bg-primary); color: var(--text-primary); font-size: 12px; text-align: left; cursor: pointer;';
        triggerBtn.textContent = inputValue || inputPlaceholder;

        // Insert trigger button where the original input was
        parentNode.insertBefore(triggerBtn, input);

        // Remove the native input completely from DOM
        parentNode.removeChild(input);

        // Create datepicker in picker-only mode (no input)
        const datepicker = new SnapDatepicker(wrapper, {
            value: inputValue,
            placeholder: inputPlaceholder,
            closeOnSelect: true
            // withInput: false is default - no input created
        });

        // Open datepicker when trigger is clicked
        triggerBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Close any other open datepickers first
            this.closeAllDatepickers();

            const rect = triggerBtn.getBoundingClientRect();
            // Pass the trigger button as follow element for scroll tracking
            datepicker.openAt(rect.left, rect.bottom + 4, triggerBtn);
        });

        // Sync values with hidden input
        datepicker.on('change', (value) => {
            hiddenInput.value = value;
            triggerBtn.textContent = value || inputPlaceholder;
            const changeEvent = new Event('change', { bubbles: true });
            hiddenInput.dispatchEvent(changeEvent);
        });

        // Store reference on hidden input
        hiddenInput._snapDatepicker = datepicker;
        hiddenInput._snapDatepickerTrigger = triggerBtn;
    };

    /**
     * Handle filter input changes for custom datepickers
     */
    SnapGrid.prototype.handleFilterInputChange = function(column, input) {
        // This method can be called when datepicker values change
        // to trigger filter updates in the grid
        console.log('📅 Custom datepicker filter change:', {
            column: column.field,
            value: input.value
        });
    };

    /**
     * Close all open datepickers in the grid
     */
    SnapGrid.prototype.closeAllDatepickers = function() {
        const container = this.container || document;
        const hiddenInputs = container.querySelectorAll('input[type="hidden"][data-converted="true"]');

        hiddenInputs.forEach(input => {
            if (input._snapDatepicker && input._snapDatepicker.isOpen) {
                input._snapDatepicker.close();
            }
        });
    };

    /**
     * Cleanup method for custom datepickers
     */
    SnapGrid.prototype.cleanupCustomDatepickers = function(container) {
        const dateInputs = container.querySelectorAll('input[type="date"]');

        dateInputs.forEach(input => {
            if (input._snapDatepicker) {
                input._snapDatepicker.destroy();
                input.style.display = '';
                delete input._snapDatepicker;
            }
        });
    };

    /**
     * Enhanced toggleDropdownMenu to close datepickers when other dropdowns open
     */
    if (originalToggleDropdownMenu) {
        SnapGrid.prototype.toggleDropdownMenu = function(...args) {
            // Close any open datepickers before opening dropdown menus
            this.closeAllDatepickers();

            // Call original method
            return originalToggleDropdownMenu.apply(this, args);
        };
    }

    // Add CSS for grid-specific datepicker styling
    const gridDatepickerCSS = `
        .snap-grid-datepicker-wrapper {
            width: 100%;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-input {
            font-size: 12px;
            padding: 8px 32px 8px 8px;
            height: 32px;
            box-sizing: border-box;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-icon {
            right: 8px;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-dropdown {
            font-size: 12px;
            z-index: 10000;
        }
        
        /* Ensure datepicker dropdowns appear above grid menus */
        .snap-grid .snap-datepicker-dropdown {
            z-index: 10001;
        }
    `;

    // Inject CSS
    const style = document.createElement('style');
    style.textContent = gridDatepickerCSS;
    document.head.appendChild(style);

    // Auto-initialize when SnapGrid is used
    const originalSnapGridConstructor = window.SnapGrid;
    
    window.SnapGrid = function(container, options) {
        // Call original constructor
        const instance = new originalSnapGridConstructor(container, options);
        
        // Add initialization hook for custom datepickers
        const originalRender = instance.render;
        instance.render = function() {
            const result = originalRender.call(this);
            
            // Initialize custom datepickers after render
            setTimeout(() => {
                if (window.SnapDatepicker) {
                    this.initializeCustomDatepickers();
                }
            }, 100);
            
            return result;
        };
        
        // Add method to initialize custom datepickers
        instance.initializeCustomDatepickers = function() {
            const container = this.container;
            const dateInputs = container.querySelectorAll('input[type="date"]:not([data-converted])');

            dateInputs.forEach(input => {
                if (!input._snapDatepicker && input.dataset.converting !== 'true') {
                    DatepickerIntegration.replaceNativeInput(input);
                    input.setAttribute('data-converted', 'true');
                }
            });
        };
        
        return instance;
    };

    // Copy static properties
    Object.setPrototypeOf(window.SnapGrid, originalSnapGridConstructor);
    Object.assign(window.SnapGrid, originalSnapGridConstructor);

    console.log('✅ SnapGrid custom datepicker integration loaded');

})();
