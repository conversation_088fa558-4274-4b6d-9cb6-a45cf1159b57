<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snap Charts - Stacked Column Test</title>
    
    <!-- Load existing project fonts and CSS variables -->
    <link rel="stylesheet" href="../../snapapp.css">
    
    <!-- Load chart-specific CSS -->
    <link rel="stylesheet" href="snap-charts.css">
    
    <style>
        /* Test page specific styles */
        body {
            margin: 0;
            padding: 40px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', Arial, sans-serif;
            min-height: 100vh;
            /* Override main app CSS that prevents scrolling */
            overflow-y: auto !important;
            overflow-x: auto !important;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .test-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-accent);
            margin: 0 0 8px 0;
        }
        
        .test-subtitle {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }
        
        .controls-section {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 32px;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: 1.5px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Amazon Ember', sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--theme-transition);
        }
        
        .control-btn:hover {
            background: var(--btn-hover);
            border-color: var(--action-btn-bg);
        }
        
        .control-btn.active {
            background: var(--action-btn-bg);
            color: var(--action-btn-text);
            border-color: var(--action-btn-bg);
        }
        
        .chart-container {
            width: 100%;
            max-width: 1064px;
            margin: 0 auto;
        }
        
        .demo-info {
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            margin-top: 32px;
        }
        
        .demo-info h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-accent);
        }
        
        .demo-info p {
            margin: 0 0 12px 0;
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-primary);
        }
        
        .demo-info ul {
            margin: 12px 0;
            padding-left: 20px;
        }
        
        .demo-info li {
            margin: 8px 0;
            font-size: 14px;
            color: var(--text-primary);
        }
        
        .code-example {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        

        
        @media (max-width: 768px) {
            body {
                padding: 20px;
            }
            
            .controls-section {
                flex-wrap: wrap;
            }
            
            .test-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1 class="test-title">Snap Charts - Chart Library Test</h1>
            <p class="test-subtitle">Interactive chart system with theme support, animations, and scrolling</p>
        </div>
        
        <!-- Controls -->
        <div class="controls-section">
            <button class="control-btn active" id="lightTheme">Light Theme</button>
            <button class="control-btn" id="darkTheme">Dark Theme</button>
            <button class="control-btn" id="updateData">Update Data</button>
            <button class="control-btn" id="toggleAnimation">Toggle Animation</button>
            <button class="control-btn" id="toggleCompare">Enable Compare</button>
        </div>
        
        <!-- Chart Containers -->
        <div class="chart-container" id="chartContainer1">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 700; color: var(--text-accent);">Standard Stacked Column Chart</h2>
        </div>
        
        <div class="chart-container" id="chartContainer2">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 700; color: var(--text-accent);">Scrollable Stacked Column Chart</h2>
        </div>
        
        <div class="chart-container" id="chartContainer3">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 700; color: var(--text-accent);">Daily Sales History Chart</h2>
        </div>
        
        <div class="chart-container" id="chartContainer4">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 700; color: var(--text-accent);">Pie Chart</h2>
        </div>
        

        
        <!-- Demo Information -->
        <div class="demo-info">
            <h3>🎯 Features Demonstrated</h3>
            <p>This chart library includes four different chart types with advanced interactive features:</p>
            <ul>
                <li><strong>SVG Rendering:</strong> Crisp, scalable graphics that look perfect at any size</li>
                <li><strong>Theme Integration:</strong> Automatically adapts to light/dark themes using CSS variables</li>
                <li><strong>Interactive Elements:</strong> Hover tooltips, dual-handle date range slider, and smooth animations</li>
                <li><strong>Responsive Design:</strong> Adapts to different screen sizes while maintaining proportions</li>
                <li><strong>All-Time Data Support:</strong> Handles years of historical data with efficient filtering</li>
                <li><strong>Dynamic Column Width:</strong> Columns automatically adjust based on selected date range</li>
                <li><strong>Accessibility:</strong> Proper ARIA labels and keyboard navigation support</li>
            </ul>
            
            <h3>📊 Chart Components</h3>
            <ul>
                <li><strong>Header:</strong> Title, subtitle, marketplace dropdown, and "View Insights" button</li>
                <li><strong>Dual Y-Axes:</strong> Left axis shows sales units, right axis shows royalties ($)</li>
                <li><strong>Grid Lines:</strong> 6 horizontal dashed lines for easy value reading</li>
                <li><strong>Stacked Columns:</strong> Fixed 20px width, 7 color segments with rounded top/bottom</li>
                <li><strong>Single Columns:</strong> Dynamic width (min 1px) for daily sales history</li>
                <li><strong>Royalties Line:</strong> Green curved line with dots showing royalties trend</li>
                <li><strong>Compare Mode:</strong> Gray 10px columns and dashed line for previous period</li>
                <li><strong>Dual-Handle Date Range Slider:</strong> Drag left/right handles to filter date ranges</li>
                <li><strong>All-Time Data:</strong> 3+ years of historical sales data with real-time filtering</li>
                <li><strong>Labels:</strong> Month/day labels below, sales values in tooltips only for daily history</li>
                <li><strong>Pie Chart:</strong> Donut-style pie chart with smooth animations and interactive tooltips</li>
            </ul>
            
            <h3>🔧 Usage Example</h3>
            <div class="code-example">
const chart = new SnapChart({
    container: '#chartContainer',
    type: 'stacked-column',
    data: chartData,
    options: {
        title: 'Last Week\'s Sales',
        subtitle: 'June 4, 2025 to June 10, 2025',
        icon: '📊',
        animate: true,
        responsive: true
    }
});
            </div>
        </div>
    </div>

    <!-- Load Chart JavaScript -->
    <script src="dummy-data.js"></script>
    <script src="data-loader.js"></script>
    <script src="snap-charts.js"></script>
    
    <script>
        // Main async function to handle data loading
        async function initializeCharts() {
            // Generate segment data for stacked columns
        function generateSegmentData(totalSales, columnIndex) {
            // Use column index to determine segment count (1-7) in a predictable pattern
            const segmentCount = ((columnIndex % 7) + 1); // This gives us 1, 2, 3, 4, 5, 6, 7, 1, 2, 3...
            
            // Create array of segment values that add up to totalSales
            const values = [];
            let remainingValue = totalSales;
            
            if (segmentCount === 1) {
                // Single segment gets all the value
                values.push(totalSales);
            } else {
                // Generate values for all segments except the last one
                for (let i = 0; i < segmentCount - 1; i++) {
                    // Use a predictable distribution based on segment index
                    const percentage = (0.8 - (i * 0.1)) / (segmentCount - 1);
                    const value = Math.round(remainingValue * percentage);
                    values.push(Math.max(1, value));
                    remainingValue -= value;
                }
                
                // Add the remaining value as the last segment
                values.push(Math.max(1, remainingValue));
            }
            
            // Get the corresponding labels (only for the segments we're using)
            const allLabels = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
            const labels = allLabels.slice(0, segmentCount);
            
            // Determine which marketplace should have 0 returns (cycle through marketplaces)
            const zeroReturnsMarketplaceIndex = columnIndex % segmentCount;
            
            return { values, labels, zeroReturnsMarketplaceIndex };
        }

        // Generate realistic royalties that vary instead of being a simple 2X multiplier
        function generateRealisticRoyalties(sales, index, dataSetOffset = 0) {
            // Create varied multipliers between 0.3 and 1.5 (instead of fixed 2X)
            const baseMultipliers = [
                0.45, 0.8, 0.35, 1.2, 0.6, 1.4, 0.5, 0.9, 0.3, 1.1, 
                0.7, 1.3, 0.4, 1.0, 0.85, 0.55, 1.25, 0.65, 0.75, 0.95
            ];
            
            // Use index + dataSetOffset to get different patterns for different datasets
            const multiplierIndex = (index + dataSetOffset) % baseMultipliers.length;
            const baseMultiplier = baseMultipliers[multiplierIndex];
            
            // Add some additional randomness (±15%) to make it feel more organic
            const randomVariation = 0.85 + (Math.sin(index * 2.7 + dataSetOffset) * 0.15);
            const finalMultiplier = baseMultiplier * randomVariation;
            
            // Calculate royalties and round to reasonable dollar amounts
            const royalties = Math.round(sales * finalMultiplier);
            
            // Ensure minimum royalty of $5 for any sale
            return Math.max(5, royalties);
        }

        // Sample data for Stacked Columns with Compare (Monthly, Current Year 2025)
        const sampleData1 = Array.from({length: 12}, (_, i) => {
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            const month = months[i];
            
            // Create varied, random-looking pattern using multiple factors
            // Mix of high, medium, and low values in unpredictable sequence
            const randomFactors = [0.8, 0.2, 0.1, 0.9, 0.3, 0.7, 0.4, 0.95, 0.15, 0.6, 0.25, 0.85];
            const salesRange = 2500 - 500; // 2000 (monthly sales range)
            const sales = Math.round(500 + (salesRange * randomFactors[i]));
            
            // Generate realistic varied royalties (no longer 2X sales)
            const royalties = generateRealisticRoyalties(sales, i, 0);
            
            // Generate segment data
            const segmentData = generateSegmentData(sales, i);
            
            // Calculate actual returns from individual marketplace data
            const individualReturns = segmentData.values.map((value, index) => {
                return (index === segmentData.zeroReturnsMarketplaceIndex) ? 0 : Math.round(value * 0.1);
            });
            const returns = individualReturns.reduce((sum, returns) => sum + returns, 0);

            return {
                month,
                year: '25', // Current year 2025
                sales,
                royalties,
                returns,
                values: segmentData.values,
                labels: segmentData.labels,
                zeroReturnsMarketplaceIndex: segmentData.zeroReturnsMarketplaceIndex
            };
        });
        
        // Alternative data for testing updates (Different year 2024) - different random pattern
        const sampleData2 = Array.from({length: 12}, (_, i) => {
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            const month = months[i];
            
            // Different varied pattern for 2024 - different random sequence
            const randomFactors = [0.3, 0.9, 0.6, 0.1, 0.8, 0.2, 0.95, 0.4, 0.7, 0.15, 0.85, 0.5];
            const salesRange = 2500 - 500; // 2000 (monthly sales range)
            const sales = Math.round(500 + (salesRange * randomFactors[i]));
            
            // Generate realistic varied royalties (no longer 2X sales)
            const royalties = generateRealisticRoyalties(sales, i, 100);
            
            // Generate segment data
            const segmentData = generateSegmentData(sales, i);
            
            // Calculate actual returns from individual marketplace data
            const individualReturns = segmentData.values.map((value, index) => {
                return (index === segmentData.zeroReturnsMarketplaceIndex) ? 0 : Math.round(value * 0.1);
            });
            const returns = individualReturns.reduce((sum, returns) => sum + returns, 0);

            return {
                month,
                year: '24', // Previous year 2024
                sales,
                royalties,
                returns,
                values: segmentData.values,
                labels: segmentData.labels,
                zeroReturnsMarketplaceIndex: segmentData.zeroReturnsMarketplaceIndex
            };
        });
        
        // Generate comparison data with varied marketplace patterns for realistic testing
        function generateComparisonSegmentData(totalSales, columnIndex) {
            // Extended marketplace pool for comparison data
            const allMarketplaces = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP', 'Walmart', 'TikTok', 'Shopify'];

            // Randomly decide how many marketplaces to include (3-7 for comparison)
            const segmentCount = Math.floor(Math.random() * 5) + 3;

            // Randomly select which marketplaces are active
            const shuffledMarketplaces = [...allMarketplaces].sort(() => 0.5 - Math.random());
            const selectedMarketplaces = shuffledMarketplaces.slice(0, segmentCount);

            // Filter to only include standard marketplaces that exist in our base set
            const standardMarketplaces = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
            const activeMarketplaces = selectedMarketplaces.filter(mp => standardMarketplaces.includes(mp));

            // If no standard marketplaces were selected, ensure at least 2-3 are included
            if (activeMarketplaces.length === 0) {
                const minMarketplaces = Math.floor(Math.random() * 2) + 2; // 2-3 marketplaces
                const shuffledStandard = [...standardMarketplaces].sort(() => 0.5 - Math.random());
                activeMarketplaces.push(...shuffledStandard.slice(0, minMarketplaces));
            }

            // Generate sales distribution for active marketplaces
            const values = [];
            const labels = [];

            // Initialize all standard marketplaces with 0
            standardMarketplaces.forEach(marketplace => {
                if (activeMarketplaces.includes(marketplace)) {
                    // Generate random sales for active marketplace
                    const marketplaceShare = Math.random() * 0.4 + 0.1; // 10-50% share
                    const marketplaceSales = Math.round(totalSales * marketplaceShare);
                    values.push(marketplaceSales);
                } else {
                    values.push(0);
                }
                labels.push(marketplace);
            });

            // Normalize values to match total sales approximately
            const currentTotal = values.reduce((sum, val) => sum + val, 0);
            if (currentTotal > 0) {
                const scaleFactor = totalSales / currentTotal;
                for (let i = 0; i < values.length; i++) {
                    if (values[i] > 0) {
                        values[i] = Math.round(values[i] * scaleFactor);
                    }
                }
            }

            // Determine which marketplace should have 0 returns (cycle through active marketplaces)
            const activeIndices = values.map((val, idx) => val > 0 ? idx : -1).filter(idx => idx !== -1);
            const zeroReturnsMarketplaceIndex = activeIndices.length > 0 ?
                activeIndices[columnIndex % activeIndices.length] : 0;

            return {
                values,
                labels,
                zeroReturnsMarketplaceIndex,
                activeMarketplaceCount: activeMarketplaces.length,
                selectedMarketplaces: activeMarketplaces
            };
        }

        // Generate comparison data for scrollable chart (120 months, previous year data)
        const extendedComparisonData = Array.from({length: 120}, (_, i) => {
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            const startYear = 2015; // One year before main data (2016-2025)
            const currentYear = startYear + Math.floor(i / 12);
            const monthIndex = i % 12;
            const month = months[monthIndex];

            // Generate comparison sales (slightly different pattern than main data)
            const baseValue = 800 + (Math.sin(i * 0.1) * 400) + (Math.random() * 600);
            let sales = Math.round(baseValue);

            // Add some seasonal variation
            if (monthIndex === 11) { // December
                sales = Math.round(sales * 1.4); // Holiday boost
            } else if (monthIndex === 0 || monthIndex === 1) { // Jan/Feb
                sales = Math.round(sales * 0.8); // Post-holiday dip
            }

            // Generate realistic royalties
            const royalties = generateRealisticRoyalties(sales, i, 250);

            // Generate comparison segment data
            const segmentData = generateComparisonSegmentData(sales, i);

            return {
                sales: sales,
                royalties: royalties,
                month: month,
                year: currentYear.toString(),
                monthDay: `${month} ${Math.floor(Math.random() * 28) + 1}`,
                values: segmentData.values,
                labels: segmentData.labels,
                returns: segmentData.values.map(v => Math.round(v * 0.1))
            };
        });

        // Comparison data (previous year 2024) - with varied marketplace patterns and independent random values
        const comparisonData1 = Array.from({length: 12}, (_, i) => {
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            const month = months[i];

            // Independent random pattern for comparison data (different from main data)
            const comparisonRandomFactors = [0.3, 0.8, 0.5, 0.9, 0.2, 0.7, 0.4, 0.85, 0.6, 0.35, 0.75, 0.45];
            const salesRange = 2800 - 400; // 2400 (different range than main data)
            let sales = Math.round(400 + (salesRange * comparisonRandomFactors[i]));

            // Add some variability with occasional higher peaks
            if (Math.random() < 0.25) { // 25% chance of higher sales
                sales = Math.round(sales * (1.2 + Math.random() * 0.5)); // 20-70% increase
            }

            // Generate realistic varied royalties
            const royalties = generateRealisticRoyalties(sales, i, 300);

            // Generate comparison segment data with varied marketplace patterns
            const segmentData = generateComparisonSegmentData(sales, i);

            // Calculate actual returns from individual marketplace data (12% instead of 10%)
            const individualReturns = segmentData.values.map((value, index) => {
                return (index === segmentData.zeroReturnsMarketplaceIndex) ? 0 : Math.round(value * 0.12);
            });
            const returns = individualReturns.reduce((sum, returns) => sum + returns, 0);

            return {
                month,
                year: '24', // Previous year 2024 for comparison
                sales,
                royalties,
                returns, // Returns for comparison columns
                values: segmentData.values,
                labels: segmentData.labels,
                zeroReturnsMarketplaceIndex: segmentData.zeroReturnsMarketplaceIndex,
                activeMarketplaceCount: segmentData.activeMarketplaceCount,
                selectedMarketplaces: segmentData.selectedMarketplaces
            };
        });
        
        let currentData = sampleData1;
        let animationEnabled = true;
        let compareMode = false;
        
        // Generate extended data for scrollable chart (All time: 2016-2025, 120 months)
        const extendedData = Array.from({length: 120}, (_, i) => {
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            const startYear = 2016;
            const currentYear = startYear + Math.floor(i / 12);
            const monthIndex = i % 12;
            const month = months[monthIndex];
            const year = currentYear.toString().slice(-2); // Last 2 digits
            
            // Create growth pattern over time (business growing from 2016 to 2025)
            const yearProgress = (currentYear - startYear) / (2025 - startYear); // 0 to 1
            const growthMultiplier = 0.3 + (yearProgress * 1.7); // Grows from 0.3 to 2.0
            
            // Seasonal patterns that repeat yearly
            const seasonalMultipliers = [0.7, 0.6, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.1, 0.9, 1.4, 1.8]; // Holiday season boost
            const seasonalMultiplier = seasonalMultipliers[monthIndex];
            
            // Add randomness to make it realistic
            const randomFactor = 0.6 + (Math.random() * 0.8); // 0.6 to 1.4
            
            // Base sales between 200 and 1500, then apply multipliers
            const baseSales = 200 + (Math.random() * 1300);
            let sales = Math.round(baseSales * growthMultiplier * seasonalMultiplier * randomFactor);
            
            // Special high sales months (Black Friday, Christmas, etc.)
            if (monthIndex === 10 || monthIndex === 11) { // November/December
                sales = Math.round(sales * 1.5); // 50% boost for holiday season
            }
            
            // Special case: Make one specific month have very high sales
            if (currentYear === 2023 && monthIndex === 11) { // December 2023
                sales = 5500; // Holiday season peak
            }
            
            // Generate realistic royalties for scrollable chart
            const royalties = generateRealisticRoyalties(sales, i, 300);
            
            // Generate segment data
            const segmentData = generateSegmentData(sales, i);
            
            // Calculate actual returns from individual marketplace data
            const individualReturns = segmentData.values.map((value, index) => {
                return (index === segmentData.zeroReturnsMarketplaceIndex) ? 0 : Math.round(value * 0.1);
            });
            const returns = individualReturns.reduce((sum, returns) => sum + returns, 0);
            
            return {
                month,
                year,
                sales,
                royalties,
                returns,
                values: segmentData.values,
                labels: segmentData.labels,
                zeroReturnsMarketplaceIndex: segmentData.zeroReturnsMarketplaceIndex
            };
        });

        // Generate all-time daily sales history data using external data loader
        const dataLoader = new SnapDataLoader();
        const allTimeSalesData = await dataLoader.generateDailySalesHistory({
            startDate: '2016-01-01',
            endDate: '2025-12-31'
            // Using default parameters from dummy-data.js (max sales: 50, 30% zero sales, consecutive zero weeks)
        });
        
        // Convert dateObj strings to Date objects for filtering
        allTimeSalesData.forEach(item => {
            item.dateObj = new Date(item.dateObj);
        });
        
        // Legacy code for reference (replaced by external data generation)
        /*
        const allTimeSalesData = Array.from({length: 3650}, (_, i) => { // 10 years = ~3650 days
            const date = new Date(2016, 0, 1); // Start from January 1, 2016
            date.setDate(date.getDate() + i);
            
            const month = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
            const day = date.getDate().toString().padStart(2, '0');
            const year = date.getFullYear().toString().slice(-2); // Last 2 digits of year
            
            // Realistic no-sales probability - some days have zero sales
            // Higher chance of no sales on certain days/periods
            const dayOfWeek = date.getDay();
            let noSalesProbability = 0.15; // Base 15% chance of no sales
            
            // Increase no-sales probability on certain days
            if (dayOfWeek === 1 || dayOfWeek === 2) { // Monday/Tuesday
                noSalesProbability = 0.25; // 25% chance
            }
            
            // Seasonal no-sales adjustments
            const monthIndex = date.getMonth();
            if (monthIndex === 0 || monthIndex === 1) { // January/February (post-holiday slump)
                noSalesProbability += 0.1; // +10% chance
            }
            
            // Random chance for no sales
            const hasNoSales = Math.random() < noSalesProbability;
            
            if (hasNoSales) {
                // Zero sales day
                return {
                    month,
                    day,
                    year,
                    sales: 0,
                    royalties: 0,
                    fullDate: date.toISOString().split('T')[0],
                    dateObj: new Date(date)
                };
            }
            
            // Create realistic sales patterns over 10 years (for days with sales)
            // Seasonal patterns that repeat yearly
            const monthMultiplier = [
                0.8, 0.7, 0.9, 1.0, 1.1, 1.2, // Jan-Jun
                1.3, 1.4, 1.2, 1.0, 1.5, 2.2  // Jul-Dec (holiday season boost)
            ][date.getMonth()];
            
            // Add yearly growth trend (business growing over time)
            const yearsSinceStart = (date.getFullYear() - 2016) + (date.getMonth() / 12);
            const growthMultiplier = 1 + (yearsSinceStart * 0.15); // 15% growth per year
            
            // Add weekly patterns (higher sales on weekends)
            const weekdayMultiplier = dayOfWeek === 0 || dayOfWeek === 6 ? 1.4 : 1.0;
            
            // Add some randomness to make it realistic
            const randomFactor = 0.6 + (Math.random() * 0.8); // 0.6 to 1.4
            
            // Special events and spikes (Black Friday, etc.)
            let eventMultiplier = 1.0;
            if (date.getMonth() === 10 && date.getDate() >= 24 && date.getDate() <= 26) {
                eventMultiplier = 3.0; // Black Friday weekend
            } else if (date.getMonth() === 11 && date.getDate() >= 20) {
                eventMultiplier = 2.5; // Christmas season
            }
            
            // Base sales between 5 and 150, then apply all multipliers
            const baseSales = 5 + (Math.random() * 145);
            const sales = Math.round(baseSales * monthMultiplier * growthMultiplier * weekdayMultiplier * eventMultiplier * randomFactor);
            
            // Generate realistic royalties (only if there are sales)
            const royalties = sales > 0 ? generateRealisticRoyalties(sales, i, 500) : 0;
            
            return {
                month,
                day,
                year,
                sales,
                royalties,
                fullDate: date.toISOString().split('T')[0], // YYYY-MM-DD format for filtering
                dateObj: new Date(date)
            };
        });
        */

        // Initial date range - show last 12 months by default
        const defaultEndDate = new Date(allTimeSalesData[allTimeSalesData.length - 1].dateObj);
        const defaultStartDate = new Date(defaultEndDate);
        defaultStartDate.setMonth(defaultStartDate.getMonth() - 12);
        
        // Filter initial data to last 12 months
        let currentDailySalesData = allTimeSalesData.filter(d => 
            d.dateObj >= defaultStartDate && d.dateObj <= defaultEndDate
        );
        
        // Store current date range for the slider
        let currentStartDate = defaultStartDate;
        let currentEndDate = defaultEndDate;

        // Initialize standard chart
        let chart1 = new SnapChart({
            container: '#chartContainer1',
            type: 'stacked-column',
            data: currentData,
            options: {
                title: 'Stacked Columns with Compare',
                subtitle: 'Interactive stacked column chart with comparison data overlay',
                compareMode: compareMode,
                compareData: comparisonData1,
                animate: animationEnabled,
                responsive: true
            }
        });

        // Initialize scrollable chart
        let chart2 = new SnapChart({
            container: '#chartContainer2',
            type: 'scrollable-stacked-column',
            data: extendedData,
            options: {
                title: 'Stacked Columns (Scrollable)',
                subtitle: 'Horizontally scrollable stacked column chart with custom scrollbar',
                compareMode: compareMode,
                compareData: extendedComparisonData,
                animate: animationEnabled,
                responsive: true
            }
        });

        // Initialize daily sales history chart
        let chart3 = new SnapChart({
            container: '#chartContainer3',
            type: 'daily-sales-history',
            data: currentDailySalesData,
            options: {
                title: 'Daily Sales History',
                subtitle: `All-time sales data (${currentDailySalesData.length} days) with interactive date range selection`,
                animate: animationEnabled,
                responsive: true,
                // Pass all-time data and date range info for the slider
                allTimeData: allTimeSalesData,
                currentStartDate: currentStartDate,
                currentEndDate: currentEndDate
            }
        });
        
        // Load pie chart data from external data loader - using fitType data like in figma-pie-test.html
        const pieChartData1 = await dataLoader.getPieChartData('fitType');
        
        // Alternative pie chart data for testing updates - using salesByMarketplace data
        const pieChartData2 = await dataLoader.getPieChartData('salesByMarketplace');

        let currentPieData = pieChartData1;

        // Initialize pie chart
        let chart4 = new SnapChart({
            container: '#chartContainer4',
            type: 'pie',
            data: currentPieData,
            options: {
                title: 'Fit Type Distribution',
                subtitle: 'Donut chart showing sales breakdown by fit type',
                animate: animationEnabled,
                responsive: true
            }
        });

        // Theme controls
        document.getElementById('lightTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'light');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        document.getElementById('darkTheme').addEventListener('click', function() {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
        });
        
        // Data update control
        let useData1 = true;
        let usePieData1 = true;
        document.getElementById('updateData').addEventListener('click', function() {
            currentData = useData1 ? sampleData2 : sampleData1;
            currentPieData = usePieData1 ? pieChartData2 : pieChartData1;
            useData1 = !useData1;
            usePieData1 = !usePieData1;
            
            chart1.updateData(currentData);
            chart4.updateData(currentPieData);
            
            // Update pie chart title and subtitle based on current data
            const newTitle = usePieData1 ? 'Sales by Marketplace' : 'Fit Type Distribution';
            const newSubtitle = usePieData1 ? 'Donut chart showing sales breakdown by marketplace' : 'Donut chart showing sales breakdown by fit type';
            chart4.options.title = newTitle;
            chart4.options.subtitle = newSubtitle;
            
            // Keep the same subtitle since it describes the chart type
            chart1.render();
            chart4.render();
        });
        
        // Animation toggle (Note: Daily Sales History chart doesn't use animations for performance)
        document.getElementById('toggleAnimation').addEventListener('click', function() {
            animationEnabled = !animationEnabled;
            chart1.options.animate = animationEnabled;
            chart2.options.animate = animationEnabled;
            chart4.options.animate = animationEnabled;
            // chart3 (Daily Sales History) doesn't use animations for performance with many columns
            this.textContent = animationEnabled ? 'Disable Animation' : 'Enable Animation';
            chart1.render();
            chart2.render();
            chart4.render();
            // chart3 doesn't need re-render for animation toggle
        });
        
        // Compare mode toggle (affects both standard and scrollable charts)
        document.getElementById('toggleCompare').addEventListener('click', function() {
            compareMode = !compareMode;

            // Update both charts
            chart1.options.compareMode = compareMode;
            chart2.options.compareMode = compareMode;

            this.textContent = compareMode ? 'Disable Compare' : 'Enable Compare';
            this.classList.toggle('active', compareMode);

            // Re-render both charts
            chart1.render();
            chart2.render();
        });
        

        
        // Date range is now controlled by the interactive slider in the chart itself
        // No button controls needed - users drag the handles to adjust the date range
        
        // Chart event listeners removed - only hover tooltips are active now
        

        
        // Apply initial theme
        document.documentElement.setAttribute('data-theme', 'light');
        }
        
        // Initialize charts when page loads
        initializeCharts().catch(error => {
            console.error('Failed to initialize charts:', error);
        });
    </script>
</body>
</html> 