
// Test the data synchronization fix using external data
async function testDataSync() {
  const dataLoader = new SnapDataLoader();
  
  // Load test data from external file
  const testData = await dataLoader.getTestData('dailySalesSync');
  
  const chart = new SnapChart({
    container: document.querySelector('#chart-container'),
    type: 'daily-sales-history',
    data: testData
  });

  // Test that both data sources are synchronized
  if (window.SnapLogger) {
    window.SnapLogger.debug('Initial data length:', chart.data.length);
    window.SnapLogger.debug('Initial allTimeData length:', chart.options.allTimeData.length);
    window.SnapLogger.debug('Data synchronized:', chart.data.length === chart.options.allTimeData.length);
  }

  // Generate new test data for update
  const newTestData = await dataLoader.generateDailySalesHistory({
    startDate: '2025-01-01',
    endDate: '2025-01-01'
    // Using default parameters from dummy-data.js (max sales: 50, 30% zero sales, consecutive zero weeks)
  });

  // Test updating data
  chart.updateData(newTestData);

  if (window.SnapLogger) {
    window.SnapLogger.debug('Updated data length:', chart.data.length);
    window.SnapLogger.debug('Updated allTimeData length:', chart.options.allTimeData.length);
    window.SnapLogger.debug('Data still synchronized:', chart.data.length === chart.options.allTimeData.length);
  }
}

// Run the test
testDataSync().catch(error => {
  console.error('Failed to test data sync:', error);
});

