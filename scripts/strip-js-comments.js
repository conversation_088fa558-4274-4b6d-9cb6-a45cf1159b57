"use strict";

const fs = require("fs");

function printUsageAndExit() {
  console.error("Usage: node scripts/strip-js-comments.js <input.js> <output.js>");
  process.exit(1);
}

const inputPath = process.argv[2];
const outputPath = process.argv[3];

if (!inputPath || !outputPath) {
  printUsageAndExit();
}

async function run() {
  // Lazily load strip-comments via npx-installed dependency when executed via npx -p
  let stripComments;
  try {
    stripComments = require("strip-comments");
  } catch (err) {
    console.error("strip-comments module not found. Run via: npx --yes -p strip-comments node scripts/strip-js-comments.js <in> <out>");
    process.exit(2);
  }

  const code = fs.readFileSync(inputPath, "utf8");
  const cleaned = stripComments(code, {
    // Ensure we do not preserve /*! ... */ comments
    preserve: false
  });
  fs.writeFileSync(outputPath, cleaned);
}

run().catch((err) => {
  console.error(err);
  process.exit(1);
});


