# SnapGrid Data Grid Architecture

## Overview

SnapGrid is a comprehensive client-side data grid engine inspired by AG Grid's capabilities, built following the single-file architecture pattern established by the Snap Dashboard's charts engine. It provides enterprise-grade features while maintaining simplicity and performance.

## Architecture Philosophy

### Single-File Pattern
Following the exact same pattern as `components/charts/snap-charts.js`, SnapGrid contains all functionality within a single JavaScript file (`snap-grid.js`) with a separate CSS file (`snap-grid.css`). This approach provides:

- **Simplicity**: All grid logic in one place
- **Maintainability**: Easy to understand and modify
- **Performance**: No module loading overhead
- **Consistency**: Matches existing Snap Dashboard patterns

### Client-Side Focus
SnapGrid operates entirely on the client-side, processing data locally for:
- Instant filtering and sorting
- No server round-trips for basic operations
- Offline capability
- Reduced server load

## Core Features

### 1. Virtual Scrolling
Efficient rendering of large datasets using DOM recycling:
```javascript
// Automatic virtual scrolling for datasets > 100 rows
const grid = new SnapGrid('container', {
    data: largeDataset,
    virtualScrolling: true,
    rowHeight: 40
});
```

**Column Virtualization**: For wide datasets, enable horizontal virtualization:
```javascript
const grid = new SnapGrid('container', {
    data: wideDataset,
    virtualScrolling: true,
    virtualizeColumns: true, // Enable column virtualization
    rowHeight: 40
});
```

### 2. Column Management
Comprehensive column configuration with sorting, filtering, and resizing:
```javascript
const columns = [
    {
        field: 'productName',
        headerName: 'Product Name',
        width: 200,
        sortable: true,
        filterable: true,
        resizable: true
    },
    {
        field: 'revenue',
        headerName: 'Revenue',
        type: 'currency',
        width: 120,
        currencyFormat: { currency: 'USD' }
    }
];
```

### 3. Data Operations
Client-side filtering, sorting, and grouping:
```javascript
// Apply filters
grid.setFilter('status', {
    type: 'text',
    operator: 'equals',
    value: 'Active'
});

// Sort by multiple columns
grid.setSortConfig([
    { field: 'revenue', direction: 'desc' },
    { field: 'productName', direction: 'asc' }
]);

// Group by field
grid.setGroupConfig({ field: 'marketplace' });
```

### 4. Custom Cell Renderers
Built-in and custom cell rendering with built-in XSS protection:
```javascript
{
    field: 'status',
    cellRenderer: (params) => {
        const { value } = params;
        const color = value === 'Active' ? 'green' : 'red';
        return `<span style="color: ${color}">${value}</span>`;
    }
}
```

**HTML Sanitization**: All custom renderer output is automatically sanitized to prevent XSS attacks. Use `allowUnsafeHtml: true` only when you trust the content source.

### 5. Security Features
- **Automatic HTML Sanitization**: Prevents XSS vectors in custom renderers
- **Content Security**: Safe handling of user-generated content
- **Configurable Safety**: Balance between security and functionality

### 6. Accessibility Features
- **ARIA Compliance**: Full ARIA grid implementation with row/column counts
- **Keyboard Navigation**: Arrow keys, Enter, Space, and modifier key support
- **Screen Reader Support**: Descriptive labels and navigation instructions
- **Focus Management**: Proper focus handling for interactive elements

## Performance Features

### Measurement Caching
Autosize operations use intelligent caching to improve performance:
```javascript
// Measurement cache automatically optimizes repeated text measurements
// Cache is invalidated when fonts or sizes change
grid.invalidateMeasurementCache(); // Manual cache invalidation if needed
```

### Deterministic Data Generation
For testing and reproducible results, use seeded data generation:
```javascript
// Generate reproducible test data
const testData = generateProductData(1000, { seed: 12345 });
// Reset to native random behavior when done
resetRandom();
```

## API Reference

### Constructor
```javascript
new SnapGrid(container, options)
```

**Parameters:**
- `container` (string|HTMLElement): Container element or ID
- `options` (Object): Configuration options

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `columns` | Array | `[]` | Column definitions |
| `data` | Array | `[]` | Data array |
| `height` | string | `'400px'` | Grid height |
| `virtualScrolling` | boolean | `true` | Enable virtual scrolling |
| `virtualizeColumns` | boolean | `false` | Enable column virtualization for wide datasets |
| `sortable` | boolean | `true` | Enable sorting |
| `filterable` | boolean | `true` | Enable filtering |
| `selectable` | boolean | `false` | Enable row selection |
| `editable` | boolean | `false` | Enable cell editing |
| `groupable` | boolean | `false` | Enable grouping |
| `theme` | string | `'default'` | Theme name |

### Column Definition

| Property | Type | Description |
|----------|------|-------------|
| `field` | string | Data field name |
| `headerName` | string | Column header text |
| `width` | number | Column width in pixels |
| `type` | string | Data type ('text', 'number', 'currency', 'date', 'boolean') |
| `sortable` | boolean | Enable sorting for this column |
| `filterable` | boolean | Enable filtering for this column |
| `editable` | boolean | Enable editing for this column |
| `cellRenderer` | function | Custom cell renderer |

### Methods

#### Data Management
```javascript
// Update grid data
grid.updateData(newData);

// Get selected data
const selected = grid.getSelectedData();

// Resize grid (call after container size changes)
grid.resize();
```

#### Filtering
```javascript
// Set filter
grid.setFilter(field, filterConfig);

// Clear filter
grid.clearFilter(field);
```

#### Sorting
```javascript
// Set sort configuration
grid.setSortConfig(sortConfig);

// Toggle sort for field
grid.toggleSort(field, multiSort);
```

#### Selection
```javascript
// Select row
grid.selectRow(rowIndex, multiSelect);

// Select all
grid.selectAll();

// Clear selection
grid.clearSelection();
```

#### Events
```javascript
// Listen to events
grid.on('selectionChanged', (data) => {
    console.log('Selected rows:', data.selectedRows);
});

grid.on('filterChanged', (data) => {
    console.log('Filter config:', data.filterConfig);
});
```

### Event Types

| Event | Description | Data |
|-------|-------------|------|
| `gridReady` | Grid initialization complete | `{ grid }` |
| `rendered` | Grid rendered | `{ renderTime }` |
| `selectionChanged` | Row selection changed | `{ selectedRows }` |
| `cellSelectionChanged` | Cell selection changed | `{ selectedCells }` |
| `sortChanged` | Sort configuration changed | `{ sortConfig }` |
| `filterChanged` | Filter configuration changed | `{ filterConfig }` |
| `columnResized` | Column resized | `{ field, width }` |
| `scroll` | Grid scrolled | `{ scrollTop, scrollLeft, firstRow, lastRow, visibleRange }` |

## Performance Guidelines

### Large Datasets
For datasets > 1,000 rows:
```javascript
const grid = new SnapGrid('container', {
    data: largeData,
    virtualScrolling: true,
    rowHeight: 35, // Smaller rows for more visible data
    performance: true // Enable performance monitoring
});
```

### Memory Optimization
- Use virtual scrolling for large datasets
- Implement data pagination for extremely large datasets
- Monitor memory usage with performance tools

### Rendering Performance
- Keep custom cell renderers lightweight
- Avoid complex DOM manipulation in renderers
- Use CSS classes instead of inline styles

## Customization

### Themes
Built-in themes:
- `default`: Standard theme
- `compact`: Smaller row heights and padding
- `comfortable`: Larger row heights and padding
- `dark`: Dark mode theme

```javascript
const grid = new SnapGrid('container', {
    theme: 'compact',
    // ... other options
});
```

### Custom CSS
Override CSS variables for customization:
```css
.snap-grid.custom-theme {
    --grid-row-height: 50px;
    --grid-cell-padding: 20px;
    --grid-border-color: #custom-color;
}
```

## Accessibility

SnapGrid includes comprehensive accessibility features:

### ARIA Support
- Grid role and structure
- Column headers with proper roles
- Keyboard navigation support
- Screen reader announcements

### Keyboard Navigation
- Arrow keys: Navigate cells
- Enter: Edit cell or confirm edit
- Escape: Cancel edit
- Space: Toggle selection
- Ctrl/Cmd + A: Select all

### Focus Management
- Visible focus indicators
- Logical tab order
- Focus restoration after operations

## Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## Integration Examples

### Basic Setup
```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="snap-grid.css">
</head>
<body>
    <div id="my-grid"></div>
    
    <script src="snap-grid.js"></script>
    <script>
        const grid = new SnapGrid('my-grid', {
            columns: [
                { field: 'name', headerName: 'Name', width: 200 },
                { field: 'sales', headerName: 'Sales', type: 'number' }
            ],
            data: [
                { name: 'Product A', sales: 100 },
                { name: 'Product B', sales: 200 }
            ]
        });
    </script>
</body>
</html>
```

### With Existing Snap Dashboard
```javascript
// Integration with existing dashboard data
import { getDashboardData } from '../dashboard/data-service.js';

const dashboardData = await getDashboardData();
const grid = new SnapGrid('products-grid', {
    columns: productColumns,
    data: dashboardData.products,
    virtualScrolling: true,
    performance: true
});
```

## Troubleshooting

### Common Issues

1. **Grid not rendering**
   - Check container element exists
   - Verify CSS is loaded
   - Check console for errors

2. **Performance issues**
   - Enable virtual scrolling
   - Reduce data size
   - Optimize custom renderers

3. **Styling issues**
   - Check CSS variable overrides
   - Verify theme application
   - Check for CSS conflicts

### Debug Mode
Enable debug logging:
```javascript
const grid = new SnapGrid('container', {
    debug: true,
    performance: true
});
```

## Future Enhancements

Planned features for future versions:
- Server-side data operations
- Column pinning/freezing
- Row grouping with aggregation
- Export functionality (CSV, Excel)
- Advanced filtering UI
- Drag and drop row reordering
