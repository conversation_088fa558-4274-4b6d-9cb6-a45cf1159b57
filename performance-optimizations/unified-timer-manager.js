/**
 * Unified Timer Manager for Performance Optimization
 * Consolidates all time-related operations into a single high-frequency timer
 * Reduces timer overhead and improves performance
 */

class UnifiedTimerManager {
  constructor() {
    this.isActive = false;
    this.masterInterval = null;
    this.lastTick = 0;
    
    // Registered callbacks with their frequencies
    this.callbacks = new Map();
    this.nextCallbackId = 1;
    
    // Master frequency (1 second for time display)
    this.masterFrequency = 1000;
    
    // Performance tracking
    this.stats = {
      totalTicks: 0,
      callbackExecutions: 0,
      averageExecutionTime: 0,
      lastExecutionTime: 0
    };
    
    console.log('🕐 UnifiedTimerManager initialized');
  }

  /**
   * Start the unified timer system
   */
  start() {
    if (this.isActive) {
      console.warn('⚠️ UnifiedTimerManager already active');
      return;
    }

    console.log('🚀 Starting unified timer system...');
    this.isActive = true;
    this.lastTick = Date.now();

    // Use EventCleanupManager for proper cleanup
    this.masterInterval = window.EventCleanupManager ? 
      window.EventCleanupManager.setInterval(() => this.tick(), this.masterFrequency) :
      setInterval(() => this.tick(), this.masterFrequency);

    console.log('✅ Unified timer system started');
  }

  /**
   * Stop the unified timer system
   */
  stop() {
    if (!this.isActive) return;

    console.log('🛑 Stopping unified timer system...');
    this.isActive = false;

    if (this.masterInterval) {
      if (window.EventCleanupManager) {
        window.EventCleanupManager.clearInterval(this.masterInterval);
      } else {
        clearInterval(this.masterInterval);
      }
      this.masterInterval = null;
    }

    console.log('✅ Unified timer system stopped');
  }

  /**
   * Register a callback to be executed at specified frequency
   * @param {Function} callback - Function to execute
   * @param {number} frequency - How often to execute (in milliseconds)
   * @param {string} name - Name for debugging
   * @returns {number} Callback ID for unregistering
   */
  registerCallback(callback, frequency, name = 'unnamed') {
    const id = this.nextCallbackId++;
    
    this.callbacks.set(id, {
      callback,
      frequency,
      name,
      lastExecution: 0,
      executionCount: 0
    });

    console.log(`📝 Registered callback "${name}" with ${frequency}ms frequency (ID: ${id})`);
    return id;
  }

  /**
   * Unregister a callback
   * @param {number} id - Callback ID returned from registerCallback
   */
  unregisterCallback(id) {
    const callbackInfo = this.callbacks.get(id);
    if (callbackInfo) {
      console.log(`🗑️ Unregistered callback "${callbackInfo.name}" (ID: ${id})`);
      this.callbacks.delete(id);
    }
  }

  /**
   * Main tick function - executes all due callbacks
   */
  tick() {
    const startTime = performance.now();
    const currentTime = Date.now();
    
    this.stats.totalTicks++;
    let executedCallbacks = 0;

    // Execute all due callbacks
    for (const [id, callbackInfo] of this.callbacks) {
      const timeSinceLastExecution = currentTime - callbackInfo.lastExecution;
      
      if (timeSinceLastExecution >= callbackInfo.frequency) {
        try {
          callbackInfo.callback();
          callbackInfo.lastExecution = currentTime;
          callbackInfo.executionCount++;
          executedCallbacks++;
        } catch (error) {
          console.error(`❌ Error executing callback "${callbackInfo.name}":`, error);
        }
      }
    }

    // Update performance stats
    const executionTime = performance.now() - startTime;
    this.stats.callbackExecutions += executedCallbacks;
    this.stats.lastExecutionTime = executionTime;
    this.stats.averageExecutionTime = 
      (this.stats.averageExecutionTime * (this.stats.totalTicks - 1) + executionTime) / this.stats.totalTicks;

    this.lastTick = currentTime;
  }

  /**
   * Get performance statistics
   */
  getStats() {
    return {
      ...this.stats,
      isActive: this.isActive,
      registeredCallbacks: this.callbacks.size,
      uptime: this.isActive ? Date.now() - (this.lastTick - this.masterFrequency) : 0
    };
  }

  /**
   * Get detailed callback information
   */
  getCallbackInfo() {
    const info = [];
    for (const [id, callbackInfo] of this.callbacks) {
      info.push({
        id,
        name: callbackInfo.name,
        frequency: callbackInfo.frequency,
        executionCount: callbackInfo.executionCount,
        lastExecution: callbackInfo.lastExecution,
        timeSinceLastExecution: Date.now() - callbackInfo.lastExecution
      });
    }
    return info;
  }
}

// Global instance
window.UnifiedTimerManager = new UnifiedTimerManager();

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.UnifiedTimerManager.stop();
});

// Auto-cleanup on component unload
window.addEventListener('componentUnloaded', (e) => {
  if (e.detail.component === 'dashboard') {
    window.UnifiedTimerManager.stop();
  }
});

console.log('🕐 UnifiedTimerManager loaded and ready');
