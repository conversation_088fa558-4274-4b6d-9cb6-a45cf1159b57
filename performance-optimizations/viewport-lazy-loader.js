// Simple security helper functions (inline to avoid import issues)
function safeSetHTML(element, html, options = {}) {
  if (!element) return false;
  if (options.trusted === true) {
    element.innerHTML = html;
  } else {
    // Basic sanitization - remove script tags and dangerous attributes
    const sanitized = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/href\s*=\s*["']javascript:[^"']*["']/gi, '');
    element.innerHTML = sanitized;
  }
  return true;
}

function updateTextContent(element, text) {
  if (!element) return false;
  element.textContent = text || '';
  return true;
}

/**
 * Viewport-Based Lazy Loading System
 * Only loads chart data when components enter the viewport
 */

class ViewportLazyLoader {
  constructor() {
    this.observers = new Map();
    this.loadedComponents = new Set();
    this.loadingComponents = new Set();
    this.intersectionObserver = null;
    this.initializeIntersectionObserver();
  }

  /**
   * Initialize Intersection Observer for viewport detection
   */
  initializeIntersectionObserver() {
    const options = {
      root: null, // Use viewport as root
      rootMargin: '100px', // Start loading 100px before entering viewport
      threshold: 0.1 // Trigger when 10% of element is visible
    };

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.handleElementIntersection(entry.target);
        }
      });
    }, options);

    console.log('👁️ Viewport Lazy Loader initialized');
  }

  /**
   * Register a component for lazy loading
   */
  registerComponent(element, config) {
    const componentId = config.id || this.generateComponentId(element);
    
    // Store component configuration
    this.observers.set(componentId, {
      element,
      config,
      loaded: false,
      loading: false
    });

    // Add loading placeholder
    this.addLoadingPlaceholder(element, config);

    // Start observing the element
    this.intersectionObserver.observe(element);

    console.log(`📝 Registered lazy component: ${componentId}`);
    
    return componentId;
  }

  /**
   * Handle element entering viewport
   */
  async handleElementIntersection(element) {
    const componentData = this.findComponentByElement(element);
    if (!componentData || componentData.loaded || componentData.loading) {
      return;
    }

    const { config, element: targetElement } = componentData;
    const componentId = config.id;

    console.log(`🔄 Loading component in viewport: ${componentId}`);

    // Mark as loading
    componentData.loading = true;
    this.loadingComponents.add(componentId);

    // Protect sidebar state during chart loading
    this.protectSidebarDuringLoading(true);

    try {
      // Show loading state
      this.showLoadingState(targetElement, config);

      // Load the component data with performance monitoring
      const loadStartTime = performance.now();
      await this.loadComponentData(targetElement, config);
      const loadEndTime = performance.now();

      console.log(`📊 Component ${componentId} loaded in ${(loadEndTime - loadStartTime).toFixed(2)}ms`);

      // Mark as loaded
      componentData.loaded = true;
      this.loadedComponents.add(componentId);
      this.loadingComponents.delete(componentId);

      // Stop observing this element
      this.intersectionObserver.unobserve(targetElement);

      console.log(`✅ Component loaded: ${componentId}`);

    } catch (error) {
      console.error(`❌ Failed to load component ${componentId}:`, error);
      componentData.loading = false;
      this.loadingComponents.delete(componentId);
      this.showErrorState(targetElement, config, error);
    } finally {
      // Always release sidebar protection when loading completes (success or failure)
      this.protectSidebarDuringLoading(false);
    }
  }

  /**
   * Load component data based on type
   */
  async loadComponentData(element, config) {
    switch (config.type) {
      case 'chart':
        await this.loadChartComponent(element, config);
        break;
      case 'sales-card':
        await this.loadSalesCardComponent(element, config);
        break;
      case 'listing-section':
        await this.loadListingSection(element, config);
        break;
      default:
        throw new Error(`Unknown component type: ${config.type}`);
    }
  }

  /**
   * Load chart component data
   */
  async loadChartComponent(element, config) {
    const { chartType, dataGenerator, initFunction } = config;

    // Generate chart data
    let chartData;
    if (dataGenerator) {
      chartData = await dataGenerator();
    }

    // Initialize the chart
    if (initFunction) {
      await initFunction(element, chartData);
    }

    // Remove loading placeholder
    this.removeLoadingPlaceholder(element);
  }

  /**
   * Load sales card component
   */
  async loadSalesCardComponent(element, config) {
    const { dataGenerator, renderer } = config;

    // Generate sales data
    const salesData = await dataGenerator();

    // Render the sales card
    if (renderer) {
      await renderer(element, salesData);
    }

    this.removeLoadingPlaceholder(element);
  }

  /**
   * Load listing section
   */
  async loadListingSection(element, config) {
    const { dataGenerator, renderer, virtualScroll = false } = config;

    // Generate listing data
    const listingData = await dataGenerator();

    // Use virtual scrolling for large datasets
    if (virtualScroll && listingData.length > 50) {
      this.setupVirtualScrolling(element, listingData, config);
    } else {
      // Regular rendering
      if (renderer) {
        await renderer(element, listingData);
      }
    }

    this.removeLoadingPlaceholder(element);
  }

  /**
   * Add loading placeholder
   */
  addLoadingPlaceholder(element, config) {
    const placeholder = document.createElement('div');
    placeholder.className = 'lazy-loading-placeholder';
    const placeholderHTML = `
      <div class="lazy-loading-content">
        <div class="lazy-loading-spinner"></div>
        <div class="lazy-loading-text">${config.loadingText || 'Loading...'}</div>
      </div>
    `;
    // Static template content, mark as trusted
    safeSetHTML(placeholder, placeholderHTML, { policy: 'trusted', trusted: true });

    // Hide original content
    const originalContent = element.innerHTML;
    element.setAttribute('data-original-content', originalContent);
    updateTextContent(element, '');
    element.appendChild(placeholder);
  }

  /**
   * Show loading state during data loading
   */
  showLoadingState(element, config) {
    if (window.SnapLoader) {
      window.SnapLoader.showOverlay(element, {
        text: config.loadingText || 'Loading data...',
        id: `lazy-${config.id}`
      });
    }
  }

  /**
   * Remove loading placeholder
   */
  removeLoadingPlaceholder(element) {
    const placeholder = element.querySelector('.lazy-loading-placeholder');
    if (placeholder) {
      placeholder.remove();
    }

    if (window.SnapLoader) {
      window.SnapLoader.hideOverlay(element);
    }
  }

  /**
   * Show error state
   */
  showErrorState(element, config, error) {
    const errorPlaceholder = document.createElement('div');
    errorPlaceholder.className = 'lazy-loading-error';
    errorPlaceholder.innerHTML = `
      <div class="lazy-loading-error-content">
        <div class="lazy-loading-error-icon">⚠️</div>
        <div class="lazy-loading-error-text">Failed to load ${config.type}</div>
        <button class="lazy-loading-retry-btn" onclick="window.ViewportLazyLoader.retryComponent('${config.id}')">
          Retry
        </button>
      </div>
    `;

    element.innerHTML = '';
    element.appendChild(errorPlaceholder);
  }

  /**
   * Setup virtual scrolling for large datasets
   */
  setupVirtualScrolling(element, data, config) {
    const itemHeight = config.itemHeight || 60;
    const containerHeight = element.clientHeight || 400;
    
    return window.DOMOptimizer.createVirtualScrollList(
      element,
      data,
      itemHeight,
      config.itemRenderer
    );
  }

  /**
   * Retry loading a failed component
   */
  async retryComponent(componentId) {
    const componentData = this.observers.get(componentId);
    if (componentData) {
      componentData.loaded = false;
      componentData.loading = false;
      await this.handleElementIntersection(componentData.element);
    }
  }

  /**
   * Protect sidebar state during heavy loading operations
   */
  protectSidebarDuringLoading(enable) {
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    if (enable) {
      // Add protection flag to prevent sidebar state changes during loading
      sidebar.dataset.loadingProtection = 'true';
      console.log('🛡️ Sidebar protection enabled during chart loading');
    } else {
      // Only remove protection if no other components are loading
      if (this.loadingComponents.size === 0) {
        delete sidebar.dataset.loadingProtection;
        console.log('🛡️ Sidebar protection disabled - all loading complete');
      }
    }
  }

  /**
   * Helper methods
   */
  generateComponentId(element) {
    return `lazy-${element.id || element.className.replace(/\s+/g, '-')}-${Date.now()}`;
  }

  findComponentByElement(element) {
    for (const [id, data] of this.observers) {
      if (data.element === element) {
        return data;
      }
    }
    return null;
  }

  /**
   * Get loading statistics
   */
  getStats() {
    return {
      registered: this.observers.size,
      loaded: this.loadedComponents.size,
      loading: this.loadingComponents.size,
      pending: this.observers.size - this.loadedComponents.size - this.loadingComponents.size
    };
  }

  /**
   * Cleanup
   */
  cleanup() {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    this.observers.clear();
    this.loadedComponents.clear();
    this.loadingComponents.clear();
  }
}

// Global instance
window.ViewportLazyLoader = new ViewportLazyLoader();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.ViewportLazyLoader.cleanup();
});

/**
 * Dashboard-specific lazy loading configurations
 */
window.DashboardLazyConfigs = {
  // Today vs Previous Years Chart
  todayVsPreviousYears: {
    id: 'today-vs-previous-years-chart',
    type: 'chart',
    chartType: 'todayVsPreviousYears',
    loadingText: 'Loading 26 years of data...',
    dataGenerator: async () => {
      return await window.generateTodayVsPreviousYearsDataCached();
    },
    initFunction: async (element, data) => {
      // Initialize chart with data
      const chart = new SnapChart({
        container: element,
        type: 'stacked-column',
        data: data,
        // ... other chart options
      });
      element.snapChart = chart;
    }
  },

  // Monthly Sales Chart
  monthlySales: {
    id: 'monthly-sales-chart',
    type: 'chart',
    chartType: 'monthlySales',
    loadingText: 'Loading monthly data...',
    dataGenerator: async () => {
      const currentYear = new Date().getFullYear();
      return await window.generateMonthlySalesDataForYearCached(currentYear);
    },
    initFunction: async (element, data) => {
      const chart = new SnapChart(element, {
        type: 'stackedColumn',
        data: data,
        // ... other chart options
      });
      element.snapChart = chart;
    }
  },

  // Last Week Sales Chart
  lastWeekSales: {
    id: 'last-week-sales-chart',
    type: 'chart',
    chartType: 'lastWeekSales',
    loadingText: 'Loading last week data...',
    dataGenerator: async () => {
      const dataLoader = new SnapDataLoader();
      return await dataLoader.generateStackedColumnData({ days: 7 });
    },
    initFunction: async (element, data) => {
      const chart = new SnapChart(element, {
        type: 'stackedColumn',
        data: data,
        // ... other chart options
      });
      element.snapChart = chart;
    }
  },

  // Sales Cards
  salesCard: {
    id: 'sales-card',
    type: 'sales-card',
    loadingText: 'Loading sales data...',
    dataGenerator: async () => {
      return await generateFourSalesCardsMockData();
    },
    renderer: async (element, data) => {
      // Render sales card with data
      applyFourSalesCardsMockData(data);
    }
  },

  // Large Listing Sections
  listingSection: {
    id: 'listing-section',
    type: 'listing-section',
    loadingText: 'Loading listings...',
    virtualScroll: true,
    itemHeight: 120,
    dataGenerator: async () => {
      // Generate or fetch listing data
      return await generateListingData();
    },
    itemRenderer: (listing, index) => {
      // Render individual listing item
      const listingElement = document.createElement('div');
      listingElement.className = 'listing-analytics-div';
      listingElement.innerHTML = `
        <div class="listing-title">${listing.title}</div>
        <div class="listing-sales">${listing.sales}</div>
        <!-- ... other listing content -->
      `;
      return listingElement;
    }
  }
};
