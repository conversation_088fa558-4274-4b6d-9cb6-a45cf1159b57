/* Lazy Loading Placeholder Styles */

.lazy-loading-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
  position: relative;
  overflow: hidden;
}

.lazy-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  text-align: center;
}

/* Loading Spinner */
.lazy-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #470CED;
  border-radius: 50%;
  animation: lazy-loading-spin 1s linear infinite;
}

@keyframes lazy-loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Loading Text */
.lazy-loading-text {
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* Shimmer Effect for Chart Placeholders */
.lazy-loading-placeholder.chart-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-loading-shimmer 2s infinite;
}

@keyframes lazy-loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Sales Card Placeholder */
.lazy-loading-placeholder.sales-card-placeholder {
  min-height: 120px;
  background: #f8f9fa;
  border: 1px dashed #dee2e6;
}

.lazy-loading-placeholder.sales-card-placeholder .lazy-loading-content {
  gap: 12px;
  padding: 16px;
}

.lazy-loading-placeholder.sales-card-placeholder .lazy-loading-spinner {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.lazy-loading-placeholder.sales-card-placeholder .lazy-loading-text {
  font-size: 12px;
}

/* Listing Section Placeholder */
.lazy-loading-placeholder.listing-placeholder {
  min-height: 300px;
  background: #ffffff;
  border: 1px solid #e9ecef;
}

/* Error State */
.lazy-loading-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
}

.lazy-loading-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  text-align: center;
}

.lazy-loading-error-icon {
  font-size: 32px;
}

.lazy-loading-error-text {
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 14px;
  color: #e53e3e;
  font-weight: 500;
}

.lazy-loading-retry-btn {
  background: #470CED;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.lazy-loading-retry-btn:hover {
  background: #3a0bc4;
}

/* Skeleton Loading for Charts */
.chart-skeleton {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  min-height: 300px;
}

.chart-skeleton-header {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-loading-shimmer 2s infinite;
  border-radius: 4px;
  width: 60%;
}

.chart-skeleton-bars {
  display: flex;
  align-items: end;
  gap: 8px;
  flex: 1;
  padding: 20px 0;
}

.chart-skeleton-bar {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-loading-shimmer 2s infinite;
  border-radius: 2px;
  flex: 1;
  min-height: 20px;
}

.chart-skeleton-bar:nth-child(1) { height: 60%; animation-delay: 0s; }
.chart-skeleton-bar:nth-child(2) { height: 80%; animation-delay: 0.1s; }
.chart-skeleton-bar:nth-child(3) { height: 40%; animation-delay: 0.2s; }
.chart-skeleton-bar:nth-child(4) { height: 90%; animation-delay: 0.3s; }
.chart-skeleton-bar:nth-child(5) { height: 70%; animation-delay: 0.4s; }
.chart-skeleton-bar:nth-child(6) { height: 50%; animation-delay: 0.5s; }
.chart-skeleton-bar:nth-child(7) { height: 85%; animation-delay: 0.6s; }
.chart-skeleton-bar:nth-child(8) { height: 65%; animation-delay: 0.7s; }

/* Sales Card Skeleton */
.sales-card-skeleton {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  min-height: 120px;
}

.sales-card-skeleton-title {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-loading-shimmer 2s infinite;
  border-radius: 4px;
  width: 40%;
}

.sales-card-skeleton-value {
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-loading-shimmer 2s infinite;
  border-radius: 4px;
  width: 60%;
}

.sales-card-skeleton-metrics {
  display: flex;
  gap: 8px;
}

.sales-card-skeleton-metric {
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-loading-shimmer 2s infinite;
  border-radius: 4px;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .lazy-loading-placeholder {
    min-height: 150px;
  }
  
  .lazy-loading-content {
    padding: 16px;
    gap: 12px;
  }
  
  .lazy-loading-spinner {
    width: 24px;
    height: 24px;
    border-width: 2px;
  }
  
  .lazy-loading-text {
    font-size: 12px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .lazy-loading-placeholder {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: #4a5568;
  }
  
  .lazy-loading-text {
    color: #a0aec0;
  }
  
  .chart-skeleton-header,
  .chart-skeleton-bar,
  .sales-card-skeleton-title,
  .sales-card-skeleton-value,
  .sales-card-skeleton-metric {
    background: linear-gradient(90deg, #4a5568 25%, #2d3748 50%, #4a5568 75%);
    background-size: 200% 100%;
  }
}
