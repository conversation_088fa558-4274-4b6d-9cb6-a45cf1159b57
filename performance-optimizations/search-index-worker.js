/**
 * Search Index Worker
 * Background worker for maintaining search index for 5M+ products
 * Tokenizes product data and maintains search index for fast lookups
 */

class SearchIndexWorker {
    constructor(indexedDBManager) {
        this.dbManager = indexedDBManager;
        this.isIndexing = false;
        this.indexingProgress = 0;
        this.batchSize = 1000;
        this.stopWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    }

    /**
     * Build complete search index from all products
     * @returns {Promise<Object>} Indexing result
     */
    async buildSearchIndex() {
        if (this.isIndexing) {
            return { success: false, reason: 'Indexing already in progress' };
        }

        try {
            this.isIndexing = true;
            this.indexingProgress = 0;

            const startTime = Date.now();
            console.log('🔍 Starting search index build...');

            // Clear existing search index
            await this._clearSearchIndex();

            // Get total product count
            const totalProducts = await this._getProductCount();
            console.log(`📊 Indexing ${totalProducts.toLocaleString()} products...`);

            let processedCount = 0;
            let indexedTokens = 0;

            // Process products in batches
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['listingsData'], 'readonly');
            const store = transaction.objectStore('listingsData');

            await new Promise((resolve, reject) => {
                const request = store.openCursor();
                const batch = [];

                request.onsuccess = async (event) => {
                    const cursor = event.target.result;

                    if (cursor) {
                        batch.push(cursor.value);
                        
                        if (batch.length >= this.batchSize) {
                            // Process batch
                            const tokens = await this._processBatch(batch);
                            indexedTokens += tokens;
                            processedCount += batch.length;
                            
                            this.indexingProgress = (processedCount / totalProducts) * 100;
                            console.log(`🔍 Indexed ${processedCount.toLocaleString()}/${totalProducts.toLocaleString()} products (${Math.round(this.indexingProgress)}%)`);
                            
                            batch.length = 0; // Clear batch
                            
                            // Allow other operations to proceed
                            await new Promise(resolve => setTimeout(resolve, 10));
                        }

                        cursor.continue();
                    } else {
                        // Process remaining items in batch
                        if (batch.length > 0) {
                            const tokens = await this._processBatch(batch);
                            indexedTokens += tokens;
                            processedCount += batch.length;
                        }
                        resolve();
                    }
                };

                request.onerror = () => reject(request.error);
            });

            const duration = Date.now() - startTime;
            console.log(`✅ Search index build completed in ${duration}ms`);
            console.log(`📊 Indexed ${indexedTokens.toLocaleString()} tokens for ${processedCount.toLocaleString()} products`);

            return {
                success: true,
                duration,
                productsIndexed: processedCount,
                tokensIndexed: indexedTokens
            };

        } catch (error) {
            console.error('❌ Search index build failed:', error);
            return {
                success: false,
                error: error.message
            };
        } finally {
            this.isIndexing = false;
            this.indexingProgress = 0;
        }
    }

    /**
     * Update search index for specific products
     * @param {Array} products - Products to update in index
     * @returns {Promise<number>} Number of tokens indexed
     */
    async updateSearchIndex(products) {
        if (!Array.isArray(products) || products.length === 0) {
            return 0;
        }

        try {
            // Remove existing tokens for these products
            await this._removeProductsFromIndex(products.map(p => p.asin));
            
            // Add new tokens
            return await this._processBatch(products);
        } catch (error) {
            console.error('❌ Failed to update search index:', error);
            throw error;
        }
    }

    /**
     * Remove products from search index
     * @param {Array} asins - ASINs to remove
     * @returns {Promise<number>} Number of tokens removed
     */
    async removeFromSearchIndex(asins) {
        if (!Array.isArray(asins) || asins.length === 0) {
            return 0;
        }

        return await this._removeProductsFromIndex(asins);
    }

    /**
     * Get indexing progress
     * @returns {Object} Progress information
     */
    getIndexingProgress() {
        return {
            isIndexing: this.isIndexing,
            progress: this.indexingProgress
        };
    }

    // Private methods

    async _clearSearchIndex() {
        const db = this.dbManager.getDB();
        const transaction = db.transaction(['productSearch'], 'readwrite');
        const store = transaction.objectStore('productSearch');
        await this.dbManager.promisifyRequest(store.clear());
    }

    async _getProductCount() {
        const db = this.dbManager.getDB();
        const transaction = db.transaction(['listingsData'], 'readonly');
        const store = transaction.objectStore('listingsData');
        return await this.dbManager.promisifyRequest(store.count());
    }

    async _processBatch(products) {
        const db = this.dbManager.getDB();
        const transaction = db.transaction(['productSearch'], 'readwrite');
        const store = transaction.objectStore('productSearch');
        
        let tokenCount = 0;

        for (const product of products) {
            const tokens = this._tokenizeProduct(product);
            
            for (const token of tokens) {
                try {
                    await this.dbManager.promisifyRequest(store.put({
                        token: token,
                        asin: product.asin,
                        relevance: this._calculateTokenRelevance(token, product)
                    }));
                    tokenCount++;
                } catch (error) {
                    // Ignore duplicate key errors
                    if (!error.message.includes('constraint')) {
                        console.warn(`Failed to index token ${token} for ${product.asin}:`, error);
                    }
                }
            }
        }

        return tokenCount;
    }

    async _removeProductsFromIndex(asins) {
        const db = this.dbManager.getDB();
        const transaction = db.transaction(['productSearch'], 'readwrite');
        const store = transaction.objectStore('productSearch');
        const index = store.index('asin');
        
        let removedCount = 0;

        for (const asin of asins) {
            await new Promise((resolve, reject) => {
                const request = index.openCursor(IDBKeyRange.only(asin));
                
                request.onsuccess = async (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        await this.dbManager.promisifyRequest(cursor.delete());
                        removedCount++;
                        cursor.continue();
                    } else {
                        resolve();
                    }
                };
                
                request.onerror = () => reject(request.error);
            });
        }

        return removedCount;
    }

    _tokenizeProduct(product) {
        const tokens = new Set();
        
        // Tokenize title
        if (product.title) {
            this._addTokensFromText(tokens, product.title, 3); // Higher weight for title
        }
        
        // Tokenize description
        if (product.description) {
            this._addTokensFromText(tokens, product.description, 1);
        }
        
        // Add ASIN as exact match token
        if (product.asin) {
            tokens.add(product.asin.toLowerCase());
        }
        
        // Add brand if available
        if (product.brand) {
            this._addTokensFromText(tokens, product.brand, 2);
        }
        
        // Add category if available
        if (product.category) {
            this._addTokensFromText(tokens, product.category, 2);
        }

        return Array.from(tokens);
    }

    _addTokensFromText(tokens, text, weight = 1) {
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ') // Replace non-word chars with spaces
            .split(/\s+/)
            .filter(word => word.length > 2 && !this.stopWords.has(word));

        for (const word of words) {
            tokens.add(word);
            
            // Add partial matches for longer words
            if (word.length > 4) {
                for (let i = 3; i <= word.length - 1; i++) {
                    tokens.add(word.substring(0, i));
                }
            }
        }
    }

    _calculateTokenRelevance(token, product) {
        let relevance = 1;
        
        // Higher relevance for title matches
        if (product.title && product.title.toLowerCase().includes(token)) {
            relevance += 3;
        }
        
        // Medium relevance for brand/category matches
        if ((product.brand && product.brand.toLowerCase().includes(token)) ||
            (product.category && product.category.toLowerCase().includes(token))) {
            relevance += 2;
        }
        
        // Exact ASIN match gets highest relevance
        if (product.asin && product.asin.toLowerCase() === token) {
            relevance += 5;
        }
        
        return relevance;
    }
}

// Make SearchIndexWorker available globally
window.SearchIndexWorker = SearchIndexWorker;
