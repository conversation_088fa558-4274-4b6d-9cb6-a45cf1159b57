/**
 * IndexedDB Manager for Web Application
 * Handles persistent storage with automatic cleanup and size limits
 * Optimized for massive scale operations (5M+ products, 20M+ sales records)
 */

class IndexedDBManager {
  constructor() {
    this.dbName = 'SnapDashboardDB';
    this.dbVersion = 3;
    this.db = null;
    this.maxStorageSize = 2 * 1024 * 1024 * 1024; // 2GB limit for web app (browser dependent)
    this.maxRecords = {
      salesData: 20000000,      // 20M daily sales records (rolling window)
      listingsData: 5000000,    // 5M product listings
      analyticsData: 1000000,   // 1M analytics records
      adSpendData: 365000,      // 1000 years of daily ad spend
      chartCache: 50000,        // 50K chart cache entries
      userSettings: 1000,       // 1K user settings
      productImages: 5000000    // 5M product images metadata
    };
    this.dataRetentionDays = {
      salesData: 3650,          // 10 years of sales data
      listingsData: -1,         // Keep forever (until manually deleted)
      analyticsData: 365,       // 1 year of analytics
      adSpendData: 3650,        // 10 years of ad spend
      chartCache: 7,            // 1 week chart cache
      userSettings: -1,         // Keep forever
      productImages: -1         // Keep forever
    };

    // Store configurations for massive scale
    this.stores = {
      salesData: { keyPath: 'id', autoIncrement: true },           // Daily sales records
      listingsData: { keyPath: 'asin' },                          // Product listings (5M)
      analyticsData: { keyPath: 'timestamp' },                    // Analytics data
      adSpendData: { keyPath: 'date' },                          // Ad spend data
      chartCache: { keyPath: 'cacheKey' },                       // Chart cache
      userSettings: { keyPath: 'setting' },                      // User settings
      productImages: { keyPath: 'asin' },                        // Product images metadata
      dailySalesHistory: { keyPath: ['asin', 'date'] },          // ASIN + Date composite key
      productMetrics: { keyPath: 'asin' },                       // Aggregated metrics per ASIN
      marketplaceData: { keyPath: ['asin', 'marketplace'] },     // ASIN + Marketplace data
      productSearch: { keyPath: ['token', 'asin'] },             // Search index: token -> ASINs
      settings: { keyPath: 'key' }                               // Settings store
    };
  }

  /**
   * Promisify IndexedDB request operations
   * @param {IDBRequest} request - IndexedDB request object
   * @returns {Promise} Promise that resolves with request result
   */
  promisifyRequest(request) {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Initialize IndexedDB connection
   */
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => {
        if (window.SnapLogger) {
          window.SnapLogger.error('❌ IndexedDB failed to open:', request.error);
        } else {
          console.error('❌ IndexedDB failed to open:', request.error);
        }
        reject(request.error);
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        if (window.SnapLogger) {
          window.SnapLogger.info('✅ IndexedDB connected successfully');
        }
        
        // Setup error handling
        this.db.onerror = (event) => {
          console.error('❌ IndexedDB error:', event.target.error);
        };
        
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        try {
          const db = event.target.result;
          const transaction = event.target.transaction;
          const oldVersion = event.oldVersion;
          const newVersion = event.newVersion;

          console.log(`🔄 Database upgrade needed from version ${oldVersion} to ${newVersion}`);

          // Create object stores for fresh installs (oldVersion === 0)
          if (oldVersion === 0) {
            Object.entries(this.stores).forEach(([storeName, config]) => {
              if (!db.objectStoreNames.contains(storeName)) {
                const store = db.createObjectStore(storeName, config);

                // Add indexes for massive scale queries
                if (storeName === 'salesData') {
                  store.createIndex('timestamp', 'timestamp', { unique: false });
                  store.createIndex('marketplace', 'marketplace', { unique: false });
                  store.createIndex('asin', 'asin', { unique: false });
                }
                if (storeName === 'listingsData') {
                  store.createIndex('lastUpdated', 'lastUpdated', { unique: false });
                  store.createIndex('status', 'status', { unique: false });
                  store.createIndex('marketplace', 'marketplace', { unique: false });
                  store.createIndex('category', 'category', { unique: false });
                  store.createIndex('title', 'title', { unique: false });
                  store.createIndex('price', 'price', { unique: false });
                  // Composite indexes for combined filtering
                  store.createIndex('marketplace_category', ['marketplace', 'category'], { unique: false });
                  store.createIndex('marketplace_lastUpdated', ['marketplace', 'lastUpdated'], { unique: false });
                  store.createIndex('category_lastUpdated', ['category', 'lastUpdated'], { unique: false });
                  store.createIndex('marketplace_category_lastUpdated', ['marketplace', 'category', 'lastUpdated'], { unique: false });
                }
                if (storeName === 'dailySalesHistory') {
                  store.createIndex('asin', 'asin', { unique: false });
                  store.createIndex('date', 'date', { unique: false });
                  store.createIndex('marketplace', 'marketplace', { unique: false });
                  store.createIndex('asin_date', ['asin', 'date'], { unique: true });
                }
                if (storeName === 'productImages') {
                  store.createIndex('lastFetched', 'lastFetched', { unique: false });
                  store.createIndex('size', 'size', { unique: false });
                }
                if (storeName === 'productMetrics') {
                  store.createIndex('totalSales', 'totalSales', { unique: false });
                  store.createIndex('lastSaleDate', 'lastSaleDate', { unique: false });
                }
                if (storeName === 'chartCache') {
                  store.createIndex('expiry', 'expiry', { unique: false });
                }
                if (storeName === 'settings') {
                  store.createIndex('category', 'category', { unique: false });
                }
                if (storeName === 'productSearch') {
                  store.createIndex('token', 'token', { unique: false });
                  store.createIndex('asin', 'asin', { unique: false });
                }

                console.log(`📦 Created object store: ${storeName}`);
              }
            });
          }

          // Perform migrations for existing databases
          if (oldVersion > 0) {
            this.performMigration(db, transaction, oldVersion, newVersion);
          }
        } catch (error) {
          console.error('❌ Database upgrade failed:', error);
          throw error;
        }
      };
    });
  }

  /**
   * Store real-time Amazon data with automatic cleanup
   */
  async storeAmazonData(type, data) {
    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    const transaction = this.db.transaction([type], 'readwrite');
    const store = transaction.objectStore(type);

    try {
      // Add timestamp and expiry
      const retentionDays = this.dataRetentionDays[type];
      const expiry = retentionDays === -1 ? -1 : Date.now() + (retentionDays * 24 * 60 * 60 * 1000);

      const enrichedData = {
        ...data,
        timestamp: Date.now(),
        expiry: expiry
      };

      await this.promisifyRequest(store.put(enrichedData));

      // Trigger cleanup if needed
      await this.cleanupExpiredData(type);
      await this.enforceStorageLimit(type);

      console.log(`💾 Stored ${type} data:`, data.id || data.asin || 'new record');

    } catch (error) {
      console.error(`❌ Failed to store ${type} data:`, error);
      throw error;
    }
  }

  /**
   * Retrieve data with caching (DEPRECATED for large stores)
   * Use iterate() method for large datasets to avoid memory issues
   */
  async getData(type, key = null) {
    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    const transaction = this.db.transaction([type], 'readonly');
    const store = transaction.objectStore(type);

    try {
      let result;

      if (key) {
        result = await this.promisifyRequest(store.get(key));
      } else {
        // Add guard against large record counts
        const count = await this.promisifyRequest(store.count());
        if (count > 10000) {
          console.warn(`⚠️ getData() called on large store '${type}' with ${count} records. Consider using iterate() method instead.`);
          throw new Error(`Store '${type}' has ${count} records. Use iterate() method for large datasets to avoid memory issues.`);
        }
        result = await this.promisifyRequest(store.getAll());
      }

      return result;

    } catch (error) {
      console.error(`❌ Failed to get ${type} data:`, error);
      throw error;
    }
  }

  /**
   * Stream data from IndexedDB using cursor iteration
   * Safe for large datasets as it processes data in batches
   * @param {string} storeName - Name of the store to iterate
   * @param {Object} options - Iteration options
   * @param {string} options.indexName - Optional index name to use
   * @param {IDBKeyRange} options.keyRange - Optional key range to filter
   * @param {number} options.batchSize - Number of records per batch (default: 1000)
   * @param {Function} options.onBatch - Callback function for each batch
   * @returns {Promise<Object>} Iteration statistics
   */
  async iterate(storeName, options = {}) {
    const {
      indexName = null,
      keyRange = null,
      batchSize = 1000,
      onBatch = null
    } = options;

    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    if (!onBatch || typeof onBatch !== 'function') {
      throw new Error('onBatch callback function is required');
    }

    const transaction = this.db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const source = indexName ? store.index(indexName) : store;

    let processedCount = 0;
    let batchCount = 0;
    const startTime = Date.now();

    try {
      return new Promise((resolve, reject) => {
        const request = source.openCursor(keyRange);
        let currentBatch = [];

        request.onsuccess = async (event) => {
          const cursor = event.target.result;

          if (cursor) {
            currentBatch.push(cursor.value);
            processedCount++;

            // Process batch when it reaches the specified size
            if (currentBatch.length >= batchSize) {
              try {
                await onBatch(currentBatch, batchCount);
                batchCount++;
                currentBatch = [];

                // Allow other operations to proceed using requestIdleCallback when available
                if (typeof requestIdleCallback !== 'undefined') {
                  requestIdleCallback(() => cursor.continue());
                } else {
                  setTimeout(() => cursor.continue(), 0);
                }
              } catch (error) {
                reject(new Error(`Error processing batch ${batchCount}: ${error.message}`));
                return;
              }
            } else {
              cursor.continue();
            }
          } else {
            // Process final batch if it has any records
            if (currentBatch.length > 0) {
              try {
                await onBatch(currentBatch, batchCount);
                batchCount++;
              } catch (error) {
                reject(new Error(`Error processing final batch: ${error.message}`));
                return;
              }
            }

            // Return iteration statistics
            const duration = Date.now() - startTime;
            resolve({
              processedCount,
              batchCount,
              duration,
              recordsPerSecond: Math.round(processedCount / (duration / 1000))
            });
          }
        };

        request.onerror = () => reject(request.error);
      });

    } catch (error) {
      console.error(`❌ Failed to iterate ${storeName}:`, error);
      throw error;
    }
  }

  /**
   * Stream data from IndexedDB using async generator pattern
   * Yields batches of data for streaming operations
   * @param {string} storeName - Name of the store to stream
   * @param {Object} options - Stream options
   * @param {string} options.indexName - Optional index name to use
   * @param {IDBKeyRange} options.keyRange - Optional key range to filter
   * @param {number} options.batchSize - Number of records per batch (default: 1000)
   * @returns {AsyncGenerator<Array>} Async generator yielding batches of data
   */
  async* stream(storeName, options = {}) {
    const {
      indexName = null,
      keyRange = null,
      batchSize = 1000
    } = options;

    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    const transaction = this.db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    const source = indexName ? store.index(indexName) : store;

    let processedCount = 0;
    let batchCount = 0;

    try {
      const request = source.openCursor(keyRange);
      let currentBatch = [];

      while (true) {
        const cursor = await new Promise((resolve, reject) => {
          request.onsuccess = (event) => resolve(event.target.result);
          request.onerror = () => reject(request.error);
        });

        if (cursor) {
          currentBatch.push(cursor.value);
          processedCount++;

          // Yield batch when it reaches the specified size
          if (currentBatch.length >= batchSize) {
            yield {
              data: currentBatch,
              batchNumber: batchCount,
              processedCount,
              hasMore: true
            };
            batchCount++;
            currentBatch = [];

            // Allow other operations to proceed using requestIdleCallback if available
            await new Promise(resolve => {
              if (typeof requestIdleCallback !== 'undefined') {
                requestIdleCallback(() => {
                  cursor.continue();
                  resolve();
                });
              } else {
                setTimeout(() => {
                  cursor.continue();
                  resolve();
                }, 0);
              }
            });
          } else {
            cursor.continue();
          }
        } else {
          // Yield final batch if it has any records
          if (currentBatch.length > 0) {
            yield {
              data: currentBatch,
              batchNumber: batchCount,
              processedCount,
              hasMore: false
            };
          }
          break;
        }
      }
    } catch (error) {
      console.error(`❌ Failed to stream ${storeName}:`, error);
      throw error;
    }
  }

  /**
   * Clean up expired data automatically using cursor iteration
   */
  async cleanupExpiredData(storeName) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const now = Date.now();
      let deletedCount = 0;
      let batchCount = 0;
      const batchSize = 100; // Process deletions in batches

      const request = store.openCursor();

      request.onsuccess = (event) => {
        const cursor = event.target.result;

        if (cursor) {
          const record = cursor.value;

          // Guard against deleting permanent data (expiry -1)
          if (record.expiry && record.expiry !== -1 && record.expiry < now) {
            cursor.delete();
            deletedCount++;
            batchCount++;

            // Process in batches to avoid memory issues
            if (batchCount >= batchSize) {
              batchCount = 0;
              // Small delay to prevent blocking
              setTimeout(() => cursor.continue(), 0);
            } else {
              cursor.continue();
            }
          } else {
            cursor.continue();
          }
        } else {
          // Finished processing
          if (deletedCount > 0) {
            console.log(`🗑️ Cleaned up ${deletedCount} expired records from ${storeName}`);
          }
          resolve(deletedCount);
        }
      };

      request.onerror = () => {
        console.error(`❌ Failed to cleanup ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Enforce storage size limits per store type using cursor iteration
   */
  async enforceStorageLimit(storeName) {
    return new Promise(async (resolve, reject) => {
      try {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);

        const count = await this.promisifyRequest(store.count());
        const maxRecords = this.maxRecords[storeName] || this.maxRecords.salesData;

        if (count <= maxRecords) {
          resolve(0);
          return;
        }

        // Calculate how many to remove (remove 10% extra to avoid frequent cleanup)
        const recordsToRemove = count - Math.floor(maxRecords * 0.9);
        let removedCount = 0;
        const batchSize = 50; // Process deletions in smaller batches

        // Select appropriate index for cleanup with safeguards
        let indexName = null;
        try {
          if (store.indexNames && store.indexNames.contains && store.indexNames.contains('timestamp')) {
            indexName = 'timestamp';
          } else if (store.indexNames && store.indexNames.contains && store.indexNames.contains('lastUpdated')) {
            indexName = 'lastUpdated';
          } else if (store.indexNames && store.indexNames.contains && store.indexNames.contains('date')) {
            indexName = 'date';
          }
        } catch (error) {
          console.warn(`⚠️ Error checking indexes for ${storeName}:`, error);
          indexName = null;
        }

        if (indexName) {
          const index = store.index(indexName);
          const request = index.openCursor();

          request.onsuccess = (event) => {
            const cursor = event.target.result;

            if (cursor && removedCount < recordsToRemove) {
              cursor.delete();
              removedCount++;

              // Process in batches to avoid blocking
              if (removedCount % batchSize === 0) {
                setTimeout(() => cursor.continue(), 0);
              } else {
                cursor.continue();
              }
            } else {
              console.log(`🗑️ Removed ${removedCount} old records from ${storeName} (by ${indexName})`);
              resolve(removedCount);
            }
          };

          request.onerror = () => {
            console.error(`❌ Failed to enforce storage limit for ${storeName}:`, request.error);
            reject(request.error);
          };
        } else {
          // Fallback: remove first N records using cursor iteration
          const request = store.openCursor();
          let removedCount = 0;

          request.onsuccess = (event) => {
            const cursor = event.target.result;

            if (cursor && removedCount < recordsToRemove) {
              cursor.delete();
              removedCount++;

              if (removedCount % batchSize === 0) {
                setTimeout(() => cursor.continue(), 0);
              } else {
                cursor.continue();
              }
            } else {
              console.log(`🗑️ Removed ${removedCount} old records from ${storeName} (FIFO)`);
              resolve(removedCount);
            }
          };

          request.onerror = () => {
            console.error(`❌ Failed to enforce storage limit for ${storeName}:`, request.error);
            reject(request.error);
          };
        }
      } catch (error) {
        console.error(`❌ Failed to enforce storage limit for ${storeName}:`, error);
        reject(error);
      }
    });
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats() {
    if (!this.db) return null;

    const stats = {};

    for (const storeName of Object.keys(this.stores)) {
      try {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const count = await this.promisifyRequest(store.count());

        stats[storeName] = {
          recordCount: count,
          maxRecords: this.maxRecords[storeName],
          usage: `${count}/${this.maxRecords[storeName]}`
        };

      } catch (error) {
        stats[storeName] = { error: error.message };
      }
    }

    return stats;
  }

  /**
   * Clear all data (for debugging/reset)
   */
  async clearAllData() {
    if (!this.db) return;

    for (const storeName of Object.keys(this.stores)) {
      try {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        await this.promisifyRequest(store.clear());
        console.log(`🗑️ Cleared all data from ${storeName}`);
      } catch (error) {
        console.error(`❌ Failed to clear ${storeName}:`, error);
      }
    }
  }

  /**
   * Bulk operations for efficient data import/export
   * @param {string} storeName - Store name
   * @param {Array} records - Records to insert
   * @returns {Promise<number>} Number of records inserted
   */
  async bulkPut(storeName, records) {
    if (!records || records.length === 0) return 0;

    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);

      let insertedCount = 0;
      const batchSize = 1000;

      for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize);

        for (const record of batch) {
          await this.promisifyRequest(store.put(record));
          insertedCount++;
        }

        // Allow other operations to proceed
        if (i % (batchSize * 10) === 0) {
          await new Promise(resolve => setTimeout(resolve, 0));
        }
      }

      console.log(`📦 Bulk inserted ${insertedCount} records into ${storeName}`);
      return insertedCount;
    } catch (error) {
      console.error('❌ Error in bulk put operation:', error);
      throw error;
    }
  }

  /**
   * Bulk delete operations
   * @param {string} storeName - Store name
   * @param {Array} keys - Keys to delete
   * @returns {Promise<number>} Number of records deleted
   */
  async bulkDelete(storeName, keys) {
    if (!keys || keys.length === 0) return 0;

    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);

      let deletedCount = 0;

      for (const key of keys) {
        await this.promisifyRequest(store.delete(key));
        deletedCount++;
      }

      console.log(`🗑️ Bulk deleted ${deletedCount} records from ${storeName}`);
      return deletedCount;
    } catch (error) {
      console.error('❌ Error in bulk delete operation:', error);
      throw error;
    }
  }

  /**
   * Data integrity checking for massive datasets
   * @param {string} storeName - Store name to check
   * @returns {Promise<Object>} Integrity check result
   */
  async checkDataIntegrity(storeName) {
    try {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);

      const issues = [];
      let recordCount = 0;
      let corruptedCount = 0;

      return new Promise((resolve, reject) => {
        const request = store.openCursor();

        request.onsuccess = (event) => {
          const cursor = event.target.result;

          if (cursor) {
            recordCount++;
            const record = cursor.value;

            // Check for corruption
            if (this.isRecordCorrupted(record, storeName)) {
              corruptedCount++;
              issues.push({
                key: cursor.key,
                issue: 'corrupted_data',
                record: record
              });
            }

            cursor.continue();
          } else {
            const integrityScore = recordCount > 0 ? ((recordCount - corruptedCount) / recordCount) * 100 : 100;
            resolve({
              storeName,
              totalRecords: recordCount,
              corruptedRecords: corruptedCount,
              issues,
              integrityScore
            });
          }
        };

        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('❌ Error checking data integrity:', error);
      throw error;
    }
  }

  /**
   * Database migration system for version upgrades
   * @param {number} newVersion - New database version
   * @returns {Promise<boolean>} Migration success
   */
  async migrateDatabase(newVersion) {
    try {
      // Close current connection
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      // Open with new version to trigger upgrade
      const request = indexedDB.open(this.dbName, newVersion);

      return new Promise((resolve, reject) => {
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          const transaction = event.target.transaction;
          const oldVersion = event.oldVersion;

          console.log(`🔄 Migrating database from version ${oldVersion} to ${newVersion}`);

          // Perform migration based on version differences
          this.performMigration(db, transaction, oldVersion, newVersion);
        };

        request.onsuccess = (event) => {
          this.db = event.target.result;
          console.log(`✅ Database migration completed to version ${newVersion}`);
          resolve(true);
        };

        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('❌ Database migration failed:', error);
      throw error;
    }
  }

  /**
   * Get database storage usage statistics
   * @returns {Promise<Object>} Storage statistics
   */
  async getDatabaseStorageStats() {
    try {
      const estimate = await navigator.storage.estimate();

      const stats = {
        quota: estimate.quota,
        usage: estimate.usage,
        available: estimate.quota - estimate.usage,
        usagePercentage: (estimate.usage / estimate.quota) * 100
      };

      // Get per-store statistics
      for (const storeName of Object.keys(this.stores)) {
        const count = await this.getRecordCount(storeName);
        stats[storeName] = {
          recordCount: count,
          estimatedSize: count * this.getAverageRecordSize(storeName)
        };
      }

      return stats;
    } catch (error) {
      console.error('❌ Error getting storage stats:', error);
      return {
        quota: 0,
        usage: 0,
        available: 0,
        usagePercentage: 0
      };
    }
  }

  /**
   * Get database instance for external managers
   * @returns {IDBDatabase} Database instance
   */
  getDB() {
    return this.db;
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('🔒 IndexedDB connection closed');
    }
  }

  // Private helper methods for new functionality
  isRecordCorrupted(record, storeName) {
    if (!record || typeof record !== 'object') return true;

    // Store-specific validation
    switch (storeName) {
      case 'listingsData':
        return !record.asin || !record.title || typeof record.price !== 'number';
      case 'dailySalesHistory':
        return !record.asin || !record.date || typeof record.revenue !== 'number';
      default:
        return false;
    }
  }

  performMigration(db, transaction, oldVersion, newVersion) {
    try {
      console.log(`🔄 Performing migration from version ${oldVersion} to ${newVersion}`);

      // Migration logic based on version differences
      if (oldVersion < 2) {
        // Create productSearch store if it doesn't exist
        if (!db.objectStoreNames.contains('productSearch')) {
          const store = db.createObjectStore('productSearch', { keyPath: ['token', 'asin'] });
          store.createIndex('token', 'token', { unique: false });
          store.createIndex('asin', 'asin', { unique: false });
          console.log('✅ Created productSearch store with indexes');
        }

        // Add missing indexes to listingsData
        if (db.objectStoreNames.contains('listingsData')) {
          const listingsStore = transaction.objectStore('listingsData');

          // Add marketplace_category_lastUpdated index if missing
          if (!listingsStore.indexNames.contains('marketplace_category_lastUpdated')) {
            listingsStore.createIndex('marketplace_category_lastUpdated', ['marketplace', 'category', 'lastUpdated'], { unique: false });
            console.log('✅ Added marketplace_category_lastUpdated index to listingsData');
          }

          // Ensure other required indexes exist
          if (!listingsStore.indexNames.contains('category')) {
            listingsStore.createIndex('category', 'category', { unique: false });
            console.log('✅ Added category index to listingsData');
          }

          // Add sorting indexes
          if (!listingsStore.indexNames.contains('title')) {
            listingsStore.createIndex('title', 'title', { unique: false });
            console.log('✅ Added title index to listingsData');
          }

          if (!listingsStore.indexNames.contains('price')) {
            listingsStore.createIndex('price', 'price', { unique: false });
            console.log('✅ Added price index to listingsData');
          }
        }
      }

      if (oldVersion < 3) {
        // Add new store for version 3
        if (!db.objectStoreNames.contains('settings')) {
          const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
          settingsStore.createIndex('category', 'category', { unique: false });
          console.log('✅ Created settings store');
        }
      }

      console.log(`✅ Migration completed successfully to version ${newVersion}`);
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  async getRecordCount(storeName) {
    try {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);

      return await this.promisifyRequest(store.count());
    } catch (error) {
      console.error(`❌ Error counting records in ${storeName}:`, error);
      return 0;
    }
  }

  getAverageRecordSize(storeName) {
    // Estimated average record sizes in bytes
    const sizes = {
      'listingsData': 1024, // 1KB per product
      'dailySalesHistory': 256      // 256 bytes per sale
    };
    return sizes[storeName] || 512;
  }
}

// Global instance for Chrome extension
window.IndexedDBManager = new IndexedDBManager();

// Auto-initialize when script loads
window.IndexedDBManager.init().catch(error => {
  console.error('❌ Failed to initialize IndexedDB:', error);
});

// Cleanup on extension unload
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onSuspend) {
  chrome.runtime.onSuspend.addListener(() => {
    window.IndexedDBManager.close();
  });
}

// Export for global use
window.storeAmazonData = (type, data) => window.IndexedDBManager.storeAmazonData(type, data);

// DEPRECATED: Use QueryManager.getProductsPaginated() for large datasets
// This function will throw an error for stores with >10,000 records
window.getStoredData = (type, key) => {
  if (type === 'listingsData' && !key) {
    console.warn('⚠️ DEPRECATED: getStoredData("listingsData") without key. Use QueryManager.getProductsPaginated() instead.');
  }
  return window.IndexedDBManager.getData(type, key);
};

window.getStorageStats = () => window.IndexedDBManager.getStorageStats();
