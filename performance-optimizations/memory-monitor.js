/**
 * Memory Monitor for Chrome Extension
 * Tracks memory usage and triggers cleanup when needed
 */

class MemoryMonitor {
  constructor() {
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.checkFrequency = 60000; // Check every minute
    this.memoryHistory = [];
    this.maxHistorySize = 100;
    
    // Memory thresholds for massive scale (in MB)
    this.thresholds = {
      warning: 500,    // 500MB - start warning (5M products needs more memory)
      critical: 1000,  // 1GB - force cleanup
      emergency: 1500  // 1.5GB - emergency cleanup
    };
    
    // Performance metrics
    this.metrics = {
      heapUsed: 0,
      heapTotal: 0,
      domNodes: 0,
      eventListeners: 0,
      cacheSize: 0,
      indexedDBSize: 0
    };

    // DOM counting performance optimization
    this.lastDOMCount = 0;
    this.skipDOMCountUntil = 0;
  }

  /**
   * Start memory monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    if (window.SnapLogger) {
      window.SnapLogger.info('🔍 Starting memory monitoring...');
    }
    this.isMonitoring = true;

    // Initial check
    this.checkMemoryUsage();

    // Setup interval using tracked interval or native fallback
    if (window.EventCleanupManager) {
      this.monitorInterval = window.EventCleanupManager.setInterval(
        () => this.checkMemoryUsage(),
        this.checkFrequency
      );
    } else {
      this.monitorInterval = setInterval(
        () => this.checkMemoryUsage(),
        this.checkFrequency
      );
    }

    if (window.SnapLogger) {
      window.SnapLogger.info(`✅ Memory monitoring started (${this.checkFrequency}ms interval)`);
    }
  }

  /**
   * Stop memory monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    if (window.SnapLogger) {
      window.SnapLogger.info('🛑 Stopping memory monitoring...');
    }
    this.isMonitoring = false;

    if (this.monitorInterval) {
      if (window.EventCleanupManager) {
        window.EventCleanupManager.clearInterval(this.monitorInterval);
      } else {
        clearInterval(this.monitorInterval);
      }
      this.monitorInterval = null;
    }

    if (window.SnapLogger) {
      window.SnapLogger.info('✅ Memory monitoring stopped');
    }
  }

  /**
   * Check current memory usage
   */
  async checkMemoryUsage() {
    try {
      // Get browser memory info
      const memoryInfo = this.getBrowserMemoryInfo();
      
      // Get application metrics
      const appMetrics = await this.getApplicationMetrics();
      
      // Combine metrics
      this.metrics = { ...memoryInfo, ...appMetrics };
      
      // Add to history
      this.addToHistory(this.metrics);
      
      // Check thresholds and take action
      this.checkThresholds();
      
      // Log current status
      this.logMemoryStatus();
      
    } catch (error) {
      if (window.SnapLogger) {
        window.SnapLogger.error('❌ Error checking memory usage:', error);
      } else {
        console.error('❌ Error checking memory usage:', error);
      }
    }
  }

  /**
   * Get browser memory information
   */
  getBrowserMemoryInfo() {
    const info = {
      heapUsed: 0,
      heapTotal: 0,
      timestamp: Date.now()
    };
    
    // Use performance.memory if available (Chrome)
    if (performance.memory) {
      info.heapUsed = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      info.heapTotal = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
    }
    
    return info;
  }

  /**
   * Get DOM node count safely with performance gating
   */
  getDOMNodeCountSafely() {
    // Check if DOM counting is enabled in settings
    const enableMonitoring = window.settingsManager?.get('performance.enableMonitoring') !== false;
    if (!enableMonitoring) {
      return 0; // Skip DOM counting if monitoring is disabled
    }

    // Check if we should skip DOM counting due to previous slow performance
    if (Date.now() < this.skipDOMCountUntil) {
      return this.lastDOMCount || 0;
    }

    try {
      const startTime = performance.now();
      const nodeCount = document.querySelectorAll('*').length;
      const duration = performance.now() - startTime;

      // If DOM counting takes too long, disable it temporarily
      if (duration > 50) { // 50ms threshold
        console.warn(`⚠️ DOM node counting took ${duration.toFixed(2)}ms, consider disabling performance monitoring`);

        // Cache the result and skip next few checks
        this.lastDOMCount = nodeCount;
        this.skipDOMCountUntil = Date.now() + 60000; // Skip for 1 minute

        return nodeCount;
      }

      // Update cached value for successful fast counts
      this.lastDOMCount = nodeCount;
      return nodeCount;
    } catch (error) {
      console.warn('⚠️ Failed to count DOM nodes:', error);
      return this.lastDOMCount || 0;
    }
  }

  /**
   * Get application-specific metrics
   */
  async getApplicationMetrics() {
    const metrics = {};

    try {
      // DOM node count (gated for performance)
      metrics.domNodes = this.getDOMNodeCountSafely();

      // Event listener count
      if (window.EventCleanupManager) {
        const stats = window.EventCleanupManager.getStats();
        metrics.eventListeners = stats.listeners + stats.observers + stats.intervals + stats.timeouts;
      }

      // Cache size
      if (window.DataCacheManager) {
        const cacheStats = window.DataCacheManager.getStats();
        metrics.cacheSize = cacheStats.size;
      }

      // IndexedDB size estimate
      if (window.IndexedDBManager) {
        const dbStats = await window.IndexedDBManager.getStorageStats();
        if (dbStats && typeof dbStats === 'object') {
          metrics.indexedDBSize = Object.values(dbStats).reduce((total, store) => {
            return total + (store.recordCount || 0);
          }, 0);
        } else {
          metrics.indexedDBSize = 0;
        }
      }
      
    } catch (error) {
      if (window.SnapLogger) {
        window.SnapLogger.error('❌ Error getting application metrics:', error);
      } else {
        console.error('❌ Error getting application metrics:', error);
      }
    }
    
    return metrics;
  }

  /**
   * Add metrics to history
   */
  addToHistory(metrics) {
    this.memoryHistory.push(metrics);
    
    // Limit history size
    if (this.memoryHistory.length > this.maxHistorySize) {
      this.memoryHistory.shift();
    }
  }

  /**
   * Check memory thresholds and trigger actions
   */
  checkThresholds() {
    const heapUsed = this.metrics.heapUsed;
    
    if (heapUsed >= this.thresholds.emergency) {
      if (window.SnapLogger) {
        window.SnapLogger.error(`🚨 EMERGENCY: Memory usage ${heapUsed}MB exceeds emergency threshold!`);
      } else {
        console.error(`🚨 EMERGENCY: Memory usage ${heapUsed}MB exceeds emergency threshold!`);
      }
      this.triggerEmergencyCleanup();
      
    } else if (heapUsed >= this.thresholds.critical) {
      console.warn(`⚠️ CRITICAL: Memory usage ${heapUsed}MB exceeds critical threshold!`);
      this.triggerCriticalCleanup();
      
    } else if (heapUsed >= this.thresholds.warning) {
      console.warn(`⚠️ WARNING: Memory usage ${heapUsed}MB exceeds warning threshold`);
      this.triggerWarningCleanup();
    }
  }

  /**
   * Trigger warning-level cleanup
   */
  triggerWarningCleanup() {
    console.log('🧹 Triggering warning-level cleanup...');

    // Clear old cache entries
    if (window.DataCacheManager) {
      window.DataCacheManager.clearCache();
    }

    // Clean up expired IndexedDB data
    if (window.IndexedDBManager) {
      Object.keys(window.IndexedDBManager.stores).forEach(storeName => {
        window.IndexedDBManager.cleanupExpiredData(storeName);
      });
    }
  }

  /**
   * Trigger critical-level cleanup
   */
  triggerCriticalCleanup() {
    console.log('🧹 Triggering critical-level cleanup...');
    
    // Do warning cleanup first
    this.triggerWarningCleanup();
    
    // Stop real-time updates temporarily
    if (window.RealTimeDataManager && window.RealTimeDataManager.isActive) {
      console.log('⏸️ Temporarily stopping real-time updates...');
      window.RealTimeDataManager.stopRealTimeUpdates();
      
      // Restart after 30 seconds
      setTimeout(() => {
        if (this.metrics.heapUsed < this.thresholds.critical) {
          console.log('▶️ Restarting real-time updates...');
          window.RealTimeDataManager.startRealTimeUpdates();
        }
      }, 30000);
    }
    
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  /**
   * Trigger emergency-level cleanup
   */
  triggerEmergencyCleanup() {
    console.log('🚨 Triggering emergency cleanup...');
    
    // Do critical cleanup first
    this.triggerCriticalCleanup();
    
    // Clear all non-essential data
    if (window.DataCacheManager) {
      window.DataCacheManager.cache.clear();
    }
    
    // Stop all real-time operations
    if (window.RealTimeDataManager) {
      window.RealTimeDataManager.stopRealTimeUpdates();
    }
    
    // Dispatch emergency event for UI to handle
    window.dispatchEvent(new CustomEvent('memoryEmergency', {
      detail: { heapUsed: this.metrics.heapUsed }
    }));
  }

  /**
   * Log current memory status
   */
  logMemoryStatus() {
    const status = this.getMemoryStatus();
    
    if (status.level === 'normal') {
      // Only log every 10 checks for normal status
      if (this.memoryHistory.length % 10 === 0) {
        console.log('💚 Memory status: Normal', status);
      }
    } else {
      console.log(`📊 Memory status: ${status.level}`, status);
    }
  }

  /**
   * Get current memory status
   */
  getMemoryStatus() {
    const heapUsed = this.metrics.heapUsed;
    let level = 'normal';

    if (heapUsed >= this.thresholds.emergency) {
      level = 'emergency';
    } else if (heapUsed >= this.thresholds.critical) {
      level = 'critical';
    } else if (heapUsed >= this.thresholds.warning) {
      level = 'warning';
    }

    return {
      level,
      heapUsed: `${heapUsed}MB`,
      heapTotal: `${this.metrics.heapTotal}MB`,
      domNodes: this.metrics.domNodes,
      eventListeners: this.metrics.eventListeners,
      cacheSize: this.metrics.cacheSize,
      indexedDBRecords: this.metrics.indexedDBSize,
      thresholds: this.thresholds
    };
  }

  /**
   * Get current memory usage (for compatibility with StressTestHarness and RealTimeDataManager)
   * @returns {Object} Current heap usage information
   */
  getCurrentUsage() {
    return {
      heapUsedMB: this.metrics.heapUsed,
      heapTotalMB: this.metrics.heapTotal,
      heapUsed: this.metrics.heapUsed * 1024 * 1024, // Convert MB to bytes for compatibility
      heapTotal: this.metrics.heapTotal * 1024 * 1024,
      timestamp: Date.now()
    };
  }

  /**
   * Get memory trend analysis
   */
  getMemoryTrend() {
    if (this.memoryHistory.length < 2) return 'insufficient_data';
    
    const recent = this.memoryHistory.slice(-5);
    const trend = recent[recent.length - 1].heapUsed - recent[0].heapUsed;
    
    if (trend > 10) return 'increasing';
    if (trend < -10) return 'decreasing';
    return 'stable';
  }

  /**
   * Export memory report
   */
  getMemoryReport() {
    return {
      current: this.getMemoryStatus(),
      trend: this.getMemoryTrend(),
      history: this.memoryHistory.slice(-10), // Last 10 entries
      thresholds: this.thresholds,
      isMonitoring: this.isMonitoring
    };
  }

  /**
   * Enhanced memory monitoring for massive datasets
   * Adjusts thresholds and cleanup strategies for 5M+ products
   */
  configureMassiveScaleThresholds(settings = {}) {
    // Adjust thresholds based on available memory and dataset size
    const deviceMemory = navigator.deviceMemory || 4; // GB
    const baseMemory = Math.min(deviceMemory * 1024 * 0.6, 2048); // Use 60% of device memory, max 2GB

    this.thresholds = {
      warning: settings.warningThreshold || Math.floor(baseMemory * 0.7),
      critical: settings.criticalThreshold || Math.floor(baseMemory * 0.85),
      emergency: settings.emergencyThreshold || Math.floor(baseMemory * 0.95),
      ...settings
    };

    console.log(`🧠 Memory thresholds configured for massive scale:`, this.thresholds);
  }

  /**
   * Monitor IndexedDB storage usage and query performance
   */
  async monitorIndexedDBPerformance() {
    try {
      const storageStats = await window.IndexedDBManager?.getDatabaseStorageStats();

      if (storageStats) {
        this.metrics.indexedDBUsage = Math.round(storageStats.usage / (1024 * 1024)); // MB
        this.metrics.indexedDBQuota = Math.round(storageStats.quota / (1024 * 1024)); // MB
        this.metrics.indexedDBPercentage = storageStats.usagePercentage;

        // Monitor for storage quota warnings
        if (storageStats.usagePercentage > 80) {
          console.warn(`⚠️ IndexedDB storage usage high: ${storageStats.usagePercentage.toFixed(1)}%`);
          this.triggerStorageCleanup();
        }
      }
    } catch (error) {
      console.error('❌ Error monitoring IndexedDB performance:', error);
    }
  }

  /**
   * Monitor virtual list efficiency and performance
   */
  monitorVirtualListPerformance() {
    const virtualLists = document.querySelectorAll('.virtual-list');
    let totalRenderedItems = 0;
    let totalLoadedPages = 0;
    let totalActiveElements = 0;

    virtualLists.forEach(list => {
      if (list.virtualListInstance) {
        // Get detailed stats from VirtualProductList instances
        if (typeof list.virtualListInstance.getMemoryStats === 'function') {
          const stats = list.virtualListInstance.getMemoryStats();
          totalLoadedPages += stats.loadedPages || 0;
          totalActiveElements += stats.activeElements || 0;
          totalRenderedItems += stats.visibleItems || 0;

          // Log detailed stats for debugging
          console.log(`📊 VirtualList stats:`, stats);
        } else {
          // Fallback to DOM counting
          const renderedItems = list.querySelectorAll('.virtual-list-item');
          totalRenderedItems += renderedItems.length;
        }
      }
    });

    // Update metrics
    this.metrics.virtualListItems = totalRenderedItems;
    this.metrics.virtualListPages = totalLoadedPages;
    this.metrics.virtualListActiveElements = totalActiveElements;

    // Check for virtual list efficiency issues
    if (totalRenderedItems > 1000) {
      console.warn(`⚠️ High virtual list item count: ${totalRenderedItems}`);
      this.optimizeVirtualListPerformance();
    }

    if (totalLoadedPages > 20) {
      console.warn(`⚠️ High virtual list page count: ${totalLoadedPages}`);
      this.clearVirtualListCaches();
    }
  }

  /**
   * Predictive memory management using machine learning-like patterns
   */
  predictiveMemoryManagement() {
    if (this.memoryHistory.length < 10) return;

    // Analyze memory usage patterns
    const recentHistory = this.memoryHistory.slice(-10);
    const memoryGrowthRate = this.calculateMemoryGrowthRate(recentHistory);
    const currentUsage = this.metrics.heapUsed;

    // Predict memory usage in next 5 minutes
    const predictedUsage = currentUsage + (memoryGrowthRate * 5);

    // Proactive cleanup if predicted usage exceeds thresholds
    if (predictedUsage > this.thresholds.warning && currentUsage < this.thresholds.warning) {
      console.log(`🔮 Predictive cleanup triggered. Current: ${currentUsage}MB, Predicted: ${predictedUsage.toFixed(1)}MB`);
      this.triggerProactiveCleanup();
    }

    this.metrics.predictedMemoryUsage = predictedUsage;
  }

  /**
   * Integration with settings system for configurable thresholds
   */
  updateFromSettings(settingsManager) {
    if (!settingsManager) return;

    const memorySettings = settingsManager.getManagerSettings('memory');

    if (memorySettings.maxMemoryUsage) {
      this.configureMassiveScaleThresholds({
        warning: memorySettings.maxMemoryUsage * 0.7,
        critical: memorySettings.maxMemoryUsage * 0.85,
        emergency: memorySettings.maxMemoryUsage * 0.95
      });
    }

    if (memorySettings.monitoringInterval) {
      this.stopMonitoring();
      this.checkFrequency = memorySettings.monitoringInterval;
      this.startMonitoring();
    }

    this.aggressiveCleanup = memorySettings.aggressiveCleanup || false;

    console.log(`⚙️ Memory monitor updated from settings`);
  }

  /**
   * Enhanced memory trend analysis with volatility detection
   */
  calculateAdvancedMemoryTrend() {
    if (this.memoryHistory.length < 5) return { trend: 'insufficient_data' };

    const recent = this.memoryHistory.slice(-10);
    const values = recent.map(entry => entry.heapUsed);

    // Calculate trend
    const trend = this.getMemoryTrend();

    // Calculate volatility (standard deviation)
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const volatility = Math.sqrt(variance);

    // Calculate growth rate
    const growthRate = this.calculateMemoryGrowthRate(recent);

    return {
      trend,
      volatility: volatility.toFixed(2),
      growthRate: growthRate.toFixed(2),
      stability: volatility < 10 ? 'stable' : volatility < 50 ? 'moderate' : 'volatile',
      recommendation: this.getAdvancedRecommendation(trend, volatility, growthRate)
    };
  }

  /**
   * Browser storage quota monitoring
   */
  async monitorBrowserStorageLimits() {
    try {
      const estimate = await navigator.storage.estimate();
      const usagePercentage = (estimate.usage / estimate.quota) * 100;

      this.metrics.storageQuota = Math.round(estimate.quota / (1024 * 1024)); // MB
      this.metrics.storageUsage = Math.round(estimate.usage / (1024 * 1024)); // MB
      this.metrics.storagePercentage = usagePercentage;

      // Early warning for storage limits
      if (usagePercentage > 75) {
        console.warn(`⚠️ Browser storage usage high: ${usagePercentage.toFixed(1)}%`);

        if (usagePercentage > 90) {
          console.error(`🚨 Browser storage critical: ${usagePercentage.toFixed(1)}%`);
          this.triggerStorageEmergencyCleanup();
        }
      }
    } catch (error) {
      console.error('❌ Error monitoring storage limits:', error);
    }
  }

  /**
   * Force cleanup for emergency situations
   */
  forceCleanup() {
    console.log('🧹 Force cleanup initiated');

    // Clear all caches
    if (window.DataCacheManager) {
      window.DataCacheManager.clearCache();
    }

    // Trigger garbage collection if available
    if (window.gc) {
      window.gc();
    }

    // Clear virtual list caches
    this.clearVirtualListCaches();

    // Clear query caches
    if (window.queryManager) {
      window.queryManager.clearCache();
    }

    // Force DOM cleanup
    this.forceDOMCleanup();

    console.log('✅ Force cleanup completed');
  }

  // Private helper methods for new functionality
  calculateMemoryGrowthRate(history) {
    if (history.length < 2) return 0;

    const first = history[0].heapUsed;
    const last = history[history.length - 1].heapUsed;
    const timeSpan = history[history.length - 1].timestamp - history[0].timestamp;

    // Growth rate in MB per minute
    return ((last - first) / (timeSpan / 60000));
  }

  triggerProactiveCleanup() {
    console.log('🔮 Proactive memory cleanup triggered');

    // Gentle cleanup to prevent predicted memory issues
    if (window.DataCacheManager) {
      window.DataCacheManager.cleanupOldEntries();
    }

    // Optimize virtual list rendering
    this.optimizeVirtualListPerformance();
  }

  triggerStorageCleanup() {
    console.log('💾 Storage cleanup triggered');

    // Clean up old IndexedDB data
    if (window.IndexedDBManager) {
      window.IndexedDBManager.cleanupExpiredData('listingsData');
      window.IndexedDBManager.cleanupExpiredData('dailySalesHistory');
    }
  }

  triggerStorageEmergencyCleanup() {
    console.log('🚨 Emergency storage cleanup triggered');

    // Aggressive storage cleanup
    this.triggerStorageCleanup();

    // Clear all non-essential data
    if (window.DataCacheManager) {
      window.DataCacheManager.clearCache();
    }

    // Clear browser caches if possible
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name));
      });
    }
  }

  optimizeVirtualListPerformance() {
    console.log('🔧 Optimizing VirtualProductList performance for memory pressure');

    const virtualLists = document.querySelectorAll('.virtual-list');
    let optimizedCount = 0;

    virtualLists.forEach(list => {
      if (list.virtualListInstance) {
        // Use the new optimization method if available
        if (typeof list.virtualListInstance.optimizeBufferSize === 'function') {
          list.virtualListInstance.optimizeBufferSize();
          optimizedCount++;
        } else {
          // Fallback to direct buffer size reduction
          const originalSize = list.virtualListInstance.bufferSize;
          list.virtualListInstance.bufferSize = Math.max(3, Math.floor(originalSize * 0.7));
          console.log(`📊 VirtualList buffer optimized: ${originalSize} → ${list.virtualListInstance.bufferSize}`);
          optimizedCount++;
        }
      }
    });

    if (optimizedCount > 0) {
      console.log(`✅ Optimized ${optimizedCount} VirtualProductList instances`);
    }
  }

  clearVirtualListCaches() {
    console.log('🧹 Clearing VirtualProductList caches for memory optimization');

    const virtualLists = document.querySelectorAll('.virtual-list');
    let clearedCount = 0;

    virtualLists.forEach(list => {
      if (list.virtualListInstance && typeof list.virtualListInstance.clearCache === 'function') {
        list.virtualListInstance.clearCache();
        clearedCount++;
      }
    });

    if (clearedCount > 0) {
      console.log(`✅ Cleared caches for ${clearedCount} VirtualProductList instances`);
    } else {
      console.log('ℹ️ No VirtualProductList instances found with clearCache method');
    }
  }

  forceDOMCleanup() {
    // Remove unused DOM elements
    const unusedElements = document.querySelectorAll('[data-cleanup="true"]');
    unusedElements.forEach(element => element.remove());

    // Clear event listeners on removed elements
    this.cleanupEventListeners();
  }

  cleanupEventListeners() {
    // This would need specific implementation based on how event listeners are tracked
    console.log('🧹 Cleaning up event listeners');
  }

  getAdvancedRecommendation(trend, volatility, growthRate) {
    if (growthRate > 5) {
      return 'High memory growth detected - consider reducing data batch sizes';
    }

    if (volatility > 50) {
      return 'Memory usage is volatile - enable aggressive cleanup';
    }

    if (trend === 'increasing' && growthRate > 2) {
      return 'Steady memory growth - monitor for potential leaks';
    }

    return 'Memory usage is stable';
  }



  /**
   * Reset monitoring state (for testing)
   */
  reset() {
    this.stopMonitoring();
    this.memoryHistory = [];
    this.metrics = {
      heapUsed: 0,
      heapTotal: 0,
      domNodes: 0,
      eventListeners: 0,
      cacheSize: 0,
      indexedDBSize: 0
    };
  }


}

// Global instance
window.MemoryMonitor = new MemoryMonitor();

// Auto-start monitoring
window.MemoryMonitor.startMonitoring();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.MemoryMonitor.stopMonitoring();
});

// Export for global use
window.getMemoryReport = () => window.MemoryMonitor.getMemoryReport();
window.forceMemoryCleanup = () => window.MemoryMonitor.triggerCriticalCleanup();
