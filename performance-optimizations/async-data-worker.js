/**
 * Web Worker for Heavy Data Generation
 * Moves expensive data generation off the main thread
 */

// Data generation functions moved to worker
function generateTodayVsPreviousYearsDataAsync(config) {
  const data = [];
  const { startYear, endYear, marketplaceCodes, pacificMonthDay } = config;
  
  // Use requestIdleCallback pattern for chunked processing
  return new Promise((resolve) => {
    let currentYear = startYear;
    
    function processChunk() {
      const chunkSize = 5; // Process 5 years at a time
      const endChunk = Math.min(currentYear + chunkSize, endYear + 1);
      
      for (let year = currentYear; year < endChunk; year++) {
        // Generate data for this year (same logic as original)
        const yearData = generateYearData(year, marketplaceCodes, pacificMonthDay);
        data.push(yearData);
      }
      
      currentYear = endChunk;
      
      if (currentYear <= endYear) {
        // Schedule next chunk
        setTimeout(processChunk, 0);
      } else {
        resolve(data);
      }
    }
    
    processChunk();
  });
}

function generateMonthlySalesDataAsync(year, monthsToShow) {
  return new Promise((resolve) => {
    const monthlyData = [];
    let currentMonth = 0;
    
    function processMonthChunk() {
      const chunkSize = 3; // Process 3 months at a time
      const endChunk = Math.min(currentMonth + chunkSize, monthsToShow);
      
      for (let monthIndex = currentMonth; monthIndex < endChunk; monthIndex++) {
        // Generate month data (same logic as original)
        const monthData = generateMonthData(monthIndex);
        monthlyData.push(monthData);
      }
      
      currentMonth = endChunk;
      
      if (currentMonth < monthsToShow) {
        setTimeout(processMonthChunk, 0);
      } else {
        resolve(monthlyData);
      }
    }
    
    processMonthChunk();
  });
}

// Helper functions
function generateYearData(year, marketplaceCodes, pacificMonthDay) {
  const marketplaces = [];
  let totalSales = 0;
  let totalRoyalties = 0;
  let totalReturns = 0;

  // Create the date for this year using today's month/day
  const yearDate = new Date(year, pacificMonthDay.month, pacificMonthDay.day);

  // Create two-line date format for Today vs Previous Years chart
  const monthAbbreviation = yearDate.toLocaleDateString('en-US', { month: 'short' });
  const day = yearDate.getDate();
  const yearAbbreviation = year.toString().slice(-2); // Get last 2 digits of year

  // First line: Month abbreviation + day (e.g., "Jul 26")
  const monthDay = `${monthAbbreviation} ${day}`;

  // Second line: Year in abbreviated format (e.g., "'25")
  const yearLabel = `'${yearAbbreviation}`;

  // Generate marketplace data for this year with realistic growth trends
  marketplaceCodes.forEach(code => {
    // Implement realistic business growth over 26 years
    let baseSalesMin, baseSalesMax;

    if (year <= 2005) {
      // Early years: 2000-2005
      baseSalesMin = 15;
      baseSalesMax = 50;
    } else if (year <= 2010) {
      // Growth period: 2006-2010
      baseSalesMin = 25;
      baseSalesMax = 70;
    } else if (year <= 2015) {
      // Established business: 2011-2015
      baseSalesMin = 35;
      baseSalesMax = 80;
    } else if (year <= 2020) {
      // Mature business: 2016-2020
      baseSalesMin = 40;
      baseSalesMax = 90;
    } else {
      // Recent years: 2021-2025
      baseSalesMin = 45;
      baseSalesMax = 100;
    }

    // Marketplace-specific multipliers (US is strongest, others vary)
    const marketplaceMultipliers = {
      'US': 1.0,
      'UK': 0.7,
      'DE': 0.8,
      'FR': 0.6,
      'IT': 0.5,
      'ES': 0.4,
      'JP': 0.3
    };

    const multiplier = marketplaceMultipliers[code] || 0.5;
    const adjustedMin = Math.floor(baseSalesMin * multiplier);
    const adjustedMax = Math.floor(baseSalesMax * multiplier);

    // Generate sales with some randomness
    const sales = Math.floor(Math.random() * (adjustedMax - adjustedMin + 1)) + adjustedMin;
    const royalties = Math.floor(sales * 0.15); // 15% royalty rate
    const returns = Math.floor(Math.random() * Math.max(1, sales * 0.05)); // Up to 5% returns

    marketplaces.push({
      code: code,
      sales: sales,
      royalties: royalties,
      returns: returns
    });

    totalSales += sales;
    totalRoyalties += royalties;
    totalReturns += returns;
  });

  // Create backward compatibility arrays
  const values = marketplaces.map(m => m.sales);
  const labels = marketplaces.map(m => m.code);

  return {
    monthDay: monthDay,
    year: yearLabel,
    marketplaces: marketplaces,
    sales: totalSales,
    royalties: totalRoyalties,
    returns: totalReturns,
    values: values,
    labels: labels,
    fullDate: yearDate.toISOString().split('T')[0],
    dateObj: yearDate.toISOString()
  };
}

function generateMonthData(monthIndex) {
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  const allMarketplaces = ["US", "UK", "DE", "FR", "IT", "ES", "JP"];

  // Seasonal patterns: some months are naturally higher/lower
  const seasonalMultipliers = [
    0.8,  // Jan - post-holiday dip
    0.9,  // Feb - slow month
    1.1,  // Mar - spring pickup
    1.2,  // Apr - strong month
    1.0,  // May - average
    0.9,  // Jun - summer slowdown
    0.8,  // Jul - vacation month
    0.9,  // Aug - still slow
    1.1,  // Sep - back to school
    1.3,  // Oct - strong fall
    1.4,  // Nov - pre-holiday surge
    1.2   // Dec - holiday month
  ];

  const seasonalMultiplier = seasonalMultipliers[monthIndex] || 1.0;

  // Randomly decide how many marketplaces have sales (4-7 for more realistic data)
  const numActiveMarketplaces = Math.floor(Math.random() * 4) + 4;

  // Randomly select which marketplaces are active
  const shuffled = [...allMarketplaces].sort(() => 0.5 - Math.random());
  const activeMarketplaces = shuffled.slice(0, numActiveMarketplaces);

  // Generate sales data for each marketplace with realistic distribution and seasonal effects
  const salesData = {};
  let totalSales = 0;
  let totalRoyalties = 0;
  let totalReturns = 0;

  allMarketplaces.forEach(marketplace => {
    if (activeMarketplaces.includes(marketplace)) {
      // Base sales ranges with seasonal adjustment
      let baseSales;
      if (marketplace === 'US') {
        baseSales = Math.floor((Math.random() * 800 + 200) * seasonalMultiplier);
      } else if (['UK', 'DE'].includes(marketplace)) {
        baseSales = Math.floor((Math.random() * 400 + 100) * seasonalMultiplier);
      } else if (['FR', 'IT', 'ES'].includes(marketplace)) {
        baseSales = Math.floor((Math.random() * 200 + 50) * seasonalMultiplier);
      } else {
        baseSales = Math.floor((Math.random() * 100 + 25) * seasonalMultiplier);
      }

      const royalties = Math.floor(baseSales * 0.15);
      const returns = Math.floor(Math.random() * Math.max(1, baseSales * 0.05));

      salesData[marketplace] = {
        sales: baseSales,
        royalties: royalties,
        returns: returns
      };

      totalSales += baseSales;
      totalRoyalties += royalties;
      totalReturns += returns;
    } else {
      salesData[marketplace] = {
        sales: 0,
        royalties: 0,
        returns: 0
      };
    }
  });

  // Create marketplace array for compatibility
  const marketplaces = allMarketplaces.map(code => ({
    code: code,
    sales: salesData[code].sales,
    royalties: salesData[code].royalties,
    returns: salesData[code].returns
  }));

  // Create backward compatibility arrays
  const values = marketplaces.map(m => m.sales);
  const labels = marketplaces.map(m => m.code);

  return {
    monthIndex: monthIndex,
    month: monthNames[monthIndex],
    marketplaces: marketplaces,
    sales: totalSales,
    royalties: totalRoyalties,
    returns: totalReturns,
    values: values,
    labels: labels
  };
}

// Worker message handler
self.onmessage = async function(e) {
  const { requestId, type, config, salesData } = e.data;

  try {
    let result;

    switch (type) {
      case 'generateTodayVsPreviousYears':
        result = await generateTodayVsPreviousYearsDataAsync(config);
        break;
      case 'generateMonthlySales':
        result = await generateMonthlySalesDataAsync(config.year, config.monthsToShow);
        break;
      case 'processSalesDataForChart':
        // Process real sales data for chart generation
        result = await processSalesDataForChart(salesData, config);
        break;
      case 'processMonthlySalesData':
        // Handle monthly sales data processing (delegate to existing function)
        result = await processSalesDataForChart(salesData, { chartType: 'monthlySales', ...config });
        break;
      case 'processMarketplaceComparisonData':
        // Handle marketplace comparison data processing (delegate to existing function)
        result = await processSalesDataForChart(salesData, { chartType: 'marketplaceComparison', ...config });
        break;
      case 'aggregateSalesData':
        // Aggregate sales data by various dimensions
        result = await aggregateSalesData(salesData, config);
        break;
      case 'calculateTrends':
        // Calculate trends and growth rates
        result = await calculateTrends(salesData, config);
        break;
      default:
        throw new Error(`Unknown task type: ${type}`);
    }

    self.postMessage({ requestId, success: true, data: result });
  } catch (error) {
    self.postMessage({ requestId, success: false, error: error.message });
  }
};

/**
 * Process real sales data for chart generation
 */
async function processSalesDataForChart(salesData, config) {
  const { chartType, dateRange, groupBy } = config;

  switch (chartType) {
    case 'todayVsPreviousYears':
      return processTodayVsPreviousYearsData(salesData, config);
    case 'monthlySales':
      return processMonthlySalesData(salesData, config);
    case 'marketplaceComparison':
      return processMarketplaceComparisonData(salesData, config);
    default:
      throw new Error(`Unknown chart type: ${chartType}`);
  }
}

/**
 * Process today vs previous years data from real sales
 */
function processTodayVsPreviousYearsData(salesData, config) {
  const { targetDate } = config;
  const targetMonth = new Date(targetDate).getMonth();
  const targetDay = new Date(targetDate).getDate();

  // Group sales by year for the same month/day
  const yearlyData = {};

  salesData.forEach(sale => {
    const saleDate = new Date(sale.date);
    if (saleDate.getMonth() === targetMonth && saleDate.getDate() === targetDay) {
      const year = saleDate.getFullYear();
      if (!yearlyData[year]) {
        yearlyData[year] = { revenue: 0, quantity: 0 };
      }
      yearlyData[year].revenue += sale.revenue || 0;
      yearlyData[year].quantity += sale.quantity || 0;
    }
  });

  // Convert to chart format
  return Object.entries(yearlyData)
    .map(([year, data]) => ({
      year: parseInt(year),
      revenue: data.revenue,
      quantity: data.quantity
    }))
    .sort((a, b) => a.year - b.year);
}

/**
 * Process monthly sales data from real sales
 */
function processMonthlySalesData(salesData, config) {
  const { year } = config;
  const monthlyData = Array(12).fill(null).map((_, index) => ({
    month: index + 1,
    revenue: 0,
    units: 0,
    orders: 0
  }));

  salesData.forEach(sale => {
    const saleDate = new Date(sale.date);
    if (saleDate.getFullYear() === year) {
      const monthIndex = saleDate.getMonth();
      monthlyData[monthIndex].revenue += sale.revenue || 0;
      monthlyData[monthIndex].units += sale.quantity || 0;
      monthlyData[monthIndex].orders += 1;
    }
  });

  // Return object structure consistent with main thread processing
  return {
    year,
    months: monthlyData,
    totals: {
      revenue: monthlyData.reduce((sum, month) => sum + month.revenue, 0),
      orders: monthlyData.reduce((sum, month) => sum + month.orders, 0),
      units: monthlyData.reduce((sum, month) => sum + month.units, 0)
    },
    trends: calculateMonthlyTrends(monthlyData)
  };
}

/**
 * Calculate monthly trends (simplified version for worker)
 */
function calculateMonthlyTrends(monthlyData) {
  const revenues = monthlyData.map(m => m.revenue);
  const orders = monthlyData.map(m => m.orders);

  return {
    revenue: {
      direction: revenues[revenues.length - 1] > revenues[0] ? 'up' : 'down',
      percentage: revenues[0] > 0 ? ((revenues[revenues.length - 1] - revenues[0]) / revenues[0] * 100) : 0
    },
    orders: {
      direction: orders[orders.length - 1] > orders[0] ? 'up' : 'down',
      percentage: orders[0] > 0 ? ((orders[orders.length - 1] - orders[0]) / orders[0] * 100) : 0
    }
  };
}

/**
 * Process marketplace comparison data
 */
function processMarketplaceComparisonData(salesData, config) {
  const { timeframe } = config;
  const marketplaceData = {};

  salesData.forEach(sale => {
    const marketplace = sale.marketplace || 'Unknown';
    if (!marketplaceData[marketplace]) {
      marketplaceData[marketplace] = { revenue: 0, quantity: 0, orders: 0 };
    }
    marketplaceData[marketplace].revenue += sale.revenue || 0;
    marketplaceData[marketplace].quantity += sale.quantity || 0;
    marketplaceData[marketplace].orders += 1;
  });

  // Return object format matching main-thread format
  return {
    timeframe,
    marketplaces: Object.entries(marketplaceData).map(([marketplace, data]) => ({
      marketplace,
      ...data,
      averageOrderValue: data.orders > 0 ? data.revenue / data.orders : 0
    }))
  };
}

/**
 * Aggregate sales data by various dimensions
 */
async function aggregateSalesData(salesData, config) {
  const { groupBy, dateRange } = config;

  // Filter by date range if provided
  let filteredData = salesData;
  if (dateRange) {
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    filteredData = salesData.filter(sale => {
      const saleDate = new Date(sale.date);
      return saleDate >= startDate && saleDate <= endDate;
    });
  }

  // Group by specified dimension
  const grouped = {};
  filteredData.forEach(sale => {
    let key;
    switch (groupBy) {
      case 'date':
        key = sale.date;
        break;
      case 'marketplace':
        key = sale.marketplace || 'Unknown';
        break;
      case 'asin':
        key = sale.asin;
        break;
      default:
        key = 'all';
    }

    if (!grouped[key]) {
      grouped[key] = { revenue: 0, quantity: 0, orders: 0 };
    }
    grouped[key].revenue += sale.revenue || 0;
    grouped[key].quantity += sale.quantity || 0;
    grouped[key].orders += 1;
  });

  return grouped;
}

/**
 * Calculate trends and growth rates
 */
async function calculateTrends(salesData, config) {
  const { period, metric } = config;

  // Group data by time period
  const periodData = {};
  salesData.forEach(sale => {
    const saleDate = new Date(sale.date);
    let periodKey;

    switch (period) {
      case 'daily':
        periodKey = sale.date;
        break;
      case 'weekly':
        const weekStart = new Date(saleDate);
        weekStart.setDate(saleDate.getDate() - saleDate.getDay());
        periodKey = weekStart.toISOString().split('T')[0];
        break;
      case 'monthly':
        periodKey = `${saleDate.getFullYear()}-${String(saleDate.getMonth() + 1).padStart(2, '0')}`;
        break;
      default:
        periodKey = sale.date;
    }

    if (!periodData[periodKey]) {
      periodData[periodKey] = { revenue: 0, quantity: 0, orders: 0 };
    }
    periodData[periodKey].revenue += sale.revenue || 0;
    periodData[periodKey].quantity += sale.quantity || 0;
    periodData[periodKey].orders += 1;
  });

  // Calculate trends
  const sortedPeriods = Object.keys(periodData).sort();
  const trends = [];

  for (let i = 1; i < sortedPeriods.length; i++) {
    const currentPeriod = sortedPeriods[i];
    const previousPeriod = sortedPeriods[i - 1];

    const currentValue = periodData[currentPeriod][metric] || 0;
    const previousValue = periodData[previousPeriod][metric] || 0;

    const growthRate = previousValue > 0 ?
      ((currentValue - previousValue) / previousValue) * 100 : 0;

    trends.push({
      period: currentPeriod,
      value: currentValue,
      growthRate: growthRate
    });
  }

  return trends;
}
