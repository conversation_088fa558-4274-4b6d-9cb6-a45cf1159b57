---
type: "agent_requested"
description: "Example description"
---
Augment Rules for JS-Centric Chrome Extension Development
⚠️ MANDATORY PRE-IMPLEMENTATION CHECKLIST ⚠️
STOP! Before writing ANY code or suggesting ANY changes:
You are a debugging monster. Before fixing or changing anything, you want to make sure you understand VERY WELL what's happening.
1. 🔍 SEARCH PHASE
REQUIRED: Run these searches and document results

grep_search for related functionality (e.g., "chrome.storage", "createElement", "innerHTML")
file_search for JS modules and components
list_dir in project script directories
open_file on manifest.json to understand extension configuration
search_codebase for DOM creation and CSS injection patterns

2. 📝 DISCOVERY DOCUMENTATION
REQUIRED: Fill this out completely
## Existing Files Found:

[List all relevant JS files]
[Note manifest.json settings that apply]
[Document JS module structure and dependencies]

## Existing Functionality:

[Document DOM generation methods used (template strings, createElement, etc.)]
[Note CSS injection techniques (style tags, classList, template literals)]
[Identify Chrome API usage patterns]
[Document event binding approaches]

## Project Patterns:

[Note JS component organization]
[Document HTML generation patterns (string templates, DOM API, etc.)]
[Identify CSS-in-JS patterns (inline styles, classNames, template literals)]
[Note state management approach]

## Gaps Identified:

[List missing functionality or UI components]
[Identify potential browser compatibility issues]
[Note missing error handling]
[Document performance concerns for dynamic content]

## Proposed Approach:
 Enhance existing JS modules (preferred)
 Create new JS files (requires justification)
 Modify manifest.json (explain permission needs)
 Integration plan with existing extension contexts

3. 🗂️ EXTENSION CONTEXT GATHERING

# Review manifest.json for permissions and extension structure
# Identify which context the code will run in:
 Background script
 Content script
 Popup script
 Options page script
 Service worker


Check for message passing needs between contexts
Understand storage requirements (local vs sync)

4. ✅ TASKS
# Before you start implementing, add task to tasks.md and keep updating it as you go and mark as completed to each completed task.
When you get a new task, clear the current task data in tasks.md and add the new new one as per the task, DO NOT DELETE the tasks.md file, just clear its data.
# Break down requirements into specific implementation tasks:
- [ ] Task 1: [Description]
- [ ] Task 2: [Description]



5. 📊 IMPLEMENTATION TRACKING
# Document your approach to HTML generation in JS
# Note CSS injection methods
# Track Chrome API usage and permission requirements
# Document dynamic content generation strategies

6. 🧪 TESTING STRATEGY
# Test in Chrome's extension developer mode
 # Check functionality in different extension contexts
 # Verify dynamically generated UI in different screen sizes
 # Test performance of DOM manipulation with DevTools
 # Validate event handling and bubbling

7. ❌ LINTER & ERROR VERIFICATION
Run JavaScript linter (ESLint) checks
Validate generated HTML structure
Check dynamically applied CSS for issues

## Document all errors found:
Error description
File location
Resolution approach

Verify console is free of errors during runtime

8. 📚 CODE DOCUMENTATION
 Update comments for HTML generation functions
 Document CSS application methods
 Add JSDoc comments for component functions
 Create notes about dynamic content generation patterns
 Document any known limitations

9. 🔄 Context7 and Sequential Thinking MCP
Each time you start a task, you must apply both Context7 and Sequential Thinking MCP methodologies:
	•	Context7: Understand the full surrounding environment — codebase structure, intent, past modifications, current dependencies, and usage scenarios.
	•	Sequential Thinking MCP: Break the task into a clear, logical sequence of steps. Each action must follow naturally from the one before it, with no skipped reasoning or undefined jumps.

These two mental control processes are mandatory for every task initiation and must be documented in your task breakdown.

10. 🧪 Code Quality Assurance:
Every time the agent performs or suggests a new implementation, a spaghetti code review is mandatory.
Check for:

Entangled logic without modular separation

Functions doing too many unrelated tasks

Unstructured nested callbacks or chained .then() without async clarity

UI logic mixed directly with business logic
If any signs of spaghetti code are found, refactor into isolated, reusable components with clear responsibility boundaries before continuing.

11. 🔄 FINAL REVIEW
 Code matches requirements
 Extension works in Chrome developer mode
 All dynamically generated UI is properly styled
 JavaScript is optimized for extension context
 Event listeners are properly managed (added/removed)
 manifest.json is correctly configured

❌ IF YOU SKIP ANY PART OF THIS CHECKLIST, YOU ARE VIOLATING THE MOST IMPORTANT RULE ❌

## JS-Centric Extension Best Practices:
Element Creation Efficiency: Batch DOM operations and minimize reflows
CSS Injection Strategy: Use consistent method for styling (classList vs inline)
Template Organization: Keep HTML template strings readable and maintainable
Event Delegation: Use event delegation for dynamically created elements
Memory Management: Clean up event listeners for removed elements
Component Isolation: Create reusable JS functions for UI components
Content Security: Be cautious with innerHTML vs createElement approaches
Error Boundaries: Implement error handling for dynamic content generation