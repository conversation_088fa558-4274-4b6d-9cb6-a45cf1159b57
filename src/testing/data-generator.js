/**
 * Realistic Test Data Generator for Massive Scale Testing
 * Generates 5M product records and 20M sales records with realistic patterns
 * Includes various product categories, marketplace distributions, and seasonal trends
 */

class DataGenerator {
    constructor() {
        // Product categories and their characteristics
        this.categories = {
            'Electronics': {
                priceRange: [10, 2000],
                seasonality: 'holiday',
                avgRating: 4.2,
                brands: ['Apple', 'Samsung', 'Sony', 'LG', 'Panasonic', 'Dell', 'HP', 'Lenovo']
            },
            'Books': {
                priceRange: [5, 50],
                seasonality: 'steady',
                avgRating: 4.1,
                brands: ['Penguin', 'HarperCollins', 'Random House', 'Scholastic', 'Wiley']
            },
            'Clothing': {
                priceRange: [15, 200],
                seasonality: 'seasonal',
                avgRating: 3.9,
                brands: ['Nike', 'Adidas', 'Levi\'s', 'Gap', 'H&M', 'Zara', 'Uniqlo']
            },
            'Home & Garden': {
                priceRange: [20, 500],
                seasonality: 'spring',
                avgRating: 4.0,
                brands: ['IKEA', 'Home Depot', 'Wayfair', 'Target', 'Walmart']
            },
            'Sports & Outdoors': {
                priceRange: [25, 800],
                seasonality: 'summer',
                avgRating: 4.3,
                brands: ['Nike', 'Adidas', 'Under Armour', 'Patagonia', 'REI', 'Coleman']
            },
            'Health & Beauty': {
                priceRange: [8, 150],
                seasonality: 'steady',
                avgRating: 4.0,
                brands: ['L\'Oreal', 'Neutrogena', 'Olay', 'Maybelline', 'CeraVe']
            }
        };

        // Marketplace characteristics
        this.marketplaces = {
            'amazon.com': { weight: 0.4, currency: 'USD', timezone: 'America/New_York' },
            'amazon.co.uk': { weight: 0.25, currency: 'GBP', timezone: 'Europe/London' },
            'amazon.de': { weight: 0.2, currency: 'EUR', timezone: 'Europe/Berlin' },
            'amazon.fr': { weight: 0.1, currency: 'EUR', timezone: 'Europe/Paris' },
            'amazon.ca': { weight: 0.05, currency: 'CAD', timezone: 'America/Toronto' }
        };

        // Sales patterns
        this.salesPatterns = {
            hourly: [0.3, 0.2, 0.1, 0.1, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.5, 1.6, 1.5, 1.4, 1.3, 1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.5, 0.4],
            weekly: [0.8, 1.0, 1.1, 1.2, 1.3, 1.5, 1.4], // Mon-Sun
            monthly: [1.0, 0.9, 1.1, 1.0, 1.2, 1.1, 1.0, 0.9, 1.1, 1.2, 1.4, 1.8], // Jan-Dec
            seasonal: {
                'holiday': [0.8, 0.7, 0.9, 1.0, 1.1, 1.0, 0.9, 0.8, 1.0, 1.2, 1.5, 2.0],
                'seasonal': [0.7, 0.8, 1.2, 1.4, 1.3, 1.5, 1.6, 1.4, 1.2, 1.0, 0.8, 0.9],
                'spring': [0.8, 0.9, 1.3, 1.5, 1.4, 1.2, 1.0, 0.9, 1.0, 1.1, 0.8, 0.7],
                'summer': [0.9, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 1.5, 1.2, 1.0, 0.8, 0.7],
                'steady': [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            }
        };

        // Common product titles and descriptions
        this.productTitleTemplates = [
            '{brand} {adjective} {product} - {feature}',
            '{adjective} {product} by {brand} - {feature}',
            '{brand} {product} with {feature}',
            '{feature} {product} - {brand} {adjective}'
        ];

        this.adjectives = ['Premium', 'Professional', 'Deluxe', 'Classic', 'Modern', 'Vintage', 'Eco-Friendly', 'Wireless', 'Smart', 'Portable'];
        this.features = ['High Quality', 'Fast Shipping', 'Best Seller', 'New Release', 'Limited Edition', 'Bestselling', 'Award Winning'];
    }

    /**
     * Generate realistic product data
     * @param {number} count - Number of products to generate
     * @param {Object} options - Generation options
     * @returns {Array} Array of product objects
     */
    generateProducts(count, options = {}) {
        const products = [];
        const startTime = Date.now();
        
        console.log(`Generating ${count.toLocaleString()} products...`);
        
        for (let i = 0; i < count; i++) {
            const product = this.generateSingleProduct(i, options);
            products.push(product);
            
            // Progress logging for large datasets
            if (i > 0 && i % 100000 === 0) {
                const progress = ((i / count) * 100).toFixed(1);
                const elapsed = Date.now() - startTime;
                const estimated = (elapsed / i) * count;
                const remaining = estimated - elapsed;
                
                console.log(`  Progress: ${progress}% (${i.toLocaleString()}/${count.toLocaleString()}) - ETA: ${Math.round(remaining / 1000)}s`);
            }
        }
        
        console.log(`Generated ${count.toLocaleString()} products in ${Math.round((Date.now() - startTime) / 1000)}s`);
        return products;
    }

    /**
     * Generate realistic sales data
     * @param {Array} products - Product array to generate sales for
     * @param {number} salesCount - Number of sales records to generate
     * @param {Object} options - Generation options
     * @returns {Array} Array of sales objects
     */
    generateSales(products, salesCount, options = {}) {
        const sales = [];
        const startTime = Date.now();
        const endDate = options.endDate || new Date();
        const startDate = options.startDate || new Date(endDate.getTime() - (365 * 24 * 60 * 60 * 1000)); // 1 year back
        
        console.log(`Generating ${salesCount.toLocaleString()} sales records...`);
        
        // Create product lookup for faster access
        const productLookup = new Map();
        products.forEach(product => {
            productLookup.set(product.asin, product);
        });
        
        // Generate sales with realistic patterns
        for (let i = 0; i < salesCount; i++) {
            const sale = this.generateSingleSale(products, productLookup, startDate, endDate, options);
            sales.push(sale);
            
            // Progress logging
            if (i > 0 && i % 500000 === 0) {
                const progress = ((i / salesCount) * 100).toFixed(1);
                const elapsed = Date.now() - startTime;
                const estimated = (elapsed / i) * salesCount;
                const remaining = estimated - elapsed;
                
                console.log(`  Progress: ${progress}% (${i.toLocaleString()}/${salesCount.toLocaleString()}) - ETA: ${Math.round(remaining / 1000)}s`);
            }
        }
        
        console.log(`Generated ${salesCount.toLocaleString()} sales in ${Math.round((Date.now() - startTime) / 1000)}s`);
        return sales;
    }

    /**
     * Generate products in batches to avoid memory overflow
     * @param {number} count - Total number of products to generate
     * @param {number} batchSize - Number of products per batch
     * @param {Function} onBatch - Callback function for each batch
     * @param {Object} options - Generation options
     * @returns {Promise<Object>} Generation statistics
     */
    async generateProductsInBatches(count, batchSize, onBatch, options = {}) {
        if (!onBatch || typeof onBatch !== 'function') {
            throw new Error('onBatch callback function is required');
        }

        const startTime = Date.now();
        let processedCount = 0;
        let batchCount = 0;

        console.log(`Generating ${count.toLocaleString()} products in batches of ${batchSize.toLocaleString()}...`);

        for (let i = 0; i < count; i += batchSize) {
            const currentBatchSize = Math.min(batchSize, count - i);
            const batch = [];

            // Generate batch
            for (let j = 0; j < currentBatchSize; j++) {
                const product = this.generateSingleProduct(i + j, options);
                batch.push(product);
            }

            // Process batch
            try {
                await onBatch(batch, batchCount);
                processedCount += batch.length;
                batchCount++;

                // Progress logging
                if (batchCount % 10 === 0) {
                    const progress = ((processedCount / count) * 100).toFixed(1);
                    const elapsed = Date.now() - startTime;
                    const estimated = (elapsed / processedCount) * count;
                    const remaining = estimated - elapsed;

                    console.log(`  Progress: ${progress}% (${processedCount.toLocaleString()}/${count.toLocaleString()}) - ETA: ${Math.round(remaining / 1000)}s`);
                }

                // Allow other operations to proceed
                if (batchCount % 5 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            } catch (error) {
                console.error(`Error processing product batch ${batchCount}:`, error);
                throw error;
            }
        }

        const duration = Date.now() - startTime;
        const stats = {
            totalCount: processedCount,
            batchCount,
            duration,
            productsPerSecond: Math.round(processedCount / (duration / 1000))
        };

        console.log(`Generated ${processedCount.toLocaleString()} products in ${batchCount} batches (${Math.round(duration / 1000)}s)`);
        return stats;
    }

    /**
     * Generate sales in batches to avoid memory overflow
     * @param {Array} products - Product array to generate sales for
     * @param {number} salesCount - Total number of sales to generate
     * @param {number} batchSize - Number of sales per batch
     * @param {Function} onBatch - Callback function for each batch
     * @param {Object} options - Generation options
     * @returns {Promise<Object>} Generation statistics
     */
    async generateSalesInBatches(products, salesCount, batchSize, onBatch, options = {}) {
        if (!onBatch || typeof onBatch !== 'function') {
            throw new Error('onBatch callback function is required');
        }

        const startTime = Date.now();
        const endDate = options.endDate || new Date();
        const startDate = options.startDate || new Date(endDate.getTime() - (365 * 24 * 60 * 60 * 1000));
        let processedCount = 0;
        let batchCount = 0;

        console.log(`Generating ${salesCount.toLocaleString()} sales in batches of ${batchSize.toLocaleString()}...`);

        // Create product lookup for faster access
        const productLookup = new Map();
        products.forEach(product => {
            productLookup.set(product.asin, product);
        });

        for (let i = 0; i < salesCount; i += batchSize) {
            const currentBatchSize = Math.min(batchSize, salesCount - i);
            const batch = [];

            // Generate batch
            for (let j = 0; j < currentBatchSize; j++) {
                const sale = this.generateSingleSale(products, productLookup, startDate, endDate, options);
                batch.push(sale);
            }

            // Process batch
            try {
                await onBatch(batch, batchCount);
                processedCount += batch.length;
                batchCount++;

                // Progress logging
                if (batchCount % 10 === 0) {
                    const progress = ((processedCount / salesCount) * 100).toFixed(1);
                    const elapsed = Date.now() - startTime;
                    const estimated = (elapsed / processedCount) * salesCount;
                    const remaining = estimated - elapsed;

                    console.log(`  Progress: ${progress}% (${processedCount.toLocaleString()}/${salesCount.toLocaleString()}) - ETA: ${Math.round(remaining / 1000)}s`);
                }

                // Allow other operations to proceed
                if (batchCount % 5 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            } catch (error) {
                console.error(`Error processing sales batch ${batchCount}:`, error);
                throw error;
            }
        }

        const duration = Date.now() - startTime;
        const stats = {
            totalCount: processedCount,
            batchCount,
            duration,
            salesPerSecond: Math.round(processedCount / (duration / 1000))
        };

        console.log(`Generated ${processedCount.toLocaleString()} sales in ${batchCount} batches (${Math.round(duration / 1000)}s)`);
        return stats;
    }

    /**
     * Generate incremental data for testing updates
     * @param {number} productCount - Number of new products
     * @param {number} salesCount - Number of new sales
     * @param {number} startId - Starting ID for products
     * @returns {Object} Incremental data
     */
    generateIncrementalData(productCount, salesCount, startId = 0) {
        const products = this.generateProducts(productCount, { startId });
        const sales = this.generateSales(products, salesCount);

        return {
            products,
            sales,
            metadata: {
                generated: new Date().toISOString(),
                productCount,
                salesCount,
                startId
            }
        };
    }

    /**
     * Generate corrupted data for testing error handling
     * @param {number} count - Number of corrupted records
     * @returns {Array} Array of corrupted records
     */
    generateCorruptedData(count) {
        const corruptedData = [];
        
        for (let i = 0; i < count; i++) {
            const corruptionType = Math.floor(Math.random() * 5);
            let record;
            
            switch (corruptionType) {
                case 0: // Missing required fields
                    record = { id: i };
                    break;
                case 1: // Invalid data types
                    record = {
                        id: i,
                        asin: 123, // Should be string
                        price: 'invalid', // Should be number
                        lastUpdated: 'not-a-date'
                    };
                    break;
                case 2: // Null values
                    record = {
                        id: i,
                        asin: null,
                        title: null,
                        price: null
                    };
                    break;
                case 3: // Extremely long strings
                    record = {
                        id: i,
                        asin: `ASIN${i}`,
                        title: 'A'.repeat(10000),
                        description: 'B'.repeat(50000)
                    };
                    break;
                case 4: // Invalid references
                    record = {
                        id: i,
                        asin: `INVALID${i}`,
                        marketplace: 'invalid-marketplace',
                        category: 'non-existent-category'
                    };
                    break;
            }
            
            corruptedData.push(record);
        }
        
        return corruptedData;
    }

    /**
     * Generate test data with specific patterns
     * @param {Object} pattern - Pattern configuration
     * @returns {Object} Patterned test data
     */
    generatePatternedData(pattern) {
        const data = {
            products: [],
            sales: []
        };
        
        switch (pattern.type) {
            case 'bestsellers':
                data.products = this.generateBestsellers(pattern.count);
                break;
            case 'seasonal':
                data.products = this.generateSeasonalProducts(pattern.count, pattern.season);
                break;
            case 'marketplace-specific':
                data.products = this.generateMarketplaceSpecificProducts(pattern.count, pattern.marketplace);
                break;
            case 'price-range':
                data.products = this.generatePriceRangeProducts(pattern.count, pattern.minPrice, pattern.maxPrice);
                break;
        }
        
        if (data.products.length > 0) {
            data.sales = this.generateSales(data.products, pattern.salesMultiplier * data.products.length);
        }
        
        return data;
    }

    // Private helper methods
    generateSingleProduct(index, options = {}) {
        const categoryNames = Object.keys(this.categories);
        const categoryName = categoryNames[index % categoryNames.length];
        const category = this.categories[categoryName];
        
        const marketplace = this.selectRandomMarketplace();
        const brand = category.brands[Math.floor(Math.random() * category.brands.length)];
        const asin = options.startId ? 
            `${marketplace.replace(/[^A-Z]/g, '')}${(options.startId + index).toString().padStart(10, '0')}` :
            `${marketplace.replace(/[^A-Z]/g, '')}${index.toString().padStart(10, '0')}`;
        
        const price = this.generateRealisticPrice(category.priceRange);
        const title = this.generateProductTitle(brand, categoryName);
        const description = this.generateProductDescription(title, brand, categoryName);
        
        return {
            id: options.startId ? options.startId + index : index,
            asin,
            title,
            description,
            price,
            marketplace,
            category: categoryName,
            brand,
            rating: this.generateRating(category.avgRating),
            reviewCount: Math.floor(Math.random() * 10000),
            imageUrl: `https://example.com/images/${asin}.jpg`,
            availability: Math.random() > 0.1 ? 'in-stock' : 'out-of-stock',
            lastUpdated: this.generateRecentDate().toISOString(),
            createdAt: this.generatePastDate().toISOString()
        };
    }

    generateSingleSale(products, productLookup, startDate, endDate, options = {}) {
        // Select random product with weighted probability (popular products sell more)
        const product = this.selectProductForSale(products);
        const saleDate = this.generateSaleDate(startDate, endDate, product.category);
        const quantity = this.generateSaleQuantity();
        const unitPrice = this.generateSalePrice(product.price);
        
        return {
            id: Math.random().toString(36).substr(2, 9),
            asin: product.asin,
            marketplace: product.marketplace,
            date: saleDate.toISOString(),
            quantity,
            unitPrice,
            revenue: quantity * unitPrice,
            fees: this.calculateFees(quantity * unitPrice, product.marketplace),
            profit: this.calculateProfit(quantity * unitPrice, product.price),
            orderId: `ORDER-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`,
            customerId: `CUSTOMER-${Math.floor(Math.random() * 1000000)}`,
            fulfillmentChannel: Math.random() > 0.3 ? 'FBA' : 'FBM'
        };
    }

    selectRandomMarketplace() {
        const random = Math.random();
        let cumulative = 0;
        
        for (const [marketplace, config] of Object.entries(this.marketplaces)) {
            cumulative += config.weight;
            if (random <= cumulative) {
                return marketplace;
            }
        }
        
        return 'amazon.com'; // fallback
    }

    generateRealisticPrice(priceRange) {
        const [min, max] = priceRange;
        // Use log-normal distribution for more realistic pricing
        const logMin = Math.log(min);
        const logMax = Math.log(max);
        const logPrice = logMin + Math.random() * (logMax - logMin);
        return Math.round(Math.exp(logPrice) * 100) / 100;
    }

    generateProductTitle(brand, category) {
        const template = this.productTitleTemplates[Math.floor(Math.random() * this.productTitleTemplates.length)];
        const adjective = this.adjectives[Math.floor(Math.random() * this.adjectives.length)];
        const feature = this.features[Math.floor(Math.random() * this.features.length)];
        const product = this.getProductTypeForCategory(category);
        
        return template
            .replace('{brand}', brand)
            .replace('{adjective}', adjective)
            .replace('{product}', product)
            .replace('{feature}', feature);
    }

    generateProductDescription(title, brand, category) {
        const descriptions = [
            `Experience the quality of ${brand} with this ${category.toLowerCase()} product. ${title} offers exceptional value and performance.`,
            `${title} by ${brand} - designed for those who demand the best. Perfect for everyday use with premium features.`,
            `Discover ${title} from ${brand}. This ${category.toLowerCase()} item combines style, functionality, and reliability.`
        ];
        
        return descriptions[Math.floor(Math.random() * descriptions.length)];
    }

    generateRating(avgRating) {
        // Generate rating with normal distribution around average
        const variance = 0.3;
        const rating = avgRating + (Math.random() - 0.5) * variance * 2;
        return Math.max(1, Math.min(5, Math.round(rating * 10) / 10));
    }

    generateRecentDate() {
        const now = Date.now();
        const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
        return new Date(thirtyDaysAgo + Math.random() * (now - thirtyDaysAgo));
    }

    generatePastDate() {
        const now = Date.now();
        const twoYearsAgo = now - (2 * 365 * 24 * 60 * 60 * 1000);
        return new Date(twoYearsAgo + Math.random() * (now - twoYearsAgo));
    }

    selectProductForSale(products) {
        // Weighted selection - some products are more popular
        const popularityThreshold = 0.8;
        
        if (Math.random() < popularityThreshold) {
            // Select from top 20% of products (bestsellers)
            const topProducts = products.slice(0, Math.floor(products.length * 0.2));
            return topProducts[Math.floor(Math.random() * topProducts.length)];
        } else {
            // Select any product
            return products[Math.floor(Math.random() * products.length)];
        }
    }

    generateSaleDate(startDate, endDate, category) {
        const timeRange = endDate.getTime() - startDate.getTime();
        const randomTime = Math.random() * timeRange;
        const saleDate = new Date(startDate.getTime() + randomTime);
        
        // Apply seasonal patterns
        const categoryInfo = this.categories[category];
        if (categoryInfo && categoryInfo.seasonality !== 'steady') {
            const month = saleDate.getMonth();
            const seasonalMultiplier = this.salesPatterns.seasonal[categoryInfo.seasonality][month];
            
            // Adjust probability based on seasonal pattern
            if (Math.random() > seasonalMultiplier / 2) {
                // Regenerate date with bias toward high-season months
                return this.generateSaleDate(startDate, endDate, category);
            }
        }
        
        return saleDate;
    }

    generateSaleQuantity() {
        // Most sales are single items, some are bulk
        const random = Math.random();
        if (random < 0.7) return 1;
        if (random < 0.9) return 2;
        if (random < 0.97) return Math.floor(Math.random() * 5) + 3;
        return Math.floor(Math.random() * 20) + 8; // Bulk orders
    }

    generateSalePrice(basePrice) {
        // Sales price can vary from base price due to discounts/promotions
        const variation = 0.2; // ±20%
        const multiplier = 1 + (Math.random() - 0.5) * variation;
        return Math.round(basePrice * multiplier * 100) / 100;
    }

    calculateFees(revenue, marketplace) {
        // Simplified fee calculation
        const feeRates = {
            'amazon.com': 0.15,
            'amazon.co.uk': 0.15,
            'amazon.de': 0.15,
            'amazon.fr': 0.15,
            'amazon.ca': 0.15
        };
        
        return Math.round(revenue * feeRates[marketplace] * 100) / 100;
    }

    calculateProfit(revenue, cost) {
        const fees = revenue * 0.15; // Simplified
        const profit = revenue - cost - fees;
        return Math.round(profit * 100) / 100;
    }

    getProductTypeForCategory(category) {
        const productTypes = {
            'Electronics': ['Smartphone', 'Laptop', 'Tablet', 'Headphones', 'Speaker', 'Camera'],
            'Books': ['Novel', 'Textbook', 'Guide', 'Manual', 'Biography', 'Cookbook'],
            'Clothing': ['T-Shirt', 'Jeans', 'Dress', 'Jacket', 'Shoes', 'Hat'],
            'Home & Garden': ['Chair', 'Table', 'Lamp', 'Vase', 'Tool', 'Plant'],
            'Sports & Outdoors': ['Ball', 'Equipment', 'Gear', 'Apparel', 'Accessory'],
            'Health & Beauty': ['Cream', 'Serum', 'Shampoo', 'Makeup', 'Supplement']
        };
        
        const types = productTypes[category] || ['Product'];
        return types[Math.floor(Math.random() * types.length)];
    }

    generateBestsellers(count) {
        return this.generateProducts(count, { bestseller: true });
    }

    generateSeasonalProducts(count, season) {
        return this.generateProducts(count, { season });
    }

    generateMarketplaceSpecificProducts(count, marketplace) {
        return this.generateProducts(count, { marketplace });
    }

    generatePriceRangeProducts(count, minPrice, maxPrice) {
        return this.generateProducts(count, { priceRange: [minPrice, maxPrice] });
    }
}

// Make DataGenerator available globally
window.DataGenerator = DataGenerator;
