/**
 * Settings Manager for User Preferences and Configuration
 * Manages user preferences for massive scale operations
 * Integrates with existing performance optimization managers
 */

class SettingsManager {
    constructor() {
        this.storageKey = 'snapDashboard_settings';
        this.fallbackStorageKey = 'snapDashboard_settings_session';
        this.eventListeners = new Map();
        this.settings = {};
        this.defaultSettings = this.getDefaultSettings();
        
        // Initialize settings
        this.loadSettings();
        this.validateSettings();
    }

    /**
     * Get default settings for massive scale operations
     * @returns {Object} Default settings configuration
     */
    getDefaultSettings() {
        return {
            // Data Management
            dataSync: {
                fetchFrequency: 300000, // 5 minutes in milliseconds
                batchSize: 1000,
                maxRetries: 3,
                enableRealTime: true,
                autoBackup: true,
                backupFrequency: 86400000 // 24 hours
            },
            
            // Memory Management
            memory: {
                maxMemoryUsage: 512, // MB
                cleanupThreshold: 0.8, // 80% of max
                aggressiveCleanup: false,
                monitoringInterval: 30000, // 30 seconds
                enableMemoryWarnings: true,
                lowMemoryThreshold: 0.9 // 90% of max
            },
            
            // Data Retention
            dataRetention: {
                maxProductAge: 2592000000, // 30 days in milliseconds
                maxSalesAge: 7776000000, // 90 days in milliseconds
                enableAutoCleanup: true,
                cleanupSchedule: 'daily',
                keepCriticalData: true
            },
            
            // UI Preferences
            ui: {
                theme: 'light', // 'light', 'dark', 'auto'
                language: 'en',
                dateFormat: 'MM/DD/YYYY',
                currency: 'USD',
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                compactMode: false
            },
            
            // Virtual List Performance
            virtualList: {
                itemHeight: 80,
                bufferSize: 10,
                pageSize: 100,
                enableVirtualization: true,
                preloadPages: 2,
                searchDebounce: 300
            },
            
            // Marketplace Filters
            marketplace: {
                defaultMarketplace: 'all',
                favoriteMarketplaces: ['amazon.com', 'amazon.co.uk'],
                hideInactiveMarketplaces: false,
                marketplaceOrder: ['amazon.com', 'amazon.co.uk', 'amazon.de', 'amazon.fr']
            },
            
            // Notifications
            notifications: {
                enableDesktop: true,
                enableSound: false,
                enableDataAlerts: true,
                enablePerformanceAlerts: true,
                enableErrorAlerts: true,
                alertThresholds: {
                    memoryUsage: 0.85,
                    queryTime: 5000, // 5 seconds
                    errorRate: 0.05 // 5%
                }
            },
            
            // Performance Monitoring
            performance: {
                enableMonitoring: true,
                enableProfiling: false,
                enableMetrics: true,
                metricsRetention: 604800000, // 7 days
                enableSlowQueryLogging: true,
                slowQueryThreshold: 2000 // 2 seconds
            },
            
            // Export/Import
            export: {
                defaultFormat: 'json',
                includeMetadata: true,
                compressExports: true,
                maxExportSize: 104857600, // 100MB
                enableStreamingExport: true
            },
            
            // Advanced Features
            advanced: {
                enableExperimentalFeatures: false,
                enableDebugMode: false,
                enableVerboseLogging: false,
                enablePerformanceProfiling: false,
                maxConcurrentQueries: 5
            }
        };
    }

    /**
     * Load settings from storage
     */
    loadSettings() {
        try {
            // Try localStorage first
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                this.settings = { ...this.defaultSettings, ...JSON.parse(stored) };
                return;
            }
        } catch (error) {
            console.warn('Failed to load settings from localStorage:', error);
        }

        try {
            // Fallback to sessionStorage
            const stored = sessionStorage.getItem(this.fallbackStorageKey);
            if (stored) {
                this.settings = { ...this.defaultSettings, ...JSON.parse(stored) };
                return;
            }
        } catch (error) {
            console.warn('Failed to load settings from sessionStorage:', error);
        }

        // Use defaults if no stored settings
        this.settings = { ...this.defaultSettings };
    }

    /**
     * Save settings to storage
     */
    saveSettings() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Failed to save to localStorage, using sessionStorage:', error);
            try {
                sessionStorage.setItem(this.fallbackStorageKey, JSON.stringify(this.settings));
            } catch (fallbackError) {
                console.error('Failed to save settings to any storage:', fallbackError);
            }
        }
    }

    /**
     * Get a setting value
     * @param {string} path - Dot notation path to setting (e.g., 'memory.maxMemoryUsage')
     * @returns {*} Setting value
     */
    get(path) {
        return this.getNestedValue(this.settings, path);
    }

    /**
     * Set a setting value
     * @param {string} path - Dot notation path to setting
     * @param {*} value - New value
     * @param {boolean} save - Whether to save immediately
     */
    set(path, value, save = true) {
        const oldValue = this.get(path);
        this.setNestedValue(this.settings, path, value);
        
        if (save) {
            this.saveSettings();
        }
        
        // Emit change event
        this.emitChange(path, value, oldValue);
    }

    /**
     * Update multiple settings at once
     * @param {Object} updates - Object with setting paths and values
     */
    updateMultiple(updates) {
        const changes = [];
        
        for (const [path, value] of Object.entries(updates)) {
            const oldValue = this.get(path);
            this.setNestedValue(this.settings, path, value);
            changes.push({ path, value, oldValue });
        }
        
        this.saveSettings();
        
        // Emit change events
        changes.forEach(({ path, value, oldValue }) => {
            this.emitChange(path, value, oldValue);
        });
    }

    /**
     * Reset settings to defaults
     * @param {string} section - Optional section to reset (e.g., 'memory')
     */
    reset(section = null) {
        if (section) {
            this.settings[section] = { ...this.defaultSettings[section] };
        } else {
            this.settings = { ...this.defaultSettings };
        }
        
        this.saveSettings();
        this.emitChange('reset', this.settings, null);
    }

    /**
     * Validate settings and fix invalid values
     */
    validateSettings() {
        // Memory settings validation
        if (this.settings.memory.maxMemoryUsage < 128) {
            this.settings.memory.maxMemoryUsage = 128;
        }
        if (this.settings.memory.cleanupThreshold > 1) {
            this.settings.memory.cleanupThreshold = 0.8;
        }

        // Virtual list validation
        if (this.settings.virtualList.itemHeight < 40) {
            this.settings.virtualList.itemHeight = 40;
        }
        if (this.settings.virtualList.pageSize < 10) {
            this.settings.virtualList.pageSize = 10;
        }

        // Data retention validation
        if (this.settings.dataRetention.maxProductAge < 86400000) { // Minimum 1 day
            this.settings.dataRetention.maxProductAge = 86400000;
        }

        // Performance thresholds validation
        if (this.settings.notifications.alertThresholds.memoryUsage > 1) {
            this.settings.notifications.alertThresholds.memoryUsage = 0.85;
        }
    }

    /**
     * Export settings for backup
     * @returns {Object} Settings export data
     */
    exportSettings() {
        return {
            version: '1.0',
            timestamp: new Date().toISOString(),
            settings: this.settings,
            metadata: {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language
            }
        };
    }

    /**
     * Import settings from backup
     * @param {Object} exportData - Exported settings data
     * @returns {boolean} Success status
     */
    importSettings(exportData) {
        try {
            if (!exportData.settings) {
                throw new Error('Invalid export data format');
            }

            // Validate imported settings
            const importedSettings = { ...this.defaultSettings, ...exportData.settings };
            
            // Apply imported settings
            this.settings = importedSettings;
            this.validateSettings();
            this.saveSettings();
            
            this.emitChange('import', this.settings, null);
            return true;
        } catch (error) {
            console.error('Failed to import settings:', error);
            return false;
        }
    }

    /**
     * Get settings for specific manager integration
     * @param {string} manager - Manager name ('memory', 'realtime', etc.)
     * @returns {Object} Manager-specific settings
     */
    getManagerSettings(manager) {
        switch (manager) {
            case 'memory':
                return {
                    maxMemoryUsage: this.get('memory.maxMemoryUsage'),
                    cleanupThreshold: this.get('memory.cleanupThreshold'),
                    aggressiveCleanup: this.get('memory.aggressiveCleanup'),
                    monitoringInterval: this.get('memory.monitoringInterval')
                };
            
            case 'realtime':
                return {
                    fetchFrequency: this.get('dataSync.fetchFrequency'),
                    batchSize: this.get('dataSync.batchSize'),
                    maxRetries: this.get('dataSync.maxRetries'),
                    enableRealTime: this.get('dataSync.enableRealTime')
                };
            
            case 'virtualList':
                return {
                    itemHeight: this.get('virtualList.itemHeight'),
                    bufferSize: this.get('virtualList.bufferSize'),
                    pageSize: this.get('virtualList.pageSize'),
                    searchDebounce: this.get('virtualList.searchDebounce')
                };
            
            default:
                return this.settings[manager] || {};
        }
    }

    /**
     * Add event listener for settings changes
     * @param {string} event - Event name or setting path
     * @param {Function} callback - Callback function
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, new Set());
        }
        this.eventListeners.get(event).add(callback);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name or setting path
     * @param {Function} callback - Callback function
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).delete(callback);
        }
    }

    /**
     * Emit change event
     * @param {string} path - Setting path that changed
     * @param {*} newValue - New value
     * @param {*} oldValue - Previous value
     */
    emitChange(path, newValue, oldValue) {
        // Emit specific path event
        if (this.eventListeners.has(path)) {
            this.eventListeners.get(path).forEach(callback => {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    console.error('Error in settings change callback:', error);
                }
            });
        }

        // Emit general change event
        if (this.eventListeners.has('change')) {
            this.eventListeners.get('change').forEach(callback => {
                try {
                    callback(path, newValue, oldValue);
                } catch (error) {
                    console.error('Error in settings change callback:', error);
                }
            });
        }
    }

    /**
     * Get nested value from object using dot notation
     * @param {Object} obj - Object to search
     * @param {string} path - Dot notation path
     * @returns {*} Value at path
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * Set nested value in object using dot notation
     * @param {Object} obj - Object to modify
     * @param {string} path - Dot notation path
     * @param {*} value - Value to set
     */
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
    }

    /**
     * Get all settings
     * @returns {Object} All settings
     */
    getAll() {
        return { ...this.settings };
    }

    /**
     * Get settings summary for diagnostics
     * @returns {Object} Settings summary
     */
    getSummary() {
        return {
            memoryLimit: this.get('memory.maxMemoryUsage'),
            virtualListEnabled: this.get('virtualList.enableVirtualization'),
            realTimeEnabled: this.get('dataSync.enableRealTime'),
            theme: this.get('ui.theme'),
            dataRetentionDays: Math.floor(this.get('dataRetention.maxSalesAge') / 86400000),
            performanceMonitoring: this.get('performance.enableMonitoring')
        };
    }
}

// Make SettingsManager available globally
window.SettingsManager = SettingsManager;

// Create singleton instance
window.settingsManager = window.settingsManager || new SettingsManager();
