/**
 * Search Indexer for Product Search
 * Builds and maintains search index for 5M+ products
 * Tokenizes product titles and descriptions for fast search
 */

class SearchIndexer {
    constructor(indexedDBManager) {
        this.dbManager = indexedDBManager;
        this.stopWords = new Set([
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
        ]);
        this.minTokenLength = 2;
        this.maxTokenLength = 50;
        this.batchSize = 1000; // Process products in batches
    }

    /**
     * Rebuild the entire search index
     * @returns {Promise<Object>} Indexing statistics
     */
    async rebuildSearchIndex() {
        console.log('🔍 Starting search index rebuild...');
        const startTime = Date.now();
        
        try {
            // Clear existing search index
            await this.clearSearchIndex();
            
            // Get all products in batches
            let processedCount = 0;
            let totalTokens = 0;
            let hasMore = true;
            let cursor = null;
            
            while (hasMore) {
                const products = await this.getProductsBatch(cursor, this.batchSize);
                
                if (products.length === 0) {
                    hasMore = false;
                    break;
                }
                
                // Process batch
                const batchTokens = await this.indexProductsBatch(products);
                totalTokens += batchTokens;
                processedCount += products.length;
                
                // Update cursor for next batch
                cursor = products[products.length - 1].asin;
                
                // Log progress
                if (processedCount % 10000 === 0) {
                    console.log(`🔍 Indexed ${processedCount} products...`);
                }
            }
            
            const duration = Date.now() - startTime;
            const stats = {
                productsIndexed: processedCount,
                tokensCreated: totalTokens,
                duration: duration,
                tokensPerSecond: Math.round(totalTokens / (duration / 1000))
            };
            
            console.log('✅ Search index rebuild complete:', stats);
            return stats;
            
        } catch (error) {
            console.error('❌ Search index rebuild failed:', error);
            throw error;
        }
    }

    /**
     * Update search index for specific products
     * @param {Array} products - Products to update in index
     * @returns {Promise<number>} Number of tokens created
     */
    async updateIndexForProducts(products) {
        if (!products || products.length === 0) return 0;
        
        try {
            // Remove existing tokens for these products
            await this.removeProductsFromIndex(products.map(p => p.asin));
            
            // Add new tokens
            return await this.indexProductsBatch(products);
            
        } catch (error) {
            console.error('❌ Failed to update search index for products:', error);
            throw error;
        }
    }

    /**
     * Clear the entire search index
     */
    async clearSearchIndex() {
        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['productSearch'], 'readwrite');
            const store = transaction.objectStore('productSearch');
            
            await this.dbManager.promisifyRequest(store.clear());
            console.log('🗑️ Search index cleared');
            
        } catch (error) {
            console.error('❌ Failed to clear search index:', error);
            throw error;
        }
    }

    /**
     * Get products in batches for indexing
     */
    async getProductsBatch(cursor, batchSize) {
        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['listingsData'], 'readonly');
            const store = transaction.objectStore('listingsData');
            
            return new Promise((resolve, reject) => {
                const products = [];
                const keyRange = cursor ? IDBKeyRange.lowerBound(cursor, true) : null;
                const request = store.openCursor(keyRange);
                
                request.onsuccess = (event) => {
                    const dbCursor = event.target.result;
                    
                    if (dbCursor && products.length < batchSize) {
                        products.push(dbCursor.value);
                        dbCursor.continue();
                    } else {
                        resolve(products);
                    }
                };
                
                request.onerror = () => reject(request.error);
            });
            
        } catch (error) {
            console.error('❌ Failed to get products batch:', error);
            throw error;
        }
    }

    /**
     * Index a batch of products
     */
    async indexProductsBatch(products) {
        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['productSearch'], 'readwrite');
            const store = transaction.objectStore('productSearch');
            
            let tokenCount = 0;
            
            for (const product of products) {
                const tokens = this.tokenizeProduct(product);
                
                for (const token of tokens) {
                    const searchEntry = {
                        token: token.toLowerCase(),
                        asin: product.asin,
                        relevance: this.calculateTokenRelevance(token, product)
                    };
                    
                    await this.dbManager.promisifyRequest(store.put(searchEntry));
                    tokenCount++;
                }
            }
            
            return tokenCount;
            
        } catch (error) {
            console.error('❌ Failed to index products batch:', error);
            throw error;
        }
    }

    /**
     * Tokenize product for search indexing
     */
    tokenizeProduct(product) {
        const tokens = new Set();
        
        // Tokenize title
        if (product.title) {
            this.extractTokens(product.title, tokens);
        }
        
        // Tokenize description
        if (product.description) {
            this.extractTokens(product.description, tokens);
        }
        
        // Add ASIN as exact match token
        if (product.asin) {
            tokens.add(product.asin);
        }
        
        // Add brand if available
        if (product.brand) {
            this.extractTokens(product.brand, tokens);
        }
        
        return Array.from(tokens);
    }

    /**
     * Extract tokens from text
     */
    extractTokens(text, tokens) {
        if (!text) return;
        
        // Split on word boundaries and clean
        const words = text
            .toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => 
                word.length >= this.minTokenLength && 
                word.length <= this.maxTokenLength &&
                !this.stopWords.has(word)
            );
        
        words.forEach(word => tokens.add(word));
        
        // Add partial tokens for longer words
        words.forEach(word => {
            if (word.length > 4) {
                for (let i = 0; i <= word.length - 3; i++) {
                    const partial = word.substring(i, i + 3);
                    if (partial.length >= this.minTokenLength) {
                        tokens.add(partial);
                    }
                }
            }
        });
    }

    /**
     * Calculate token relevance score
     */
    calculateTokenRelevance(token, product) {
        let score = 1;
        
        // Higher score for title matches
        if (product.title && product.title.toLowerCase().includes(token.toLowerCase())) {
            score += 3;
        }
        
        // Medium score for brand matches
        if (product.brand && product.brand.toLowerCase().includes(token.toLowerCase())) {
            score += 2;
        }
        
        // Exact ASIN match gets highest score
        if (product.asin && product.asin.toLowerCase() === token.toLowerCase()) {
            score += 5;
        }
        
        return score;
    }

    /**
     * Remove products from search index
     */
    async removeProductsFromIndex(asins) {
        if (!asins || asins.length === 0) return;
        
        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['productSearch'], 'readwrite');
            const store = transaction.objectStore('productSearch');
            const index = store.index('asin');
            
            for (const asin of asins) {
                const request = index.openCursor(IDBKeyRange.only(asin));
                
                await new Promise((resolve, reject) => {
                    request.onsuccess = (event) => {
                        const cursor = event.target.result;
                        if (cursor) {
                            cursor.delete();
                            cursor.continue();
                        } else {
                            resolve();
                        }
                    };
                    
                    request.onerror = () => reject(request.error);
                });
            }
            
        } catch (error) {
            console.error('❌ Failed to remove products from index:', error);
            throw error;
        }
    }

    /**
     * Get indexing statistics
     */
    async getIndexStats() {
        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['productSearch'], 'readonly');
            const store = transaction.objectStore('productSearch');
            
            const totalTokens = await this.dbManager.promisifyRequest(store.count());
            
            // Count unique products
            const asinIndex = store.index('asin');
            const uniqueProducts = await this.dbManager.promisifyRequest(asinIndex.count());
            
            return {
                totalTokens,
                uniqueProducts,
                avgTokensPerProduct: uniqueProducts > 0 ? Math.round(totalTokens / uniqueProducts) : 0
            };
            
        } catch (error) {
            console.error('❌ Failed to get index stats:', error);
            return { totalTokens: 0, uniqueProducts: 0, avgTokensPerProduct: 0 };
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchIndexer;
} else {
    window.SearchIndexer = SearchIndexer;
}
