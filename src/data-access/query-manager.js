/**
 * Query Manager for Massive Dataset Operations
 * Efficient data access layer for querying 5M+ products and 20M+ sales records
 * Uses cursor-based pagination and indexed queries for optimal performance
 */

class QueryManager {
    constructor(indexedDBManager) {
        this.dbManager = indexedDBManager;
        this.queryCache = new Map();
        this.defaultPageSize = 1000;
        this.maxCacheSize = 100;
        this.cacheHits = 0;
        this.cacheMisses = 0;
        this.searchIndexer = null; // Will be initialized when needed

        // Search configuration
        this.searchConfig = {
            fallbackLimit: 500,           // Max results from fallback search
            streamingThreshold: 1000000,  // Use streaming search for stores > 1M records
            maxScanRatio: 10              // Scan at most 10x the result limit
        };
    }

    /**
     * Get products with pagination, filtering, and sorting using cursor-based approach
     * @param {Object} filter - Filter criteria
     * @param {number} pageSize - Number of records per page
     * @param {string} cursorKey - Cursor key for pagination
     * @param {Object} sort - Sort criteria {field, direction}
     * @returns {Promise<Object>} Paginated results with next cursor
     */
    async getProductsPaginated(filter = {}, pageSize = this.defaultPageSize, cursorKey = null, sort = null) {
        const cacheKey = this._generateCacheKey('products', { filter, pageSize, cursorKey, sort });

        if (this.queryCache.has(cacheKey)) {
            this.cacheHits++;
            return this.queryCache.get(cacheKey);
        }

        this.cacheMisses++;

        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['listingsData'], 'readonly');
            const store = transaction.objectStore('listingsData');

            let index = store;
            let keyRange = null;

            // Apply filters and sorting using composite indexes for optimal performance
            const { indexName, keyRange: filterKeyRange, direction } = this._selectOptimalIndex(filter, cursorKey, sort);

            if (indexName) {
                index = store.index(indexName);
                keyRange = filterKeyRange;
            } else {
                // No specific filter, use primary key for pagination
                if (cursorKey) {
                    keyRange = IDBKeyRange.lowerBound(this._extractCursorKey(cursorKey), true);
                }
            }

            const results = [];
            let count = 0;
            let nextCursor = null;

            return new Promise((resolve, reject) => {
                const request = index.openCursor(keyRange, direction || 'next');
                let reachedPage = false;
                let done = false;

                request.onsuccess = (event) => {
                    if (done) return; // No-op if already resolved

                    const dbcursor = event.target.result;

                    if (dbcursor) {
                        // Skip cursor if primaryKey tie-breaker indicates we should
                        if (this._shouldSkipCursor(dbcursor, cursorKey)) {
                            dbcursor.continue();
                            return;
                        }

                        const product = dbcursor.value;

                        // Apply additional filters
                        if (this._matchesFilter(product, filter)) {
                            results.push(product);
                            count++;

                            // Check if we've reached the page size
                            if (count >= pageSize && !reachedPage) {
                                reachedPage = true;
                                // Store nextCursor for pagination
                                nextCursor = this._extractStableCursor(dbcursor, indexName);

                                // Do one continue() to check if there are more records
                                dbcursor.continue();
                                return;
                            }
                        }

                        // If we've reached page size and this is the next call after continue()
                        if (reachedPage) {
                            const result = {
                                data: results,
                                nextCursor,
                                hasMore: true // We know there's more since cursor is not null
                            };

                            done = true; // Set done flag to no-op future events
                            this._cacheResult(cacheKey, result);
                            resolve(result);
                            return;
                        }

                        dbcursor.continue();
                    } else {
                        // No more records
                        const result = {
                            data: results,
                            nextCursor: null,
                            hasMore: false
                        };

                        done = true;
                        this._cacheResult(cacheKey, result);
                        resolve(result);
                    }
                };

                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error in getProductsPaginated:', error);
            throw error;
        }
    }

    /**
     * Get sales data by date range for specific ASIN
     * @param {Date} startDate - Start date
     * @param {Date} endDate - End date
     * @param {string} asin - Product ASIN
     * @returns {Promise<Array>} Sales data
     */
    async getSalesDataByDateRange(startDate, endDate, asin = null) {
        // Convert Date objects to ISO date strings for IndexedDB compatibility
        const startDateStr = this._toISODateString(startDate);
        const endDateStr = this._toISODateString(endDate);

        const cacheKey = this._generateCacheKey('sales', { startDate: startDateStr, endDate: endDateStr, asin });

        if (this.queryCache.has(cacheKey)) {
            this.cacheHits++;
            return this.queryCache.get(cacheKey);
        }

        this.cacheMisses++;

        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['dailySalesHistory'], 'readonly');
            const store = transaction.objectStore('dailySalesHistory');
            const index = asin ? store.index('asin_date') : store.index('date');

            let keyRange;
            if (asin) {
                keyRange = IDBKeyRange.bound([asin, startDateStr], [asin, endDateStr]);
            } else {
                keyRange = IDBKeyRange.bound(startDateStr, endDateStr);
            }

            const results = [];

            return new Promise((resolve, reject) => {
                const request = index.openCursor(keyRange);

                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    
                    if (cursor) {
                        results.push(cursor.value);
                        cursor.continue();
                    } else {
                        this._cacheResult(cacheKey, results);
                        resolve(results);
                    }
                };

                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error in getSalesDataByDateRange:', error);
            throw error;
        }
    }

    /**
     * Search products using indexed search for scalability
     * @param {string} query - Search query
     * @param {Object} filters - Additional filters
     * @returns {Promise<Array>} Search results
     */
    async searchProducts(query, filters = {}) {
        const cacheKey = this._generateCacheKey('search', { query, filters });

        if (this.queryCache.has(cacheKey)) {
            this.cacheHits++;
            return this.queryCache.get(cacheKey);
        }

        this.cacheMisses++;

        try {
            const db = this.dbManager.getDB();
            const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 2);

            if (searchTerms.length === 0) {
                return [];
            }

            // Try to use SearchIndexer first
            const indexer = this.getSearchIndexer();
            let matchingASINs = new Set();

            if (indexer) {
                try {
                    matchingASINs = await indexer.searchIndex(query);
                    console.log(`🔍 SearchIndexer found ${matchingASINs.size} matches for "${query}"`);
                } catch (error) {
                    console.warn('SearchIndexer failed, falling back to legacy search:', error);
                    matchingASINs = new Set();
                }
            }

            // Fallback to legacy search index if SearchIndexer failed or found no results
            if (matchingASINs.size === 0) {
                console.log('🔍 Trying legacy search index...');

                // Get ASINs from legacy search index for each term
                const asinSets = await Promise.all(
                    searchTerms.map(term => this._getASINsForSearchTerm(db, term))
                );

                // Check if search index is empty or incomplete - fallback to basic search
                if (asinSets.length === 0 || asinSets.some(set => set.size === 0)) {
                    console.log('🔍 Legacy search index incomplete, falling back to basic search');
                    return this._selectFallbackSearch(query, filters);
                }

                // Find intersection of ASINs (products that match all terms)
                matchingASINs = asinSets[0];
                for (let i = 1; i < asinSets.length; i++) {
                    matchingASINs = new Set([...matchingASINs].filter(asin => asinSets[i].has(asin)));
                }
            }

            if (matchingASINs.size === 0) {
                // No matches in any index, try fallback search
                console.log('🔍 No index matches found, falling back to basic search');
                return this._selectFallbackSearch(query, filters);
            }

            // Fetch product details for matching ASINs
            const results = await this._fetchProductsByASINs(db, Array.from(matchingASINs), filters);

            // Calculate relevance scores and sort
            const scoredResults = results.map(product => ({
                ...product,
                relevanceScore: this._calculateRelevance(product, searchTerms)
            })).sort((a, b) => b.relevanceScore - a.relevanceScore);

            this._cacheResult(cacheKey, scoredResults);
            return scoredResults;
        } catch (error) {
            console.error('Error in searchProducts:', error);
            // Fallback to basic search if index search fails
            return this._selectFallbackSearch(query, filters);
        }
    }

    /**
     * Get ASINs for a specific search term from the search index
     * @param {IDBDatabase} db - Database instance
     * @param {string} term - Search term
     * @returns {Promise<Set>} Set of ASINs
     */
    async _getASINsForSearchTerm(db, term) {
        const transaction = db.transaction(['productSearch'], 'readonly');
        const store = transaction.objectStore('productSearch');
        const index = store.index('token');

        return new Promise((resolve, reject) => {
            const asins = new Set();
            const request = index.openCursor(IDBKeyRange.only(term));

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    asins.add(cursor.value.asin);
                    cursor.continue();
                } else {
                    resolve(asins);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Fetch products by ASINs with additional filtering
     * @param {IDBDatabase} db - Database instance
     * @param {Array} asins - Array of ASINs
     * @param {Object} filters - Additional filters
     * @returns {Promise<Array>} Filtered products
     */
    async _fetchProductsByASINs(db, asins, filters) {
        const transaction = db.transaction(['listingsData'], 'readonly');
        const store = transaction.objectStore('listingsData');

        const products = [];

        for (const asin of asins) {
            try {
                const product = await this.dbManager.promisifyRequest(store.get(asin));
                if (product && this._matchesFilter(product, filters)) {
                    products.push(product);
                }
            } catch (error) {
                console.warn(`Failed to fetch product ${asin}:`, error);
            }
        }

        return products;
    }

    /**
     * Select appropriate fallback search method based on dataset size
     * @param {string} query - Search query
     * @param {Object} filters - Additional filters
     * @returns {Promise<Array>} Search results
     */
    async _selectFallbackSearch(query, filters) {
        try {
            // Check store size to determine search method
            const recordCount = await this.dbManager.getRecordCount('listingsData');

            if (recordCount > this.searchConfig.streamingThreshold) {
                console.log(`📊 Large dataset (${recordCount} records), using streaming search`);
                return this._streamingFallbackSearch(query, filters, this.searchConfig.fallbackLimit);
            } else {
                console.log(`📊 Medium dataset (${recordCount} records), using cursor search`);
                return this._fallbackSearch(query, filters, this.searchConfig.fallbackLimit);
            }
        } catch (error) {
            console.error('Error determining search method:', error);
            // Default to bounded cursor search
            return this._fallbackSearch(query, filters, this.searchConfig.fallbackLimit);
        }
    }

    /**
     * Fallback search using bounded cursor scan (for when search index is not available)
     * @param {string} query - Search query
     * @param {Object} filters - Additional filters
     * @param {number} limit - Maximum number of results to return (default: 500)
     * @returns {Promise<Array>} Search results
     */
    async _fallbackSearch(query, filters, limit = 500) {
        console.warn(`⚠️ Using fallback search with limit ${limit}. Consider rebuilding search index for better performance.`);

        const db = this.dbManager.getDB();
        const transaction = db.transaction(['listingsData'], 'readonly');
        const store = transaction.objectStore('listingsData');

        const results = [];
        const searchTerms = query.toLowerCase().split(' ');
        let scannedCount = 0;
        const maxScanLimit = limit * 10; // Scan at most 10x the result limit

        return new Promise((resolve, reject) => {
            const request = store.openCursor();

            request.onsuccess = (event) => {
                const cursor = event.target.result;

                if (cursor && results.length < limit && scannedCount < maxScanLimit) {
                    scannedCount++;
                    const product = cursor.value;
                    const searchText = `${product.title} ${product.description} ${product.asin}`.toLowerCase();

                    // Check if all search terms are present
                    const matches = searchTerms.every(term => searchText.includes(term));

                    if (matches && this._matchesFilter(product, filters)) {
                        results.push({
                            ...product,
                            relevanceScore: this._calculateRelevance(product, searchTerms)
                        });
                    }

                    // Continue scanning if we haven't hit limits
                    if (results.length < limit && scannedCount < maxScanLimit) {
                        cursor.continue();
                    } else {
                        // Hit limit, finish up
                        this._finalizeFallbackSearch(results, resolve, scannedCount, maxScanLimit);
                    }
                } else {
                    // No more records or hit limits
                    this._finalizeFallbackSearch(results, resolve, scannedCount, maxScanLimit);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Finalize fallback search results
     */
    _finalizeFallbackSearch(results, resolve, scannedCount, maxScanLimit) {
        // Sort by relevance score
        results.sort((a, b) => b.relevanceScore - a.relevanceScore);

        if (scannedCount >= maxScanLimit) {
            console.warn(`⚠️ Fallback search hit scan limit (${maxScanLimit} records). Results may be incomplete.`);
        }

        console.log(`🔍 Fallback search completed: ${results.length} results from ${scannedCount} scanned records`);
        resolve(results);
    }

    /**
     * Streaming fallback search using IndexedDBManager.iterate for very large datasets
     * @param {string} query - Search query
     * @param {Object} filters - Additional filters
     * @param {number} limit - Maximum number of results to return
     * @returns {Promise<Array>} Search results
     */
    async _streamingFallbackSearch(query, filters, limit = 500) {
        console.warn(`⚠️ Using streaming fallback search with limit ${limit}. Consider rebuilding search index.`);

        const results = [];
        const searchTerms = query.toLowerCase().split(' ');
        let scannedCount = 0;
        const maxScanLimit = limit * 10;

        try {
            await this.dbManager.iterate('listingsData', {
                batchSize: 1000,
                onBatch: (batch) => {
                    for (const product of batch) {
                        if (results.length >= limit || scannedCount >= maxScanLimit) {
                            return false; // Stop iteration
                        }

                        scannedCount++;
                        const searchText = `${product.title} ${product.description} ${product.asin}`.toLowerCase();

                        // Check if all search terms are present
                        const matches = searchTerms.every(term => searchText.includes(term));

                        if (matches && this._matchesFilter(product, filters)) {
                            results.push({
                                ...product,
                                relevanceScore: this._calculateRelevance(product, searchTerms)
                            });
                        }
                    }
                    return true; // Continue iteration
                }
            });
        } catch (error) {
            console.error('Error in streaming fallback search:', error);
            throw error;
        }

        // Sort by relevance score
        results.sort((a, b) => b.relevanceScore - a.relevanceScore);

        if (scannedCount >= maxScanLimit) {
            console.warn(`⚠️ Streaming search hit scan limit (${maxScanLimit} records). Results may be incomplete.`);
        }

        console.log(`🔍 Streaming search completed: ${results.length} results from ${scannedCount} scanned records`);
        return results;
    }

    /**
     * Get top selling products for a specific timeframe
     * @param {string} timeframe - 'today', 'week', 'month', 'year'
     * @param {number} limit - Number of top products to return
     * @returns {Promise<Array>} Top selling products
     */
    async getTopSellingProducts(timeframe = 'month', limit = 100) {
        const cacheKey = this._generateCacheKey('topSelling', { timeframe, limit });

        if (this.queryCache.has(cacheKey)) {
            this.cacheHits++;
            return this.queryCache.get(cacheKey);
        }

        this.cacheMisses++;

        try {
            const dateRange = this._getDateRangeForTimeframe(timeframe);
            const salesData = await this.getSalesDataByDateRange(dateRange.start, dateRange.end);
            
            // Aggregate sales by ASIN
            const salesByAsin = new Map();
            
            salesData.forEach(sale => {
                const current = salesByAsin.get(sale.asin) || { quantity: 0, revenue: 0 };
                current.quantity += sale.quantity;
                current.revenue += sale.revenue;
                salesByAsin.set(sale.asin, current);
            });

            // Convert to array and sort by quantity
            const topProducts = Array.from(salesByAsin.entries())
                .map(([asin, data]) => ({ asin, ...data }))
                .sort((a, b) => b.quantity - a.quantity)
                .slice(0, limit);

            this._cacheResult(cacheKey, topProducts);
            return topProducts;
        } catch (error) {
            console.error('Error in getTopSellingProducts:', error);
            throw error;
        }
    }

    /**
     * Stream data provider for virtual scrolling
     * @param {Object} config - Configuration for streaming
     * @returns {AsyncGenerator} Data stream
     */
    async* streamData(config) {
        const { type, filter, batchSize = 1000 } = config;
        let cursor = null;
        let hasMore = true;

        while (hasMore) {
            let batch;
            
            switch (type) {
                case 'products':
                    batch = await this.getProductsPaginated(filter, batchSize, cursor);
                    break;
                case 'sales':
                    batch = await this.getSalesDataByDateRange(filter.startDate, filter.endDate);
                    hasMore = false; // Sales data is typically smaller
                    break;
                default:
                    throw new Error(`Unsupported stream type: ${type}`);
            }

            yield batch.data || batch;
            
            cursor = batch.nextCursor;
            hasMore = batch.hasMore;
        }
    }

    // clearCache() method moved to end of class for better organization

    /**
     * Count products matching filter criteria
     * @param {Object} filter - Filter criteria
     * @returns {Promise<number>} Total count of matching products
     */
    async countProducts(filter = {}) {
        const cacheKey = this._generateCacheKey('count', { filter });

        if (this.queryCache.has(cacheKey)) {
            this.cacheHits++;
            return this.queryCache.get(cacheKey);
        }

        this.cacheMisses++;

        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['listingsData'], 'readonly');
            const store = transaction.objectStore('listingsData');

            // Use appropriate index for counting
            const { indexName, keyRange } = this._selectOptimalIndex(filter, null);

            let count;
            if (indexName && keyRange) {
                const index = store.index(indexName);
                count = await this.dbManager.promisifyRequest(index.count(keyRange));
            } else {
                count = await this.dbManager.promisifyRequest(store.count());
            }

            // If we have additional filters not covered by indexes, we need to scan
            if (this._hasAdditionalFilters(filter)) {
                count = await this._countWithAdditionalFilters(store, filter, indexName, keyRange);
            }

            this._cacheResult(cacheKey, count);
            return count;

        } catch (error) {
            console.error('Error counting products:', error);
            throw error;
        }
    }

    /**
     * Check if filter has additional criteria not covered by indexes
     */
    _hasAdditionalFilters(filter) {
        const indexedFields = ['marketplace', 'category', 'dateRange', 'status'];
        return Object.keys(filter).some(key => !indexedFields.includes(key));
    }

    /**
     * Count products with additional filters using cursor scan
     */
    async _countWithAdditionalFilters(store, filter, indexName, keyRange) {
        return new Promise((resolve, reject) => {
            let count = 0;
            const index = indexName ? store.index(indexName) : store;
            const request = index.openCursor(keyRange);

            request.onsuccess = (event) => {
                const cursor = event.target.result;

                if (cursor) {
                    if (this._matchesFilter(cursor.value, filter)) {
                        count++;
                    }
                    cursor.continue();
                } else {
                    resolve(count);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Initialize search indexer
     */
    getSearchIndexer() {
        if (!this.searchIndexer) {
            if (typeof SearchIndexer !== 'undefined') {
                this.searchIndexer = new SearchIndexer(this.dbManager);
            } else {
                console.warn('SearchIndexer not available');
            }
        }
        return this.searchIndexer;
    }

    /**
     * Rebuild search index for all products
     * @returns {Promise<Object>} Indexing statistics
     */
    async rebuildSearchIndex() {
        const indexer = this.getSearchIndexer();
        if (!indexer) {
            throw new Error('Search indexer not available');
        }
        return await indexer.rebuildSearchIndex();
    }

    /**
     * Update search index for specific products
     * @param {Array} products - Products to update in index
     * @returns {Promise<number>} Number of tokens created
     */
    async updateSearchIndexForProducts(products) {
        const indexer = this.getSearchIndexer();
        if (!indexer) {
            console.warn('Search indexer not available, skipping index update');
            return 0;
        }
        return await indexer.updateIndexForProducts(products);
    }

    /**
     * Remove products from search index
     * @param {Array<string>} asins - ASINs of products to remove from index
     * @returns {Promise<number>} Number of tokens removed
     */
    async removeProductsFromSearchIndex(asins) {
        if (!Array.isArray(asins) || asins.length === 0) {
            return 0;
        }

        try {
            const db = await this.dbManager.getDB();
            const transaction = db.transaction(['productSearch'], 'readwrite');
            const store = transaction.objectStore('productSearch');

            let removedCount = 0;

            // Remove all tokens for each ASIN
            for (const asin of asins) {
                const asinIndex = store.index('asin');
                const request = asinIndex.openCursor(IDBKeyRange.only(asin));

                await new Promise((resolve, reject) => {
                    request.onsuccess = (event) => {
                        const cursor = event.target.result;
                        if (cursor) {
                            cursor.delete();
                            removedCount++;
                            cursor.continue();
                        } else {
                            resolve();
                        }
                    };
                    request.onerror = () => reject(request.error);
                });
            }

            await new Promise((resolve, reject) => {
                transaction.oncomplete = () => resolve();
                transaction.onerror = () => reject(transaction.error);
            });

            console.log(`🗑️ Removed ${removedCount} search tokens for ${asins.length} products`);
            return removedCount;
        } catch (error) {
            console.error('❌ Failed to remove products from search index:', error);
            return 0;
        }
    }

    /**
     * Get search index statistics
     * @returns {Promise<Object>} Index statistics
     */
    async getSearchIndexStats() {
        const indexer = this.getSearchIndexer();
        if (!indexer) {
            return { totalTokens: 0, uniqueProducts: 0, avgTokensPerProduct: 0 };
        }
        return await indexer.getIndexStats();
    }

    /**
     * Get products before a cursor (for bidirectional paging)
     * @param {Object} filter - Filter criteria
     * @param {number} pageSize - Number of records per page
     * @param {string} cursorKey - Cursor key for pagination
     * @returns {Promise<Object>} Paginated results with previous cursor
     */
    async getProductsBeforeCursor(filter = {}, pageSize = this.defaultPageSize, cursorKey = null) {
        if (!cursorKey) {
            return { data: [], previousCursor: null, hasPrevious: false };
        }

        try {
            const db = this.dbManager.getDB();
            const transaction = db.transaction(['listingsData'], 'readonly');
            const store = transaction.objectStore('listingsData');

            let index = store;
            let keyRange = null;

            // Apply filters using composite indexes for optimal performance
            const { indexName, keyRange: filterKeyRange } = this._selectOptimalIndex(filter, cursorKey);

            if (indexName) {
                index = store.index(indexName);
                // For reverse pagination, construct proper composite upperBound
                keyRange = this._constructReverseKeyRange(filter, cursorKey, indexName);
            } else {
                keyRange = IDBKeyRange.upperBound(this._extractCursorKey(cursorKey), true);
            }

            const results = [];
            let count = 0;
            let previousCursor = null;

            return new Promise((resolve, reject) => {
                // Open cursor in reverse direction
                const request = index.openCursor(keyRange, 'prev');

                request.onsuccess = (event) => {
                    const cursor = event.target.result;

                    if (cursor && count < pageSize) {
                        // Skip cursor if primaryKey tie-breaker indicates we should
                        if (this._shouldSkipCursor(cursor, cursorKey)) {
                            cursor.continue();
                            return;
                        }

                        results.unshift(cursor.value); // Add to beginning for correct order
                        previousCursor = this._extractStableCursor(cursor, indexName);
                        count++;
                        cursor.continue();
                    } else {
                        const result = {
                            data: results,
                            previousCursor,
                            hasPrevious: cursor !== null
                        };

                        resolve(result);
                    }
                };

                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error in getProductsBeforeCursor:', error);
            return { data: [], previousCursor: null, hasPrevious: false };
        }
    }

    /**
     * Clear query cache
     */
    clearCache() {
        this.queryCache.clear();
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }

    /**
     * Get cache statistics
     * @returns {Object} Cache statistics
     */
    getCacheStats() {
        return {
            size: this.queryCache.size,
            maxSize: this.maxCacheSize,
            hits: this.cacheHits,
            misses: this.cacheMisses,
            hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
        };
    }

    // Private helper methods
    _generateCacheKey(type, params) {
        return `${type}:${JSON.stringify(params)}`;
    }

    /**
     * Extract stable cursor value for pagination with primaryKey tie-breaker
     * @param {IDBCursor} cursor - Database cursor
     * @param {string} indexName - Index name being used
     * @returns {Object} Stable cursor value with primaryKey
     */
    _extractStableCursor(cursor, indexName) {
        // Always include primaryKey for tie-breaking
        const stableCursor = {
            key: cursor.key,
            primaryKey: cursor.primaryKey
        };

        if (!indexName) {
            // Primary key cursor - key is the primaryKey
            return stableCursor;
        }

        // For composite indexes, we need both the composite key and primaryKey
        if (indexName.includes('lastUpdated')) {
            // For lastUpdated indexes, the key might be composite
            return stableCursor;
        }

        // For other indexes, use the full key with primaryKey
        return stableCursor;
    }

    /**
     * Extract cursor key for pagination, handling both old and new cursor formats
     * @param {*} cursorKey - Cursor key (could be simple value or {key, primaryKey} object)
     * @returns {*} The key portion for range queries
     */
    _extractCursorKey(cursorKey) {
        if (!cursorKey) return null;

        // Handle new cursor format with primaryKey
        if (typeof cursorKey === 'object' && cursorKey.key !== undefined) {
            return cursorKey.key;
        }

        // Handle legacy cursor format
        return cursorKey;
    }

    /**
     * Check if we should skip a cursor based on primaryKey tie-breaker
     * @param {IDBCursor} cursor - Database cursor
     * @param {*} savedCursor - Saved cursor with primaryKey
     * @returns {boolean} True if cursor should be skipped
     */
    _shouldSkipCursor(cursor, savedCursor) {
        if (!savedCursor || typeof savedCursor !== 'object' || !savedCursor.primaryKey) {
            return false;
        }

        // Skip if primaryKey is less than or equal to saved primaryKey
        return cursor.primaryKey <= savedCursor.primaryKey;
    }

    /**
     * Construct proper key range for reverse pagination with composite indexes
     * @param {Object} filter - Filter criteria
     * @param {*} cursorKey - Cursor key for pagination
     * @param {string} indexName - Index name being used
     * @returns {IDBKeyRange} Proper key range for reverse pagination
     */
    _constructReverseKeyRange(filter, cursorKey, indexName) {
        const extractedKey = this._extractCursorKey(cursorKey);

        if (indexName === 'marketplace_category_lastUpdated') {
            // For composite index, construct upperBound with filter prefix
            return IDBKeyRange.upperBound([filter.marketplace, filter.category, extractedKey], true);
        } else if (indexName === 'marketplace_lastUpdated') {
            return IDBKeyRange.upperBound([filter.marketplace, extractedKey], true);
        } else if (indexName === 'category_lastUpdated') {
            return IDBKeyRange.upperBound([filter.category, extractedKey], true);
        } else if (indexName === 'marketplace_category') {
            // For non-temporal composite, use the full composite key
            return IDBKeyRange.upperBound([filter.marketplace, filter.category], true);
        } else {
            // For single-field indexes, use the extracted key
            return IDBKeyRange.upperBound(extractedKey, true);
        }
    }

    /**
     * Convert Date object to ISO date string (YYYY-MM-DD)
     * @param {Date} date - Date object
     * @returns {string} ISO date string
     */
    _toISODateString(date) {
        if (typeof date === 'string') {
            // Already a string, ensure it's in YYYY-MM-DD format
            return date.split('T')[0];
        }

        if (!(date instanceof Date)) {
            throw new Error('Invalid date object');
        }

        return date.toISOString().split('T')[0];
    }

    /**
     * Select optimal index for combined filters and sorting
     * @param {Object} filter - Filter criteria
     * @param {string} cursorKey - Cursor key for pagination
     * @param {Object} sort - Sort criteria {field, direction}
     * @returns {Object} Index name, key range, and cursor direction
     */
    _selectOptimalIndex(filter, cursorKey, sort = null) {
        const hasMarketplace = filter.marketplace;
        const hasCategory = filter.category;
        const hasDateRange = filter.dateRange;
        const hasStatus = filter.status;

        // Maximum timestamp for numeric date bounds
        const MAX_TS = 8640000000000000; // Number.MAX_SAFE_INTEGER-ish for dates

        // Determine cursor direction based on sort
        const direction = sort && sort.direction === 'desc' ? 'prev' : 'next';

        // Check if we can use an index for sorting
        const sortableIndexes = ['title', 'price', 'lastUpdated', 'status', 'marketplace', 'category'];
        const canSortByIndex = sort && sortableIndexes.includes(sort.field);

        // If sorting by a field that has an index and no complex filters, use that index
        if (canSortByIndex && !hasMarketplace && !hasCategory && !hasDateRange && !hasStatus) {
            return {
                indexName: sort.field,
                keyRange: cursorKey ?
                    (direction === 'prev' ?
                        IDBKeyRange.upperBound(this._extractCursorKey(cursorKey), true) :
                        IDBKeyRange.lowerBound(this._extractCursorKey(cursorKey), true)
                    ) : null,
                direction
            };
        }

        // Determine the most selective composite index
        if (hasMarketplace && hasCategory && hasDateRange) {
            // Use three-way composite index
            const startTs = this._ensureNumericTimestamp(filter.dateRange.start);
            const endTs = this._ensureNumericTimestamp(filter.dateRange.end);
            const indexKey = [filter.marketplace, filter.category, startTs];
            const endKey = [filter.marketplace, filter.category, endTs];

            return {
                indexName: 'marketplace_category_lastUpdated',
                keyRange: cursorKey ?
                    IDBKeyRange.bound([filter.marketplace, filter.category, this._ensureNumericTimestamp(this._extractCursorKey(cursorKey))], endKey, true, false) :
                    IDBKeyRange.bound(indexKey, endKey),
                direction
            };
        } else if (hasMarketplace && hasCategory) {
            // Use marketplace + category composite index with stable ordering
            // For pagination, use marketplace_category_lastUpdated for stable sort
            if (cursorKey) {
                return {
                    indexName: 'marketplace_category_lastUpdated',
                    keyRange: IDBKeyRange.bound(
                        [filter.marketplace, filter.category, this._ensureNumericTimestamp(this._extractCursorKey(cursorKey))],
                        [filter.marketplace, filter.category, MAX_TS],
                        true, false
                    ),
                    direction
                };
            } else {
                return {
                    indexName: 'marketplace_category',
                    keyRange: IDBKeyRange.only([filter.marketplace, filter.category]),
                    direction
                };
            }
        } else if (hasMarketplace && hasDateRange) {
            // Use marketplace + date composite index
            const startTs = this._ensureNumericTimestamp(filter.dateRange.start);
            const endTs = this._ensureNumericTimestamp(filter.dateRange.end);
            const indexKey = [filter.marketplace, startTs];
            const endKey = [filter.marketplace, endTs];

            return {
                indexName: 'marketplace_lastUpdated',
                keyRange: cursorKey ?
                    IDBKeyRange.bound([filter.marketplace, this._ensureNumericTimestamp(this._extractCursorKey(cursorKey))], endKey, true, false) :
                    IDBKeyRange.bound(indexKey, endKey),
                direction
            };
        } else if (hasCategory && hasDateRange) {
            // Use category + date composite index
            const startTs = this._ensureNumericTimestamp(filter.dateRange.start);
            const endTs = this._ensureNumericTimestamp(filter.dateRange.end);
            const indexKey = [filter.category, startTs];
            const endKey = [filter.category, endTs];

            return {
                indexName: 'category_lastUpdated',
                keyRange: cursorKey ?
                    IDBKeyRange.bound([filter.category, this._ensureNumericTimestamp(this._extractCursorKey(cursorKey))], endKey, true, false) :
                    IDBKeyRange.bound(indexKey, endKey),
                direction
            };
        } else if (hasMarketplace) {
            // For single marketplace with cursor, use marketplace_lastUpdated for stable sort
            if (cursorKey) {
                return {
                    indexName: 'marketplace_lastUpdated',
                    keyRange: IDBKeyRange.bound(
                        [filter.marketplace, this._ensureNumericTimestamp(this._extractCursorKey(cursorKey))],
                        [filter.marketplace, MAX_TS],
                        true, false
                    ),
                    direction
                };
            } else {
                return {
                    indexName: 'marketplace',
                    keyRange: IDBKeyRange.only(filter.marketplace),
                    direction
                };
            }
        } else if (hasCategory) {
            // For single category with cursor, use category_lastUpdated for stable sort
            if (cursorKey) {
                return {
                    indexName: 'category_lastUpdated',
                    keyRange: IDBKeyRange.bound(
                        [filter.category, this._ensureNumericTimestamp(this._extractCursorKey(cursorKey))],
                        [filter.category, MAX_TS],
                        true, false
                    ),
                    direction
                };
            } else {
                return {
                    indexName: 'category',
                    keyRange: IDBKeyRange.only(filter.category),
                    direction
                };
            }
        } else if (hasDateRange) {
            // Use single date index (scalar key)
            const startTs = this._ensureNumericTimestamp(filter.dateRange.start);
            const endTs = this._ensureNumericTimestamp(filter.dateRange.end);
            return {
                indexName: 'lastUpdated',
                keyRange: cursorKey ?
                    IDBKeyRange.bound(this._ensureNumericTimestamp(this._extractCursorKey(cursorKey)), endTs, true, false) :
                    IDBKeyRange.bound(startTs, endTs),
                direction
            };
        } else if (hasStatus) {
            // Use status index for status-only filtering
            return {
                indexName: 'status',
                keyRange: IDBKeyRange.only(filter.status),
                direction
            };
        }

        // No specific filters
        return { indexName: null, keyRange: null, direction };
    }

    _cacheResult(key, result) {
        if (this.queryCache.size >= this.maxCacheSize) {
            // Remove oldest entry
            const firstKey = this.queryCache.keys().next().value;
            this.queryCache.delete(firstKey);
        }
        
        this.queryCache.set(key, result);
    }

    _matchesFilter(item, filter) {
        for (const [key, value] of Object.entries(filter)) {
            if (key === 'dateRange' || key === 'marketplace' || key === 'category') {
                continue; // Already handled by index
            }
            
            if (item[key] !== value) {
                return false;
            }
        }
        return true;
    }

    _calculateRelevance(product, searchTerms) {
        let score = 0;
        const title = product.title.toLowerCase();
        const description = product.description.toLowerCase();
        
        searchTerms.forEach(term => {
            if (title.includes(term)) score += 3;
            if (description.includes(term)) score += 1;
            if (product.asin.toLowerCase().includes(term)) score += 5;
        });
        
        return score;
    }

    _getDateRangeForTimeframe(timeframe) {
        const now = new Date();
        const start = new Date();
        
        switch (timeframe) {
            case 'today':
                start.setHours(0, 0, 0, 0);
                break;
            case 'week':
                start.setDate(now.getDate() - 7);
                break;
            case 'month':
                start.setMonth(now.getMonth() - 1);
                break;
            case 'year':
                start.setFullYear(now.getFullYear() - 1);
                break;
            default:
                start.setMonth(now.getMonth() - 1);
        }
        
        return { start, end: now };
    }

    /**
     * Ensure timestamp is numeric (milliseconds since epoch)
     * @param {string|number|Date} timestamp - Input timestamp
     * @returns {number} Numeric timestamp
     */
    _ensureNumericTimestamp(timestamp) {
        if (typeof timestamp === 'number') {
            return timestamp;
        }
        if (timestamp instanceof Date) {
            return timestamp.getTime();
        }
        if (typeof timestamp === 'string') {
            return new Date(timestamp).getTime();
        }
        return Date.now();
    }
}

// Make QueryManager available globally
window.QueryManager = QueryManager;
