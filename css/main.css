/* Main CSS File
 * Imports all partial CSS files in the correct order
 * This file should be included in the HTML instead of the monolithic snapapp.css
 */

/* 1. Design Tokens and Variables (must come first) */
@import url('./tokens.css');

/* 2. Base Styles */
@import url('./base.css');

/* 3. Layout Components */
@import url('./layout.css');

/* 4. Component Styles (to be created) */
/* @import url('./components.css'); */

/* 5. Utility Classes (to be created) */
/* @import url('./utilities.css'); */

/* 6. Theme Overrides (to be created) */
/* @import url('./themes.css'); */

/* Note: The remaining CSS from snapapp.css will be split into the appropriate partial files
   as part of the ongoing refactoring process. This modular approach provides:
   
   - Better organization and maintainability
   - Easier debugging and development
   - Reduced file sizes for better performance
   - Clear separation of concerns
   - Easier team collaboration
   
   To complete the refactoring:
   1. Extract component styles into components.css
   2. Extract utility classes into utilities.css  
   3. Extract theme-specific overrides into themes.css
   4. Update HTML to include main.css instead of snapapp.css
   5. Remove the old snapapp.css file
*/
