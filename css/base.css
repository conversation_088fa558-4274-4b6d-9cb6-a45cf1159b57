/* Base Styles
 * Fundamental styles for HTML elements and global layout
 */

/* ==========================================================================
   REQUIRED CSS VARIABLES
   ==========================================================================
   
   The following CSS variables are required for proper functionality and are
   defined in css/tokens.css:
   
   Colors:
   --color-positive: Positive/success color (default: var(--palette-green))
   --color-negative: Negative/error color (default: var(--palette-red))
   --color-increase: Increase/upward trend color (default: var(--palette-green))
   --color-decrease: Decrease/downward trend color (default: var(--palette-red))
   --color-up: Upward direction color (default: var(--palette-green))
   --color-down: Downward direction color (default: var(--palette-red))
   --color-success: Success state color (default: var(--palette-green))
   --color-danger: Danger/error state color (default: var(--palette-red))
   --color-rejected: Rejected state color (default: var(--palette-orange))
   
   Text Colors:
   --text-primary: Primary text color (default: var(--palette-gray-500))
   --text-secondary: Secondary text color (default: var(--palette-gray-400))
   
   Background Colors:
   --bg-secondary: Secondary background color (default: var(--palette-gray-100))
   
   Transitions:
   --theme-transition: Theme transition timing (default: transform 0.3s ease, opacity 0.3s ease)
   
   Note: These variables are defined in css/tokens.css and should be used
   consistently across the application for maintainable theming.
   ========================================================================== */

/* Font Face Definitions */
@font-face {
  font-family: 'Amazon Ember';
  src: url('../fonts/AmazonEmber_Regular.woff2') format('woff2'),
       url('../fonts/AmazonEmber_Regular.woff') format('woff'),
       url('../fonts/AmazonEmber_Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Amazon Ember';
  src: url('../fonts/Amazon-Ember-Medium.woff2') format('woff2'),
       url('../fonts/Amazon-Ember-Medium.woff') format('woff'),
       url('../fonts/Amazon-Ember-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Amazon Ember';
  src: url('../fonts/AmazonEmber_Bold.woff2') format('woff2'),
       url('../fonts/AmazonEmber_Bold.woff') format('woff'),
       url('../fonts/AmazonEmber_Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Amazon Ember';
  src: url('../fonts/AmazonEmber_He.woff2') format('woff2'),
       url('../fonts/AmazonEmber_He.woff') format('woff'),
       url('../fonts/AmazonEmber_He.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Global Styles - Using higher specificity instead of !important */
html body,
html .sidebar,
html .sidebar-logo,
html .sidebar-nav,
html .sidebar-btn,
html .main-content {
  font-family: 'Amazon Ember', Arial, sans-serif;
}

body {
  margin: 0;
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: var(--theme-transition);
}

/* Ensure smooth transitions for background images and filters */
img {
  transition: var(--theme-transition);
}

/* Add specific transitions for elements that need them */
.sidebar-btn,
.theme-toggle-btn,
.progress-bar,
.metric-item {
  transition: var(--theme-transition);
}

/* Remove transitions from specific elements where needed */
.sidebar-collapse-btn .collapse-icon,
.sidebar.collapsed * {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global zero-mode enforcement: when an element has class zero, it must not inherit positive/negative coloring */
.zero {
  color: var(--text-secondary);
  opacity: 0.9;
  
  /* Override semantic color variables with neutral values */
  --color-positive: var(--text-secondary);
  --color-negative: var(--text-secondary);
  --color-increase: var(--text-secondary);
  --color-decrease: var(--text-secondary);
  --color-up: var(--text-secondary);
  --color-down: var(--text-secondary);
  --color-success: var(--text-secondary);
  --color-danger: var(--text-secondary);
  --color-rejected: var(--text-secondary);
}

/* Semantic color classes now use CSS variables for better maintainability */
.positive { color: var(--color-positive); }
.negative { color: var(--color-negative); }
.increase { color: var(--color-increase); }
.decrease { color: var(--color-decrease); }
.up { color: var(--color-up); }
.down { color: var(--color-down); }
.success { color: var(--color-success); }
.danger { color: var(--color-danger); }
.rejected { color: var(--color-rejected); }
