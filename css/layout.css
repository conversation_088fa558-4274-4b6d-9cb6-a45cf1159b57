/* Layout Components
 * Main layout structure including sidebar, main content, and grid systems
 */

/* Main Layout Container */
.main-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Layout */
.sidebar {
  width: 280px;
  background: var(--bg-primary);
  border-right: 1.5px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: var(--theme-transition);
  position: relative;
  z-index: var(--z-surface);
  transform: translateX(0);
}

.sidebar.collapsed {
  transform: translateX(-200px);
  width: 80px;
}

/* Main Content Area */
.main-content {
  flex: 1;
  background: var(--bg-secondary);
  padding: 24px;
  overflow-y: auto;
  transition: var(--theme-transition);
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Database Layout */
.database-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.database-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.database-header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.database-header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Base Grid Layout System - Responsive by default */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

/* Specific Grid Overrides - Only for exact column counts when needed */
.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Mobile Responsiveness - Simplified */
@media (max-width: 600px) {
  .main-content {
    padding: 16px;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
}

/* Flexbox Layout Utilities */
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  align-items: center;
  justify-content: space-around;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* Spacing Utilities */
.gap-4 { gap: 4px; }
.gap-8 { gap: 8px; }
.gap-12 { gap: 12px; }
.gap-16 { gap: 16px; }
.gap-24 { gap: 24px; }
.gap-32 { gap: 32px; }

.mt-4 { margin-top: 4px; }
.mt-8 { margin-top: 8px; }
.mt-12 { margin-top: 12px; }
.mt-16 { margin-top: 16px; }
.mt-24 { margin-top: 24px; }
.mt-32 { margin-top: 32px; }

.mb-4 { margin-bottom: 4px; }
.mb-8 { margin-bottom: 8px; }
.mb-12 { margin-bottom: 12px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }
.mb-32 { margin-bottom: 32px; }

.p-4 { padding: 4px; }
.p-8 { padding: 8px; }
.p-12 { padding: 12px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }
.p-32 { padding: 32px; }
