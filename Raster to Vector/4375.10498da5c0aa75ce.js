(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[4375], {
    14375: function(e, t, r) {
        "use strict";
        var n, a = r(48834).lW, o = r(34406), i = (n = (n = "undefined" != typeof document && document.currentScript ? document.currentScript.src : void 0) || "/index.js",
        function() {
            let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
            e.ready = new Promise( (e, t) => {
                i = e,
                s = t
            }
            ),
            ["_malloc", "_free", "_memory", "___indirect_function_table", "_fflush", "__embind_initialize_bindings", "onRuntimeInitialized"].forEach(t => {
                Object.getOwnPropertyDescriptor(e.ready, t) || Object.defineProperty(e.ready, t, {
                    get: () => en("You are getting " + t + " on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js"),
                    set: () => en("You are setting " + t + " on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")
                })
            }
            ),
            e._extraInitializations = e._extraInitializations || [],
            e._extraInitializations.push(function() {
                e.MakeSWCanvasSurface = function(t) {
                    var r = t
                      , n = "undefined" != typeof HTMLCanvasElement && r instanceof HTMLCanvasElement
                      , a = "undefined" != typeof OffscreenCanvas && r instanceof OffscreenCanvas;
                    if (!n && !a && !(r = document.getElementById(t)))
                        throw "Canvas with id " + t + " was not found";
                    var o = e.MakeSurface(r.width, r.height);
                    return o && (o._canvas = r),
                    o
                }
                ,
                e.MakeCanvasSurface || (e.MakeCanvasSurface = e.MakeSWCanvasSurface),
                e.MakeSurface = function(t, r) {
                    var n = {
                        width: t,
                        height: r,
                        colorType: e.ColorType.RGBA_8888,
                        alphaType: e.AlphaType.Unpremul,
                        colorSpace: e.ColorSpace.SRGB
                    }
                      , a = t * r * 4
                      , o = e._malloc(a)
                      , i = e.Surface._makeRasterDirect(n, o, 4 * t);
                    return i && (i._canvas = null,
                    i._width = t,
                    i._height = r,
                    i._pixelLen = a,
                    i._pixelPtr = o,
                    i.getCanvas().clear(e.TRANSPARENT)),
                    i
                }
                ,
                e.MakeRasterDirectSurface = function(t, r, n) {
                    return e.Surface._makeRasterDirect(t, r.byteOffset, n)
                }
                ,
                e.Surface.prototype.flush = function(t) {
                    if (e.setCurrentContext(this._context),
                    this._flush(),
                    this._canvas) {
                        var r = new ImageData(new Uint8ClampedArray(e.HEAPU8.buffer,this._pixelPtr,this._pixelLen),this._width,this._height);
                        t ? this._canvas.getContext("2d").putImageData(r, 0, 0, t[0], t[1], t[2] - t[0], t[3] - t[1]) : this._canvas.getContext("2d").putImageData(r, 0, 0)
                    }
                }
                ,
                e.Surface.prototype.dispose = function() {
                    this._pixelPtr && e._free(this._pixelPtr),
                    this.delete()
                }
                ,
                e.setCurrentContext = e.setCurrentContext || function() {}
                ,
                e.getCurrentGrDirectContext = e.getCurrentGrDirectContext || function() {
                    return null
                }
            }),
            e._extraInitializations = e._extraInitializations || [],
            e._extraInitializations.push(function() {
                function t(e, t, r) {
                    return e && e.hasOwnProperty(t) ? e[t] : r
                }
                function r(e) {
                    var t = tI.getNewId(tI.textures);
                    return tI.textures[t] = e,
                    t
                }
                function n(e) {
                    return e.naturalHeight || e.videoHeight || e.displayHeight || e.height
                }
                function a(e) {
                    return e.naturalWidth || e.videoWidth || e.displayWidth || e.width
                }
                function o(t, r, n, a) {
                    return t.bindTexture(t.TEXTURE_2D, r),
                    a || n.alphaType !== e.AlphaType.Premul || t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL, !0),
                    r
                }
                function i(t, r, n) {
                    n || r.alphaType !== e.AlphaType.Premul || t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL, !1),
                    t.bindTexture(t.TEXTURE_2D, null)
                }
                e.GetWebGLContext = function(e, r) {
                    if (!e)
                        throw "null canvas passed into makeWebGLContext";
                    var n = {
                        alpha: t(r, "alpha", 1),
                        depth: t(r, "depth", 1),
                        stencil: t(r, "stencil", 8),
                        antialias: t(r, "antialias", 0),
                        premultipliedAlpha: t(r, "premultipliedAlpha", 1),
                        preserveDrawingBuffer: t(r, "preserveDrawingBuffer", 0),
                        preferLowPowerToHighPerformance: t(r, "preferLowPowerToHighPerformance", 0),
                        failIfMajorPerformanceCaveat: t(r, "failIfMajorPerformanceCaveat", 0),
                        enableExtensionsByDefault: t(r, "enableExtensionsByDefault", 1),
                        explicitSwapControl: t(r, "explicitSwapControl", 0),
                        renderViaOffscreenBackBuffer: t(r, "renderViaOffscreenBackBuffer", 0)
                    };
                    if (r && r.majorVersion ? n.majorVersion = r.majorVersion : n.majorVersion = "undefined" != typeof WebGL2RenderingContext ? 2 : 1,
                    n.explicitSwapControl)
                        throw "explicitSwapControl is not supported";
                    var a = tI.createContext(e, n);
                    return a ? (tI.makeContextCurrent(a),
                    tI.currentContext.GLctx.getExtension("WEBGL_debug_renderer_info"),
                    a) : 0
                }
                ,
                e.deleteContext = function(e) {
                    tI.deleteContext(e)
                }
                ,
                e._setTextureCleanup({
                    deleteTexture: function(e, t) {
                        var r = tI.textures[t];
                        r && tI.getContext(e).GLctx.deleteTexture(r),
                        tI.textures[t] = null
                    }
                }),
                e.MakeWebGLContext = function(t) {
                    if (!this.setCurrentContext(t))
                        return null;
                    var r = this._MakeGrContext();
                    if (!r)
                        return null;
                    r._context = t;
                    var n = r.delete.bind(r);
                    return r.delete = (function() {
                        e.setCurrentContext(this._context),
                        n()
                    }
                    ).bind(r),
                    tI.currentContext.grDirectContext = r,
                    r
                }
                ,
                e.MakeGrContext = e.MakeWebGLContext,
                e.GrDirectContext.prototype.getResourceCacheLimitBytes = function() {
                    return e.setCurrentContext(this._context),
                    this._getResourceCacheLimitBytes()
                }
                ,
                e.GrDirectContext.prototype.getResourceCacheUsageBytes = function() {
                    return e.setCurrentContext(this._context),
                    this._getResourceCacheUsageBytes()
                }
                ,
                e.GrDirectContext.prototype.releaseResourcesAndAbandonContext = function() {
                    e.setCurrentContext(this._context),
                    this._releaseResourcesAndAbandonContext()
                }
                ,
                e.GrDirectContext.prototype.setResourceCacheLimitBytes = function(t) {
                    e.setCurrentContext(this._context),
                    this._setResourceCacheLimitBytes(t)
                }
                ,
                e.MakeOnScreenGLSurface = function(e, t, r, n, a, o) {
                    var i;
                    return this.setCurrentContext(e._context) && (i = void 0 === a || void 0 === o ? this._MakeOnScreenGLSurface(e, t, r, n) : this._MakeOnScreenGLSurface(e, t, r, n, a, o)) ? (i._context = e._context,
                    i) : null
                }
                ,
                e.MakeRenderTarget = function() {
                    var e, t = arguments[0];
                    if (!this.setCurrentContext(t._context))
                        return null;
                    if (3 == arguments.length) {
                        if (e = this._MakeRenderTargetWH(t, arguments[1], arguments[2]),
                        !e)
                            return null
                    } else if (2 != arguments.length)
                        return null;
                    else if (e = this._MakeRenderTargetII(t, arguments[1]),
                    !e)
                        return null;
                    return e._context = t._context,
                    e
                }
                ,
                e.MakeWebGLCanvasSurface = function(t, r, n) {
                    r = r || null;
                    var a = t
                      , o = "undefined" != typeof HTMLCanvasElement && a instanceof HTMLCanvasElement
                      , i = "undefined" != typeof OffscreenCanvas && a instanceof OffscreenCanvas;
                    if (!o && !i && !(a = document.getElementById(t)))
                        throw "Canvas with id " + t + " was not found";
                    var s = this.GetWebGLContext(a, n);
                    if (!s || s < 0)
                        throw "failed to create webgl context: err " + s;
                    var l = this.MakeWebGLContext(s)
                      , c = this.MakeOnScreenGLSurface(l, a.width, a.height, r);
                    if (!c) {
                        var u = a.cloneNode(!0);
                        return a.parentNode.replaceChild(u, a),
                        u.classList.add("ck-replaced"),
                        e.MakeSWCanvasSurface(u)
                    }
                    return c
                }
                ,
                e.MakeCanvasSurface = e.MakeWebGLCanvasSurface,
                e.Surface.prototype.makeImageFromTexture = function(t, n) {
                    e.setCurrentContext(this._context);
                    var a = r(t)
                      , o = this._makeImageFromTexture(this._context, a, n);
                    return o && (o._tex = a),
                    o
                }
                ,
                e.Surface.prototype.makeImageFromTextureSource = function(t, r, s) {
                    r || (r = {
                        height: n(t),
                        width: a(t),
                        colorType: e.ColorType.RGBA_8888,
                        alphaType: s ? e.AlphaType.Premul : e.AlphaType.Unpremul
                    }),
                    r.colorSpace || (r.colorSpace = e.ColorSpace.SRGB),
                    r.colorType,
                    e.ColorType.RGBA_8888,
                    e.setCurrentContext(this._context);
                    var l = tI.currentContext.GLctx
                      , c = o(l, l.createTexture(), r, s);
                    return 2 === tI.currentContext.version ? l.texImage2D(l.TEXTURE_2D, 0, l.RGBA, r.width, r.height, 0, l.RGBA, l.UNSIGNED_BYTE, t) : l.texImage2D(l.TEXTURE_2D, 0, l.RGBA, l.RGBA, l.UNSIGNED_BYTE, t),
                    i(l, r),
                    this._resetContext(),
                    this.makeImageFromTexture(c, r)
                }
                ,
                e.Surface.prototype.updateTextureFromSource = function(t, s, l) {
                    if (t._tex) {
                        e.setCurrentContext(this._context);
                        var c = t.getImageInfo()
                          , u = tI.currentContext.GLctx
                          , f = o(u, tI.textures[t._tex], c, l);
                        2 === tI.currentContext.version ? u.texImage2D(u.TEXTURE_2D, 0, u.RGBA, a(s), n(s), 0, u.RGBA, u.UNSIGNED_BYTE, s) : u.texImage2D(u.TEXTURE_2D, 0, u.RGBA, u.RGBA, u.UNSIGNED_BYTE, s),
                        i(u, c, l),
                        this._resetContext(),
                        tI.textures[t._tex] = null,
                        t._tex = r(f),
                        c.colorSpace = t.getColorSpace();
                        var h = this._makeImageFromTexture(this._context, t._tex, c)
                          , d = t.$$.ptr
                          , p = t.$$.smartPtr;
                        t.$$.ptr = h.$$.ptr,
                        t.$$.smartPtr = h.$$.smartPtr,
                        h.$$.ptr = d,
                        h.$$.smartPtr = p,
                        h.delete(),
                        c.colorSpace.delete()
                    }
                }
                ,
                e.MakeLazyImageFromTextureSource = function(t, s, l) {
                    s || (s = {
                        height: n(t),
                        width: a(t),
                        colorType: e.ColorType.RGBA_8888,
                        alphaType: l ? e.AlphaType.Premul : e.AlphaType.Unpremul
                    }),
                    s.colorSpace || (s.colorSpace = e.ColorSpace.SRGB),
                    s.colorType,
                    e.ColorType.RGBA_8888;
                    var c = {
                        makeTexture: function() {
                            var e = tI.currentContext
                              , n = e.GLctx
                              , a = o(n, n.createTexture(), s, l);
                            return 2 === e.version ? n.texImage2D(n.TEXTURE_2D, 0, n.RGBA, s.width, s.height, 0, n.RGBA, n.UNSIGNED_BYTE, t) : n.texImage2D(n.TEXTURE_2D, 0, n.RGBA, n.RGBA, n.UNSIGNED_BYTE, t),
                            i(n, s, l),
                            r(a)
                        },
                        freeSrc: function() {}
                    };
                    return "VideoFrame" === t.constructor.name && (c.freeSrc = function() {
                        t.close()
                    }
                    ),
                    e.Image._makeFromGenerator(s, c)
                }
                ,
                e.setCurrentContext = function(e) {
                    return !!e && tI.makeContextCurrent(e)
                }
                ,
                e.getCurrentGrDirectContext = function() {
                    return tI.currentContext && tI.currentContext.grDirectContext && !tI.currentContext.grDirectContext.isDeleted() ? tI.currentContext.grDirectContext : null
                }
            }),
            function(t) {
                function r(e) {
                    return !!e && e.constructor === Float32Array && 4 === e.length
                }
                function n(e) {
                    return (s(255 * e[3]) << 24 | s(255 * e[0]) << 16 | s(255 * e[1]) << 8 | s(255 * e[2]) << 0) >>> 0
                }
                function o(e) {
                    if (c(e))
                        return e;
                    if (e instanceof Float32Array) {
                        for (var t = Math.floor(e.length / 4), r = new Uint32Array(t), a = 0; a < t; a++)
                            r[a] = n(e.slice(4 * a, (a + 1) * 4));
                        return r
                    }
                    return e instanceof Uint32Array ? e : e instanceof Array && e[0]instanceof Float32Array ? e.map(n) : void 0
                }
                function i(e) {
                    if (void 0 === e)
                        return 1;
                    var t = parseFloat(e);
                    return e && -1 !== e.indexOf("%") ? t / 100 : t
                }
                function s(e) {
                    return Math.round(Math.max(0, Math.min(e || 0, 255)))
                }
                function l(e, r) {
                    c(r) || t._free(e)
                }
                function c(e) {
                    return e && e._ck
                }
                t.Color = function(e, r, n, a) {
                    return void 0 === a && (a = 1),
                    t.Color4f(s(e) / 255, s(r) / 255, s(n) / 255, a)
                }
                ,
                t.ColorAsInt = function(e, t, r, n) {
                    return void 0 === n && (n = 255),
                    (s(n) << 24 | s(e) << 16 | s(t) << 8 | s(r) << 0 & 268435455) >>> 0
                }
                ,
                t.Color4f = function(e, t, r, n) {
                    return void 0 === n && (n = 1),
                    Float32Array.of(e, t, r, n)
                }
                ,
                Object.defineProperty(t, "TRANSPARENT", {
                    get: function() {
                        return t.Color4f(0, 0, 0, 0)
                    }
                }),
                Object.defineProperty(t, "BLACK", {
                    get: function() {
                        return t.Color4f(0, 0, 0, 1)
                    }
                }),
                Object.defineProperty(t, "WHITE", {
                    get: function() {
                        return t.Color4f(1, 1, 1, 1)
                    }
                }),
                Object.defineProperty(t, "RED", {
                    get: function() {
                        return t.Color4f(1, 0, 0, 1)
                    }
                }),
                Object.defineProperty(t, "GREEN", {
                    get: function() {
                        return t.Color4f(0, 1, 0, 1)
                    }
                }),
                Object.defineProperty(t, "BLUE", {
                    get: function() {
                        return t.Color4f(0, 0, 1, 1)
                    }
                }),
                Object.defineProperty(t, "YELLOW", {
                    get: function() {
                        return t.Color4f(1, 1, 0, 1)
                    }
                }),
                Object.defineProperty(t, "CYAN", {
                    get: function() {
                        return t.Color4f(0, 1, 1, 1)
                    }
                }),
                Object.defineProperty(t, "MAGENTA", {
                    get: function() {
                        return t.Color4f(1, 0, 1, 1)
                    }
                }),
                t.getColorComponents = function(e) {
                    return [Math.floor(255 * e[0]), Math.floor(255 * e[1]), Math.floor(255 * e[2]), e[3]]
                }
                ,
                t.parseColorString = function(e, r) {
                    if ((e = e.toLowerCase()).startsWith("#")) {
                        var n, a, o, s = 255;
                        switch (e.length) {
                        case 9:
                            s = parseInt(e.slice(7, 9), 16);
                        case 7:
                            n = parseInt(e.slice(1, 3), 16),
                            a = parseInt(e.slice(3, 5), 16),
                            o = parseInt(e.slice(5, 7), 16);
                            break;
                        case 5:
                            s = 17 * parseInt(e.slice(4, 5), 16);
                        case 4:
                            n = 17 * parseInt(e.slice(1, 2), 16),
                            a = 17 * parseInt(e.slice(2, 3), 16),
                            o = 17 * parseInt(e.slice(3, 4), 16)
                        }
                        return t.Color(n, a, o, s / 255)
                    }
                    if (e.startsWith("rgba")) {
                        var l = (e = e.slice(5, -1)).split(",");
                        return t.Color(+l[0], +l[1], +l[2], i(l[3]))
                    }
                    if (e.startsWith("rgb")) {
                        var l = (e = e.slice(4, -1)).split(",");
                        return t.Color(+l[0], +l[1], +l[2], i(l[3]))
                    }
                    if (e.startsWith("gray("))
                        ;
                    else if (e.startsWith("hsl"))
                        ;
                    else if (r) {
                        var c = r[e];
                        if (void 0 !== c)
                            return c
                    }
                    return t.BLACK
                }
                ,
                t.multiplyByAlpha = function(e, t) {
                    var r = e.slice();
                    return r[3] = Math.max(0, Math.min(r[3] * t, 1)),
                    r
                }
                ,
                t.Malloc = function(e, r) {
                    var n = r * e.BYTES_PER_ELEMENT
                      , a = t._malloc(n);
                    return {
                        _ck: !0,
                        length: r,
                        byteOffset: a,
                        typedArray: null,
                        subarray: function(e, t) {
                            var r = this.toTypedArray().subarray(e, t);
                            return r._ck = !0,
                            r
                        },
                        toTypedArray: function() {
                            return this.typedArray && this.typedArray.length || (this.typedArray = new e(t.HEAPU8.buffer,a,r),
                            this.typedArray._ck = !0),
                            this.typedArray
                        }
                    }
                }
                ,
                t.Free = function(e) {
                    t._free(e.byteOffset),
                    e.byteOffset = D,
                    e.toTypedArray = null,
                    e.typedArray = null
                }
                ;
                var u, f, h, d, p, g, m = D, y = D, _ = D, v = D, b = D, C = D, A = D, w = D, x = D, T = D;
                function P(e, r, n) {
                    if (!e || !e.length)
                        return D;
                    if (c(e))
                        return e.byteOffset;
                    var a = t[r].BYTES_PER_ELEMENT;
                    return n || (n = t._malloc(e.length * a)),
                    t[r].set(e, n / a),
                    n
                }
                function S(e) {
                    var r = {
                        colorPtr: D,
                        count: e.length,
                        colorType: t.ColorType.RGBA_F32
                    };
                    if (e instanceof Float32Array)
                        r.colorPtr = P(e, "HEAPF32"),
                        r.count = e.length / 4;
                    else if (e instanceof Uint32Array)
                        r.colorPtr = P(e, "HEAPU32"),
                        r.colorType = t.ColorType.RGBA_8888;
                    else if (e instanceof Array)
                        r.colorPtr = function(e) {
                            if (!e || !e.length)
                                return D;
                            for (var r = t._malloc(16 * e.length), n = 0, a = r / 4, o = 0; o < e.length; o++)
                                for (var i = 0; i < 4; i++)
                                    t.HEAPF32[a + n] = e[o][i],
                                    n++;
                            return r
                        }(e);
                    else
                        throw "Invalid argument to copyFlexibleColorArray, Not a color array " + typeof e;
                    return r
                }
                var F = Float32Array.of(0, 0, 1);
                function k(e) {
                    if (!e)
                        return D;
                    var r = u.toTypedArray();
                    if (e.length) {
                        if (6 === e.length || 9 === e.length)
                            return P(e, "HEAPF32", m),
                            6 === e.length && t.HEAPF32.set(F, 6 + m / 4),
                            m;
                        if (16 === e.length)
                            return r[0] = e[0],
                            r[1] = e[1],
                            r[2] = e[3],
                            r[3] = e[4],
                            r[4] = e[5],
                            r[5] = e[7],
                            r[6] = e[12],
                            r[7] = e[13],
                            r[8] = e[15],
                            m;
                        throw "invalid matrix size"
                    }
                    if (void 0 === e.m11)
                        throw "invalid matrix argument";
                    return r[0] = e.m11,
                    r[1] = e.m21,
                    r[2] = e.m41,
                    r[3] = e.m12,
                    r[4] = e.m22,
                    r[5] = e.m42,
                    r[6] = e.m14,
                    r[7] = e.m24,
                    r[8] = e.m44,
                    m
                }
                function E(e, t) {
                    return P(e, "HEAPF32", t || _)
                }
                function M(e, t, r, n) {
                    var a = h.toTypedArray();
                    return a[0] = e,
                    a[1] = t,
                    a[2] = r,
                    a[3] = n,
                    _
                }
                function I(e) {
                    return P(e, "HEAPF32")
                }
                function B(e) {
                    for (var r = new Float32Array(4), n = 0; n < 4; n++)
                        r[n] = t.HEAPF32[e / 4 + n];
                    return r
                }
                function R(e, t) {
                    return P(e, "HEAPF32", t || v)
                }
                function L(e, t) {
                    return P(e, "HEAP32", t || w)
                }
                function O(e, t) {
                    return P(e, "HEAPF32", t || x)
                }
                var D = 0;
                function U(e) {
                    return e / Math.PI * 180
                }
                t.onRuntimeInitialized = function() {
                    function e(e, r, n, a, o, i, s) {
                        i || (i = 4 * a.width,
                        a.colorType === t.ColorType.RGBA_F16 ? i *= 2 : a.colorType !== t.ColorType.RGBA_F32 || (i *= 4));
                        var l, c = i * a.height;
                        if (l = o ? o.byteOffset : t._malloc(c),
                        !(s ? e._readPixels(a, l, i, r, n, s) : e._readPixels(a, l, i, r, n)))
                            return o || t._free(l),
                            null;
                        if (o)
                            return o.toTypedArray();
                        var u = null;
                        switch (a.colorType) {
                        case t.ColorType.RGBA_8888:
                        case t.ColorType.RGBA_F16:
                            u = new Uint8Array(t.HEAPU8.buffer,l,c).slice();
                            break;
                        case t.ColorType.RGBA_F32:
                            u = new Float32Array(t.HEAPU8.buffer,l,c).slice();
                            break;
                        default:
                            return null
                        }
                        return t._free(l),
                        u
                    }
                    _ = (h = t.Malloc(Float32Array, 4)).byteOffset,
                    y = (f = t.Malloc(Float32Array, 16)).byteOffset,
                    m = (u = t.Malloc(Float32Array, 9)).byteOffset,
                    x = t.Malloc(Float32Array, 12).byteOffset,
                    T = t.Malloc(Float32Array, 12).byteOffset,
                    v = (d = t.Malloc(Float32Array, 4)).byteOffset,
                    b = t.Malloc(Float32Array, 4).byteOffset,
                    C = (p = t.Malloc(Float32Array, 3)).byteOffset,
                    A = t.Malloc(Float32Array, 3).byteOffset,
                    w = (g = t.Malloc(Int32Array, 4)).byteOffset,
                    t.ColorSpace.SRGB = t.ColorSpace._MakeSRGB(),
                    t.ColorSpace.DISPLAY_P3 = t.ColorSpace._MakeDisplayP3(),
                    t.ColorSpace.ADOBE_RGB = t.ColorSpace._MakeAdobeRGB(),
                    t.GlyphRunFlags = {
                        IsWhiteSpace: t._GlyphRunFlags_isWhiteSpace
                    },
                    t.Path.MakeFromCmds = function(e) {
                        var r = P(e, "HEAPF32")
                          , n = t.Path._MakeFromCmds(r, e.length);
                        return l(r, e),
                        n
                    }
                    ,
                    t.Path.MakeFromVerbsPointsWeights = function(e, r, n) {
                        var a = P(e, "HEAPU8")
                          , o = P(r, "HEAPF32")
                          , i = P(n, "HEAPF32")
                          , s = n && n.length || 0
                          , c = t.Path._MakeFromVerbsPointsWeights(a, e.length, o, r.length, i, s);
                        return l(a, e),
                        l(o, r),
                        l(i, n),
                        c
                    }
                    ,
                    t.Path.prototype.addArc = function(e, t, r) {
                        var n = R(e);
                        return this._addArc(n, t, r),
                        this
                    }
                    ,
                    t.Path.prototype.addCircle = function(e, t, r, n) {
                        return this._addCircle(e, t, r, !!n),
                        this
                    }
                    ,
                    t.Path.prototype.addOval = function(e, t, r) {
                        void 0 === r && (r = 1);
                        var n = R(e);
                        return this._addOval(n, !!t, r),
                        this
                    }
                    ,
                    t.Path.prototype.addPath = function() {
                        var e = Array.prototype.slice.call(arguments)
                          , t = e[0]
                          , r = !1;
                        if ("boolean" == typeof e[e.length - 1] && (r = e.pop()),
                        1 === e.length)
                            this._addPath(t, 1, 0, 0, 0, 1, 0, 0, 0, 1, r);
                        else if (2 === e.length) {
                            var n = e[1];
                            this._addPath(t, n[0], n[1], n[2], n[3], n[4], n[5], n[6] || 0, n[7] || 0, n[8] || 1, r)
                        } else {
                            if (7 !== e.length && 10 !== e.length)
                                return e.length,
                                null;
                            var n = e;
                            this._addPath(t, n[1], n[2], n[3], n[4], n[5], n[6], n[7] || 0, n[8] || 0, n[9] || 1, r)
                        }
                        return this
                    }
                    ,
                    t.Path.prototype.addPoly = function(e, t) {
                        var r = P(e, "HEAPF32");
                        return this._addPoly(r, e.length / 2, t),
                        l(r, e),
                        this
                    }
                    ,
                    t.Path.prototype.addRect = function(e, t) {
                        var r = R(e);
                        return this._addRect(r, !!t),
                        this
                    }
                    ,
                    t.Path.prototype.addRRect = function(e, t) {
                        var r = O(e);
                        return this._addRRect(r, !!t),
                        this
                    }
                    ,
                    t.Path.prototype.addVerbsPointsWeights = function(e, t, r) {
                        var n = P(e, "HEAPU8")
                          , a = P(t, "HEAPF32")
                          , o = P(r, "HEAPF32")
                          , i = r && r.length || 0;
                        this._addVerbsPointsWeights(n, e.length, a, t.length, o, i),
                        l(n, e),
                        l(a, t),
                        l(o, r)
                    }
                    ,
                    t.Path.prototype.arc = function(e, r, n, a, o, i) {
                        var s = t.LTRBRect(e - n, r - n, e + n, r + n)
                          , l = U(o - a) - 360 * !!i
                          , c = new t.Path;
                        return c.addArc(s, U(a), l),
                        this.addPath(c, !0),
                        c.delete(),
                        this
                    }
                    ,
                    t.Path.prototype.arcToOval = function(e, t, r, n) {
                        var a = R(e);
                        return this._arcToOval(a, t, r, n),
                        this
                    }
                    ,
                    t.Path.prototype.arcToRotated = function(e, t, r, n, a, o, i) {
                        return this._arcToRotated(e, t, r, !!n, !!a, o, i),
                        this
                    }
                    ,
                    t.Path.prototype.arcToTangent = function(e, t, r, n, a) {
                        return this._arcToTangent(e, t, r, n, a),
                        this
                    }
                    ,
                    t.Path.prototype.close = function() {
                        return this._close(),
                        this
                    }
                    ,
                    t.Path.prototype.conicTo = function(e, t, r, n, a) {
                        return this._conicTo(e, t, r, n, a),
                        this
                    }
                    ,
                    t.Path.prototype.computeTightBounds = function(e) {
                        this._computeTightBounds(v);
                        var t = d.toTypedArray();
                        return e ? (e.set(t),
                        e) : t.slice()
                    }
                    ,
                    t.Path.prototype.cubicTo = function(e, t, r, n, a, o) {
                        return this._cubicTo(e, t, r, n, a, o),
                        this
                    }
                    ,
                    t.Path.prototype.dash = function(e, t, r) {
                        return this._dash(e, t, r) ? this : null
                    }
                    ,
                    t.Path.prototype.getBounds = function(e) {
                        this._getBounds(v);
                        var t = d.toTypedArray();
                        return e ? (e.set(t),
                        e) : t.slice()
                    }
                    ,
                    t.Path.prototype.lineTo = function(e, t) {
                        return this._lineTo(e, t),
                        this
                    }
                    ,
                    t.Path.prototype.moveTo = function(e, t) {
                        return this._moveTo(e, t),
                        this
                    }
                    ,
                    t.Path.prototype.offset = function(e, t) {
                        return this._transform(1, 0, e, 0, 1, t, 0, 0, 1),
                        this
                    }
                    ,
                    t.Path.prototype.quadTo = function(e, t, r, n) {
                        return this._quadTo(e, t, r, n),
                        this
                    }
                    ,
                    t.Path.prototype.rArcTo = function(e, t, r, n, a, o, i) {
                        return this._rArcTo(e, t, r, n, a, o, i),
                        this
                    }
                    ,
                    t.Path.prototype.rConicTo = function(e, t, r, n, a) {
                        return this._rConicTo(e, t, r, n, a),
                        this
                    }
                    ,
                    t.Path.prototype.rCubicTo = function(e, t, r, n, a, o) {
                        return this._rCubicTo(e, t, r, n, a, o),
                        this
                    }
                    ,
                    t.Path.prototype.rLineTo = function(e, t) {
                        return this._rLineTo(e, t),
                        this
                    }
                    ,
                    t.Path.prototype.rMoveTo = function(e, t) {
                        return this._rMoveTo(e, t),
                        this
                    }
                    ,
                    t.Path.prototype.rQuadTo = function(e, t, r, n) {
                        return this._rQuadTo(e, t, r, n),
                        this
                    }
                    ,
                    t.Path.prototype.stroke = function(e) {
                        return ((e = e || {}).width = e.width || 1,
                        e.miter_limit = e.miter_limit || 4,
                        e.cap = e.cap || t.StrokeCap.Butt,
                        e.join = e.join || t.StrokeJoin.Miter,
                        e.precision = e.precision || 1,
                        this._stroke(e)) ? this : null
                    }
                    ,
                    t.Path.prototype.transform = function() {
                        if (1 == arguments.length) {
                            var e = arguments[0];
                            this._transform(e[0], e[1], e[2], e[3], e[4], e[5], e[6] || 0, e[7] || 0, e[8] || 1)
                        } else if (6 == arguments.length || 9 == arguments.length) {
                            var e = arguments;
                            this._transform(e[0], e[1], e[2], e[3], e[4], e[5], e[6] || 0, e[7] || 0, e[8] || 1)
                        } else
                            throw "transform expected to take 1 or 9 arguments. Got " + arguments.length;
                        return this
                    }
                    ,
                    t.Path.prototype.trim = function(e, t, r) {
                        return this._trim(e, t, !!r) ? this : null
                    }
                    ,
                    t.Image.prototype.encodeToBytes = function(e, r) {
                        var n = t.getCurrentGrDirectContext();
                        return (e = e || t.ImageFormat.PNG,
                        r = r || 100,
                        n) ? this._encodeToBytes(e, r, n) : this._encodeToBytes(e, r)
                    }
                    ,
                    t.Image.prototype.makeShaderCubic = function(e, t, r, n, a) {
                        var o = k(a);
                        return this._makeShaderCubic(e, t, r, n, o)
                    }
                    ,
                    t.Image.prototype.makeShaderOptions = function(e, t, r, n, a) {
                        var o = k(a);
                        return this._makeShaderOptions(e, t, r, n, o)
                    }
                    ,
                    t.Image.prototype.readPixels = function(r, n, a, o, i) {
                        return e(this, r, n, a, o, i, t.getCurrentGrDirectContext())
                    }
                    ,
                    t.Canvas.prototype.clear = function(e) {
                        t.setCurrentContext(this._context);
                        var r = E(e);
                        this._clear(r)
                    }
                    ,
                    t.Canvas.prototype.clipRRect = function(e, r, n) {
                        t.setCurrentContext(this._context);
                        var a = O(e);
                        this._clipRRect(a, r, n)
                    }
                    ,
                    t.Canvas.prototype.clipRect = function(e, r, n) {
                        t.setCurrentContext(this._context);
                        var a = R(e);
                        this._clipRect(a, r, n)
                    }
                    ,
                    t.Canvas.prototype.concat = function(e) {
                        t.setCurrentContext(this._context);
                        var r = function(e) {
                            if (!e)
                                return D;
                            var t = f.toTypedArray();
                            if (e.length) {
                                if (16 !== e.length && 6 !== e.length && 9 !== e.length)
                                    throw "invalid matrix size";
                                return 16 === e.length ? P(e, "HEAPF32", y) : (t.fill(0),
                                t[0] = e[0],
                                t[1] = e[1],
                                t[3] = e[2],
                                t[4] = e[3],
                                t[5] = e[4],
                                t[7] = e[5],
                                t[10] = 1,
                                t[12] = e[6],
                                t[13] = e[7],
                                t[15] = e[8],
                                6 === e.length && (t[12] = 0,
                                t[13] = 0,
                                t[15] = 1),
                                y)
                            }
                            if (void 0 === e.m11)
                                throw "invalid matrix argument";
                            return t[0] = e.m11,
                            t[1] = e.m21,
                            t[2] = e.m31,
                            t[3] = e.m41,
                            t[4] = e.m12,
                            t[5] = e.m22,
                            t[6] = e.m32,
                            t[7] = e.m42,
                            t[8] = e.m13,
                            t[9] = e.m23,
                            t[10] = e.m33,
                            t[11] = e.m43,
                            t[12] = e.m14,
                            t[13] = e.m24,
                            t[14] = e.m34,
                            t[15] = e.m44,
                            y
                        }(e);
                        this._concat(r)
                    }
                    ,
                    t.Canvas.prototype.drawArc = function(e, r, n, a, o) {
                        t.setCurrentContext(this._context);
                        var i = R(e);
                        this._drawArc(i, r, n, a, o)
                    }
                    ,
                    t.Canvas.prototype.drawAtlas = function(e, r, n, a, i, s, c) {
                        if (e && a && r && n && r.length === n.length) {
                            t.setCurrentContext(this._context),
                            i || (i = t.BlendMode.SrcOver);
                            var u = P(r, "HEAPF32")
                              , f = P(n, "HEAPF32")
                              , h = n.length / 4
                              , d = P(o(s), "HEAPU32");
                            if (c && "B"in c && "C"in c)
                                this._drawAtlasCubic(e, f, u, d, h, i, c.B, c.C, a);
                            else {
                                let r = t.FilterMode.Linear
                                  , n = t.MipmapMode.None;
                                c && (r = c.filter,
                                "mipmap"in c && (n = c.mipmap)),
                                this._drawAtlasOptions(e, f, u, d, h, i, r, n, a)
                            }
                            l(u, r),
                            l(f, n),
                            l(d, s)
                        }
                    }
                    ,
                    t.Canvas.prototype.drawCircle = function(e, r, n, a) {
                        t.setCurrentContext(this._context),
                        this._drawCircle(e, r, n, a)
                    }
                    ,
                    t.Canvas.prototype.drawColor = function(e, r) {
                        t.setCurrentContext(this._context);
                        var n = E(e);
                        void 0 !== r ? this._drawColor(n, r) : this._drawColor(n)
                    }
                    ,
                    t.Canvas.prototype.drawColorInt = function(e, r) {
                        t.setCurrentContext(this._context),
                        this._drawColorInt(e, r || t.BlendMode.SrcOver)
                    }
                    ,
                    t.Canvas.prototype.drawColorComponents = function(e, r, n, a, o) {
                        t.setCurrentContext(this._context);
                        var i = M(e, r, n, a);
                        void 0 !== o ? this._drawColor(i, o) : this._drawColor(i)
                    }
                    ,
                    t.Canvas.prototype.drawDRRect = function(e, r, n) {
                        t.setCurrentContext(this._context);
                        var a = O(e, x)
                          , o = O(r, T);
                        this._drawDRRect(a, o, n)
                    }
                    ,
                    t.Canvas.prototype.drawImage = function(e, r, n, a) {
                        t.setCurrentContext(this._context),
                        this._drawImage(e, r, n, a || null)
                    }
                    ,
                    t.Canvas.prototype.drawImageCubic = function(e, r, n, a, o, i) {
                        t.setCurrentContext(this._context),
                        this._drawImageCubic(e, r, n, a, o, i || null)
                    }
                    ,
                    t.Canvas.prototype.drawImageOptions = function(e, r, n, a, o, i) {
                        t.setCurrentContext(this._context),
                        this._drawImageOptions(e, r, n, a, o, i || null)
                    }
                    ,
                    t.Canvas.prototype.drawImageNine = function(e, r, n, a, o) {
                        t.setCurrentContext(this._context);
                        var i = L(r)
                          , s = R(n);
                        this._drawImageNine(e, i, s, a, o || null)
                    }
                    ,
                    t.Canvas.prototype.drawImageRect = function(e, r, n, a, o) {
                        t.setCurrentContext(this._context),
                        R(r, v),
                        R(n, b),
                        this._drawImageRect(e, v, b, a, !!o)
                    }
                    ,
                    t.Canvas.prototype.drawImageRectCubic = function(e, r, n, a, o, i) {
                        t.setCurrentContext(this._context),
                        R(r, v),
                        R(n, b),
                        this._drawImageRectCubic(e, v, b, a, o, i || null)
                    }
                    ,
                    t.Canvas.prototype.drawImageRectOptions = function(e, r, n, a, o, i) {
                        t.setCurrentContext(this._context),
                        R(r, v),
                        R(n, b),
                        this._drawImageRectOptions(e, v, b, a, o, i || null)
                    }
                    ,
                    t.Canvas.prototype.drawLine = function(e, r, n, a, o) {
                        t.setCurrentContext(this._context),
                        this._drawLine(e, r, n, a, o)
                    }
                    ,
                    t.Canvas.prototype.drawOval = function(e, r) {
                        t.setCurrentContext(this._context);
                        var n = R(e);
                        this._drawOval(n, r)
                    }
                    ,
                    t.Canvas.prototype.drawPaint = function(e) {
                        t.setCurrentContext(this._context),
                        this._drawPaint(e)
                    }
                    ,
                    t.Canvas.prototype.drawParagraph = function(e, r, n) {
                        t.setCurrentContext(this._context),
                        this._drawParagraph(e, r, n)
                    }
                    ,
                    t.Canvas.prototype.drawPatch = function(e, r, n, a, i) {
                        if (e.length < 24)
                            throw "Need 12 cubic points";
                        if (r && r.length < 4)
                            throw "Need 4 colors";
                        if (n && n.length < 8)
                            throw "Need 4 shader coordinates";
                        t.setCurrentContext(this._context);
                        let s = P(e, "HEAPF32")
                          , c = r ? P(o(r), "HEAPU32") : D
                          , u = n ? P(n, "HEAPF32") : D;
                        a || (a = t.BlendMode.Modulate),
                        this._drawPatch(s, c, u, a, i),
                        l(u, n),
                        l(c, r),
                        l(s, e)
                    }
                    ,
                    t.Canvas.prototype.drawPath = function(e, r) {
                        t.setCurrentContext(this._context),
                        this._drawPath(e, r)
                    }
                    ,
                    t.Canvas.prototype.drawPicture = function(e) {
                        t.setCurrentContext(this._context),
                        this._drawPicture(e)
                    }
                    ,
                    t.Canvas.prototype.drawPoints = function(e, r, n) {
                        t.setCurrentContext(this._context);
                        var a = P(r, "HEAPF32");
                        this._drawPoints(e, a, r.length / 2, n),
                        l(a, r)
                    }
                    ,
                    t.Canvas.prototype.drawRRect = function(e, r) {
                        t.setCurrentContext(this._context);
                        var n = O(e);
                        this._drawRRect(n, r)
                    }
                    ,
                    t.Canvas.prototype.drawRect = function(e, r) {
                        t.setCurrentContext(this._context);
                        var n = R(e);
                        this._drawRect(n, r)
                    }
                    ,
                    t.Canvas.prototype.drawRect4f = function(e, r, n, a, o) {
                        t.setCurrentContext(this._context),
                        this._drawRect4f(e, r, n, a, o)
                    }
                    ,
                    t.Canvas.prototype.drawShadow = function(e, r, n, a, o, i, s) {
                        t.setCurrentContext(this._context);
                        var c = I(o)
                          , u = I(i)
                          , f = P(r, "HEAPF32", C)
                          , h = P(n, "HEAPF32", A);
                        this._drawShadow(e, f, h, a, c, u, s),
                        l(c, o),
                        l(u, i)
                    }
                    ,
                    t.getShadowLocalBounds = function(e, t, r, n, a, o, i) {
                        var s = k(e)
                          , l = P(r, "HEAPF32", C)
                          , c = P(n, "HEAPF32", A);
                        if (!this._getShadowLocalBounds(s, t, l, c, a, o, v))
                            return null;
                        var u = d.toTypedArray();
                        return i ? (i.set(u),
                        i) : u.slice()
                    }
                    ,
                    t.Canvas.prototype.drawTextBlob = function(e, r, n, a) {
                        t.setCurrentContext(this._context),
                        this._drawTextBlob(e, r, n, a)
                    }
                    ,
                    t.Canvas.prototype.drawVertices = function(e, r, n) {
                        t.setCurrentContext(this._context),
                        this._drawVertices(e, r, n)
                    }
                    ,
                    t.Canvas.prototype.getDeviceClipBounds = function(e) {
                        var t;
                        return this._getDeviceClipBounds(w),
                        t = g.toTypedArray(),
                        e ? (e.set(t),
                        e) : t.slice()
                    }
                    ,
                    t.Canvas.prototype.getLocalToDevice = function() {
                        return this._getLocalToDevice(y),
                        function(e) {
                            for (var r = Array(16), n = 0; n < 16; n++)
                                r[n] = t.HEAPF32[e / 4 + n];
                            return r
                        }(y)
                    }
                    ,
                    t.Canvas.prototype.getTotalMatrix = function() {
                        this._getTotalMatrix(m);
                        for (var e = Array(9), r = 0; r < 9; r++)
                            e[r] = t.HEAPF32[m / 4 + r];
                        return e
                    }
                    ,
                    t.Canvas.prototype.makeSurface = function(e) {
                        var t = this._makeSurface(e);
                        return t._context = this._context,
                        t
                    }
                    ,
                    t.Canvas.prototype.readPixels = function(r, n, a, o, i) {
                        return t.setCurrentContext(this._context),
                        e(this, r, n, a, o, i)
                    }
                    ,
                    t.Canvas.prototype.saveLayer = function(e, t, r, n) {
                        var a = R(t);
                        return this._saveLayer(e || null, a, r || null, n || 0)
                    }
                    ,
                    t.Canvas.prototype.writePixels = function(e, r, n, a, o, i, s, c) {
                        if (e.byteLength % (r * n))
                            throw "pixels length must be a multiple of the srcWidth * srcHeight";
                        t.setCurrentContext(this._context);
                        var u = e.byteLength / (r * n);
                        i = i || t.AlphaType.Unpremul,
                        s = s || t.ColorType.RGBA_8888,
                        c = c || t.ColorSpace.SRGB;
                        var f = P(e, "HEAPU8")
                          , h = this._writePixels({
                            width: r,
                            height: n,
                            colorType: s,
                            alphaType: i,
                            colorSpace: c
                        }, f, u * r, a, o);
                        return l(f, e),
                        h
                    }
                    ,
                    t.ColorFilter.MakeBlend = function(e, r, n) {
                        var a = E(e);
                        return n = n || t.ColorSpace.SRGB,
                        t.ColorFilter._MakeBlend(a, r, n)
                    }
                    ,
                    t.ColorFilter.MakeMatrix = function(e) {
                        if (!e || 20 !== e.length)
                            throw "invalid color matrix";
                        var r = P(e, "HEAPF32")
                          , n = t.ColorFilter._makeMatrix(r);
                        return l(r, e),
                        n
                    }
                    ,
                    t.ContourMeasure.prototype.getPosTan = function(e, t) {
                        this._getPosTan(e, v);
                        var r = d.toTypedArray();
                        return t ? (t.set(r),
                        t) : r.slice()
                    }
                    ,
                    t.ImageFilter.prototype.getOutputBounds = function(e, t, r) {
                        var n = R(e, v)
                          , a = k(t);
                        this._getOutputBounds(n, a, w);
                        var o = g.toTypedArray();
                        return r ? (r.set(o),
                        r) : o.slice()
                    }
                    ,
                    t.ImageFilter.MakeDropShadow = function(e, r, n, a, o, i) {
                        var s = E(o, _);
                        return t.ImageFilter._MakeDropShadow(e, r, n, a, s, i)
                    }
                    ,
                    t.ImageFilter.MakeDropShadowOnly = function(e, r, n, a, o, i) {
                        var s = E(o, _);
                        return t.ImageFilter._MakeDropShadowOnly(e, r, n, a, s, i)
                    }
                    ,
                    t.ImageFilter.MakeImage = function(e, r, n, a) {
                        var o = R(n, v)
                          , i = R(a, b);
                        if ("B"in r && "C"in r)
                            return t.ImageFilter._MakeImageCubic(e, r.B, r.C, o, i);
                        {
                            let n = r.filter
                              , a = t.MipmapMode.None;
                            return "mipmap"in r && (a = r.mipmap),
                            t.ImageFilter._MakeImageOptions(e, n, a, o, i)
                        }
                    }
                    ,
                    t.ImageFilter.MakeMatrixTransform = function(e, r, n) {
                        var a = k(e);
                        if ("B"in r && "C"in r)
                            return t.ImageFilter._MakeMatrixTransformCubic(a, r.B, r.C, n);
                        {
                            let e = r.filter
                              , o = t.MipmapMode.None;
                            return "mipmap"in r && (o = r.mipmap),
                            t.ImageFilter._MakeMatrixTransformOptions(a, e, o, n)
                        }
                    }
                    ,
                    t.Paint.prototype.getColor = function() {
                        return this._getColor(_),
                        B(_)
                    }
                    ,
                    t.Paint.prototype.setColor = function(e, t) {
                        t = t || null;
                        var r = E(e);
                        this._setColor(r, t)
                    }
                    ,
                    t.Paint.prototype.setColorComponents = function(e, t, r, n, a) {
                        a = a || null;
                        var o = M(e, t, r, n);
                        this._setColor(o, a)
                    }
                    ,
                    t.Path.prototype.getPoint = function(e, t) {
                        this._getPoint(e, v);
                        var r = d.toTypedArray();
                        return t ? (t[0] = r[0],
                        t[1] = r[1],
                        t) : r.slice(0, 2)
                    }
                    ,
                    t.Picture.prototype.makeShader = function(e, t, r, n, a) {
                        var o = k(n)
                          , i = R(a);
                        return this._makeShader(e, t, r, o, i)
                    }
                    ,
                    t.Picture.prototype.cullRect = function(e) {
                        this._cullRect(v);
                        var t = d.toTypedArray();
                        return e ? (e.set(t),
                        e) : t.slice()
                    }
                    ,
                    t.PictureRecorder.prototype.beginRecording = function(e, t) {
                        var r = R(e);
                        return this._beginRecording(r, !!t)
                    }
                    ,
                    t.Surface.prototype.getCanvas = function() {
                        var e = this._getCanvas();
                        return e._context = this._context,
                        e
                    }
                    ,
                    t.Surface.prototype.makeImageSnapshot = function(e) {
                        t.setCurrentContext(this._context);
                        var r = L(e);
                        return this._makeImageSnapshot(r)
                    }
                    ,
                    t.Surface.prototype.makeSurface = function(e) {
                        t.setCurrentContext(this._context);
                        var r = this._makeSurface(e);
                        return r._context = this._context,
                        r
                    }
                    ,
                    t.Surface.prototype._requestAnimationFrameInternal = function(e, r) {
                        return this._cached_canvas || (this._cached_canvas = this.getCanvas()),
                        requestAnimationFrame((function() {
                            t.setCurrentContext(this._context),
                            e(this._cached_canvas),
                            this.flush(r)
                        }
                        ).bind(this))
                    }
                    ,
                    t.Surface.prototype.requestAnimationFrame || (t.Surface.prototype.requestAnimationFrame = t.Surface.prototype._requestAnimationFrameInternal),
                    t.Surface.prototype._drawOnceInternal = function(e, r) {
                        this._cached_canvas || (this._cached_canvas = this.getCanvas()),
                        requestAnimationFrame((function() {
                            t.setCurrentContext(this._context),
                            e(this._cached_canvas),
                            this.flush(r),
                            this.dispose()
                        }
                        ).bind(this))
                    }
                    ,
                    t.Surface.prototype.drawOnce || (t.Surface.prototype.drawOnce = t.Surface.prototype._drawOnceInternal),
                    t.PathEffect.MakeDash = function(e, r) {
                        if (r || (r = 0),
                        !e.length || e.length % 2 == 1)
                            throw "Intervals array must have even length";
                        var n = P(e, "HEAPF32")
                          , a = t.PathEffect._MakeDash(n, e.length, r);
                        return l(n, e),
                        a
                    }
                    ,
                    t.PathEffect.MakeLine2D = function(e, r) {
                        var n = k(r);
                        return t.PathEffect._MakeLine2D(e, n)
                    }
                    ,
                    t.PathEffect.MakePath2D = function(e, r) {
                        var n = k(e);
                        return t.PathEffect._MakePath2D(n, r)
                    }
                    ,
                    t.Shader.MakeColor = function(e, r) {
                        r = r || null;
                        var n = E(e);
                        return t.Shader._MakeColor(n, r)
                    }
                    ,
                    t.Shader.Blend = t.Shader.MakeBlend,
                    t.Shader.Color = t.Shader.MakeColor,
                    t.Shader.MakeLinearGradient = function(e, r, n, a, o, i, s, c) {
                        c = c || null;
                        var u = S(n)
                          , f = P(a, "HEAPF32");
                        s = s || 0;
                        var h = k(i)
                          , p = d.toTypedArray();
                        p.set(e),
                        p.set(r, 2);
                        var g = t.Shader._MakeLinearGradient(v, u.colorPtr, u.colorType, f, u.count, o, s, h, c);
                        return l(u.colorPtr, n),
                        a && l(f, a),
                        g
                    }
                    ,
                    t.Shader.MakeRadialGradient = function(e, r, n, a, o, i, s, c) {
                        c = c || null;
                        var u = S(n)
                          , f = P(a, "HEAPF32");
                        s = s || 0;
                        var h = k(i)
                          , d = t.Shader._MakeRadialGradient(e[0], e[1], r, u.colorPtr, u.colorType, f, u.count, o, s, h, c);
                        return l(u.colorPtr, n),
                        a && l(f, a),
                        d
                    }
                    ,
                    t.Shader.MakeSweepGradient = function(e, r, n, a, o, i, s, c, u, f) {
                        f = f || null;
                        var h = S(n)
                          , d = P(a, "HEAPF32");
                        s = s || 0,
                        c = c || 0,
                        u = u || 360;
                        var p = k(i)
                          , g = t.Shader._MakeSweepGradient(e, r, h.colorPtr, h.colorType, d, h.count, o, c, u, s, p, f);
                        return l(h.colorPtr, n),
                        a && l(d, a),
                        g
                    }
                    ,
                    t.Shader.MakeTwoPointConicalGradient = function(e, r, n, a, o, i, s, c, u, f) {
                        f = f || null;
                        var h = S(o)
                          , p = P(i, "HEAPF32");
                        u = u || 0;
                        var g = k(c)
                          , m = d.toTypedArray();
                        m.set(e),
                        m.set(n, 2);
                        var y = t.Shader._MakeTwoPointConicalGradient(v, r, a, h.colorPtr, h.colorType, p, h.count, s, u, g, f);
                        return l(h.colorPtr, o),
                        i && l(p, i),
                        y
                    }
                    ,
                    t.Vertices.prototype.bounds = function(e) {
                        this._bounds(v);
                        var t = d.toTypedArray();
                        return e ? (e.set(t),
                        e) : t.slice()
                    }
                    ,
                    t._extraInitializations && t._extraInitializations.forEach(function(e) {
                        e()
                    })
                }
                ,
                t.computeTonalColors = function(e) {
                    var t = I(e.ambient)
                      , r = I(e.spot);
                    this._computeTonalColors(t, r);
                    var n = {
                        ambient: B(t),
                        spot: B(r)
                    };
                    return l(t, e.ambient),
                    l(r, e.spot),
                    n
                }
                ,
                t.LTRBRect = function(e, t, r, n) {
                    return Float32Array.of(e, t, r, n)
                }
                ,
                t.XYWHRect = function(e, t, r, n) {
                    return Float32Array.of(e, t, e + r, t + n)
                }
                ,
                t.LTRBiRect = function(e, t, r, n) {
                    return Int32Array.of(e, t, r, n)
                }
                ,
                t.XYWHiRect = function(e, t, r, n) {
                    return Int32Array.of(e, t, e + r, t + n)
                }
                ,
                t.RRectXY = function(e, t, r) {
                    return Float32Array.of(e[0], e[1], e[2], e[3], t, r, t, r, t, r, t, r)
                }
                ,
                t.MakeAnimatedImageFromEncoded = function(e) {
                    e = new Uint8Array(e);
                    var r = t._malloc(e.byteLength);
                    return t.HEAPU8.set(e, r),
                    t._decodeAnimatedImage(r, e.byteLength) || null
                }
                ,
                t.MakeImageFromEncoded = function(e) {
                    e = new Uint8Array(e);
                    var r = t._malloc(e.byteLength);
                    return t.HEAPU8.set(e, r),
                    t._decodeImage(r, e.byteLength) || null
                }
                ;
                var G = null;
                function W() {
                    for (var e = 0, t = 0; t < arguments.length - 1; t += 2)
                        e += arguments[t] * arguments[t + 1];
                    return e
                }
                t.MakeImageFromCanvasImageSource = function(e) {
                    var r = e.width
                      , n = e.height;
                    G || (G = document.createElement("canvas")),
                    G.width = r,
                    G.height = n;
                    var a = G.getContext("2d", {
                        willReadFrequently: !0
                    });
                    a.drawImage(e, 0, 0);
                    var o = a.getImageData(0, 0, r, n);
                    return t.MakeImage({
                        width: r,
                        height: n,
                        alphaType: t.AlphaType.Unpremul,
                        colorType: t.ColorType.RGBA_8888,
                        colorSpace: t.ColorSpace.SRGB
                    }, o.data, 4 * r)
                }
                ,
                t.MakeImage = function(e, r, n) {
                    var a = t._malloc(r.length);
                    return t.HEAPU8.set(r, a),
                    t._MakeImage(e, a, r.length, n)
                }
                ,
                t.MakeVertices = function(e, r, n, a, i, s) {
                    s = void 0 === s || s;
                    var l = i && i.length || 0
                      , c = 0;
                    n && n.length && (c |= 1),
                    a && a.length && (c |= 2),
                    s || (c |= 4);
                    var u = new t._VerticesBuilder(e,r.length / 2,l,c);
                    return P(r, "HEAPF32", u.positions()),
                    u.texCoords() && P(n, "HEAPF32", u.texCoords()),
                    u.colors() && P(o(a), "HEAPU32", u.colors()),
                    u.indices() && P(i, "HEAPU16", u.indices()),
                    u.detach()
                }
                ,
                t.Matrix = {};
                var H = function(e) {
                    for (var t = e * e, r = Array(t); t--; )
                        r[t] = t % (e + 1) == 0 ? 1 : 0;
                    return r
                }
                  , j = function(e, t, r, n, a) {
                    for (var o = 0; o < e.length; o++)
                        t[o * r + (o * a + n + r) % r] = e[o];
                    return t
                };
                function $(e, t, r) {
                    for (var n = Array(e.length), a = 0; a < r; a++)
                        for (var o = 0; o < r; o++) {
                            for (var i = 0, s = 0; s < r; s++)
                                i += e[r * a + s] * t[r * s + o];
                            n[a * r + o] = i
                        }
                    return n
                }
                function N(e, t) {
                    for (var r = $(t[0], t[1], e), n = 2; n < t.length; )
                        r = $(r, t[n], e),
                        n++;
                    return r
                }
                t.Matrix.identity = function() {
                    return H(3)
                }
                ,
                t.Matrix.invert = function(e) {
                    var t = e[0] * e[4] * e[8] + e[1] * e[5] * e[6] + e[2] * e[3] * e[7] - e[2] * e[4] * e[6] - e[1] * e[3] * e[8] - e[0] * e[5] * e[7];
                    return t ? [(e[4] * e[8] - e[5] * e[7]) / t, (e[2] * e[7] - e[1] * e[8]) / t, (e[1] * e[5] - e[2] * e[4]) / t, (e[5] * e[6] - e[3] * e[8]) / t, (e[0] * e[8] - e[2] * e[6]) / t, (e[2] * e[3] - e[0] * e[5]) / t, (e[3] * e[7] - e[4] * e[6]) / t, (e[1] * e[6] - e[0] * e[7]) / t, (e[0] * e[4] - e[1] * e[3]) / t] : null
                }
                ,
                t.Matrix.mapPoints = function(e, t) {
                    for (var r = 0; r < t.length; r += 2) {
                        var n = t[r]
                          , a = t[r + 1]
                          , o = e[6] * n + e[7] * a + e[8]
                          , i = e[0] * n + e[1] * a + e[2]
                          , s = e[3] * n + e[4] * a + e[5];
                        t[r] = i / o,
                        t[r + 1] = s / o
                    }
                    return t
                }
                ,
                t.Matrix.multiply = function() {
                    return N(3, arguments)
                }
                ,
                t.Matrix.rotated = function(e, t, r) {
                    var n = Math.sin(e)
                      , a = Math.cos(e);
                    return [a, -n, W(n, r = r || 0, 1 - a, t = t || 0), n, a, W(-n, t, 1 - a, r), 0, 0, 1]
                }
                ,
                t.Matrix.scaled = function(e, t, r, n) {
                    r = r || 0,
                    n = n || 0;
                    var a = j([e, t], H(3), 3, 0, 1);
                    return j([r - e * r, n - t * n], a, 3, 2, 0)
                }
                ,
                t.Matrix.skewed = function(e, t, r, n) {
                    r = r || 0,
                    n = n || 0;
                    var a = j([e, t], H(3), 3, 1, -1);
                    return j([-e * r, -t * n], a, 3, 2, 0)
                }
                ,
                t.Matrix.translated = function(e, t) {
                    return j(arguments, H(3), 3, 2, 0)
                }
                ,
                t.Vector = {},
                t.Vector.dot = function(e, t) {
                    return e.map(function(e, r) {
                        return e * t[r]
                    }).reduce(function(e, t) {
                        return e + t
                    })
                }
                ,
                t.Vector.lengthSquared = function(e) {
                    return t.Vector.dot(e, e)
                }
                ,
                t.Vector.length = function(e) {
                    return Math.sqrt(t.Vector.lengthSquared(e))
                }
                ,
                t.Vector.mulScalar = function(e, t) {
                    return e.map(function(e) {
                        return e * t
                    })
                }
                ,
                t.Vector.add = function(e, t) {
                    return e.map(function(e, r) {
                        return e + t[r]
                    })
                }
                ,
                t.Vector.sub = function(e, t) {
                    return e.map(function(e, r) {
                        return e - t[r]
                    })
                }
                ,
                t.Vector.dist = function(e, r) {
                    return t.Vector.length(t.Vector.sub(e, r))
                }
                ,
                t.Vector.normalize = function(e) {
                    return t.Vector.mulScalar(e, 1 / t.Vector.length(e))
                }
                ,
                t.Vector.cross = function(e, t) {
                    return [e[1] * t[2] - e[2] * t[1], e[2] * t[0] - e[0] * t[2], e[0] * t[1] - e[1] * t[0]]
                }
                ,
                t.M44 = {},
                t.M44.identity = function() {
                    return H(4)
                }
                ,
                t.M44.translated = function(e) {
                    return j(e, H(4), 4, 3, 0)
                }
                ,
                t.M44.scaled = function(e) {
                    return j(e, H(4), 4, 0, 1)
                }
                ,
                t.M44.rotated = function(e, r) {
                    return t.M44.rotatedUnitSinCos(t.Vector.normalize(e), Math.sin(r), Math.cos(r))
                }
                ,
                t.M44.rotatedUnitSinCos = function(e, t, r) {
                    var n = e[0]
                      , a = e[1]
                      , o = e[2]
                      , i = 1 - r;
                    return [i * n * n + r, i * n * a - t * o, i * n * o + t * a, 0, i * n * a + t * o, i * a * a + r, i * a * o - t * n, 0, i * n * o - t * a, i * a * o + t * n, i * o * o + r, 0, 0, 0, 0, 1]
                }
                ,
                t.M44.lookat = function(e, r, n) {
                    var a = t.Vector.normalize(t.Vector.sub(r, e))
                      , o = t.Vector.normalize(n)
                      , i = t.Vector.normalize(t.Vector.cross(a, o))
                      , s = t.M44.identity();
                    j(i, s, 4, 0, 0),
                    j(t.Vector.cross(i, a), s, 4, 1, 0),
                    j(t.Vector.mulScalar(a, -1), s, 4, 2, 0),
                    j(e, s, 4, 3, 0);
                    var l = t.M44.invert(s);
                    return null === l ? t.M44.identity() : l
                }
                ,
                t.M44.perspective = function(e, t, r) {
                    var n = 1 / (t - e)
                      , a = r / 2
                      , o = Math.cos(a) / Math.sin(a);
                    return [o, 0, 0, 0, 0, o, 0, 0, 0, 0, (t + e) * n, 2 * t * e * n, 0, 0, -1, 1]
                }
                ,
                t.M44.rc = function(e, t, r) {
                    return e[4 * t + r]
                }
                ,
                t.M44.multiply = function() {
                    return N(4, arguments)
                }
                ,
                t.M44.invert = function(e) {
                    var t = e[0]
                      , r = e[4]
                      , n = e[8]
                      , a = e[12]
                      , o = e[1]
                      , i = e[5]
                      , s = e[9]
                      , l = e[13]
                      , c = e[2]
                      , u = e[6]
                      , f = e[10]
                      , h = e[14]
                      , d = e[3]
                      , p = e[7]
                      , g = e[11]
                      , m = e[15]
                      , y = t * i - r * o
                      , _ = t * s - n * o
                      , v = t * l - a * o
                      , b = r * s - n * i
                      , C = r * l - a * i
                      , A = n * l - a * s
                      , w = c * p - u * d
                      , x = c * g - f * d
                      , T = c * m - h * d
                      , P = u * g - f * p
                      , S = u * m - h * p
                      , F = f * m - h * g
                      , k = y * F - _ * S + v * P + b * T - C * x + A * w
                      , E = 1 / k;
                    if (0 === k || E === 1 / 0)
                        return null;
                    y *= E,
                    _ *= E,
                    v *= E,
                    b *= E,
                    C *= E,
                    A *= E,
                    w *= E,
                    x *= E,
                    T *= E,
                    P *= E,
                    S *= E;
                    var M = [i * (F *= E) - s * S + l * P, s * T - o * F - l * x, o * S - i * T + l * w, i * x - o * P - s * w, n * S - r * F - a * P, t * F - n * T + a * x, r * T - t * S - a * w, t * P - r * x + n * w, p * A - g * C + m * b, g * v - d * A - m * _, d * C - p * v + m * y, p * _ - d * b - g * y, f * C - u * A - h * b, c * A - f * v + h * _, u * v - c * C - h * y, c * b - u * _ + f * y];
                    return M.every(function(e) {
                        return !isNaN(e) && e !== 1 / 0 && e !== -1 / 0
                    }) ? M : null
                }
                ,
                t.M44.transpose = function(e) {
                    return [e[0], e[4], e[8], e[12], e[1], e[5], e[9], e[13], e[2], e[6], e[10], e[14], e[3], e[7], e[11], e[15]]
                }
                ,
                t.M44.mustInvert = function(e) {
                    var r = t.M44.invert(e);
                    if (null === r)
                        throw "Matrix not invertible";
                    return r
                }
                ,
                t.M44.setupCamera = function(e, r, n) {
                    var a = t.M44.lookat(n.eye, n.coa, n.up)
                      , o = t.M44.perspective(n.near, n.far, n.angle)
                      , i = [(e[0] + e[2]) / 2, (e[1] + e[3]) / 2, 0]
                      , s = [(e[2] - e[0]) / 2, (e[3] - e[1]) / 2, r]
                      , l = t.M44.multiply(t.M44.translated(i), t.M44.scaled(s));
                    return t.M44.multiply(l, o, a, t.M44.mustInvert(l))
                }
                ,
                t.ColorMatrix = {},
                t.ColorMatrix.identity = function() {
                    var e = new Float32Array(20);
                    return e[0] = 1,
                    e[6] = 1,
                    e[12] = 1,
                    e[18] = 1,
                    e
                }
                ,
                t.ColorMatrix.scaled = function(e, t, r, n) {
                    var a = new Float32Array(20);
                    return a[0] = e,
                    a[6] = t,
                    a[12] = r,
                    a[18] = n,
                    a
                }
                ;
                var V = [[6, 7, 11, 12], [0, 10, 2, 12], [0, 1, 5, 6]];
                t.ColorMatrix.rotated = function(e, r, n) {
                    var a = t.ColorMatrix.identity()
                      , o = V[e];
                    return a[o[0]] = n,
                    a[o[1]] = r,
                    a[o[2]] = -r,
                    a[o[3]] = n,
                    a
                }
                ,
                t.ColorMatrix.postTranslate = function(e, t, r, n, a) {
                    return e[4] += t,
                    e[9] += r,
                    e[14] += n,
                    e[19] += a,
                    e
                }
                ,
                t.ColorMatrix.concat = function(e, t) {
                    for (var r = new Float32Array(20), n = 0, a = 0; a < 20; a += 5) {
                        for (var o = 0; o < 4; o++)
                            r[n++] = e[a + 0] * t[o + 0] + e[a + 1] * t[o + 5] + e[a + 2] * t[o + 10] + e[a + 3] * t[o + 15];
                        r[n++] = e[a + 0] * t[4] + e[a + 1] * t[9] + e[a + 2] * t[14] + e[a + 3] * t[19] + e[a + 4]
                    }
                    return r
                }
                ,
                e._extraInitializations = e._extraInitializations || [],
                e._extraInitializations.push(function() {
                    function t(t) {
                        return t && (0 === t.dir ? t.dir = e.TextDirection.RTL : t.dir = e.TextDirection.LTR),
                        t
                    }
                    function r(t) {
                        if (!t || !t.length)
                            return [];
                        for (var r = [], n = 0; n < t.length; n += 5) {
                            var a = e.LTRBRect(t[n], t[n + 1], t[n + 2], t[n + 3])
                              , o = e.TextDirection.LTR;
                            0 === t[n + 4] && (o = e.TextDirection.RTL),
                            r.push({
                                rect: a,
                                dir: o
                            })
                        }
                        return e._free(t.byteOffset),
                        r
                    }
                    function n(t) {
                        return void 0 === (t = t || {}).weight && (t.weight = e.FontWeight.Normal),
                        t.width = t.width || e.FontWidth.Normal,
                        t.slant = t.slant || e.FontSlant.Upright,
                        t
                    }
                    function a(e) {
                        if (!e || !e.length)
                            return D;
                        for (var t = [], r = 0; r < e.length; r++) {
                            var n = i(e[r]);
                            t.push(n)
                        }
                        return P(t, "HEAPU32")
                    }
                    e.Paragraph.prototype.getRectsForRange = function(e, t, n, a) {
                        return r(this._getRectsForRange(e, t, n, a))
                    }
                    ,
                    e.Paragraph.prototype.getRectsForPlaceholders = function() {
                        return r(this._getRectsForPlaceholders())
                    }
                    ,
                    e.Paragraph.prototype.getGlyphInfoAt = function(e) {
                        return t(this._getGlyphInfoAt(e))
                    }
                    ,
                    e.Paragraph.prototype.getClosestGlyphInfoAtCoordinate = function(e, r) {
                        return t(this._getClosestGlyphInfoAtCoordinate(e, r))
                    }
                    ,
                    e.TypefaceFontProvider.prototype.registerFont = function(t, r) {
                        var n = e.Typeface.MakeTypefaceFromData(t);
                        if (!n)
                            return null;
                        n.deleteLater();
                        var a = i(r);
                        this._registerFont(n, a)
                    }
                    ,
                    e.ParagraphStyle = function(t) {
                        if (t.disableHinting = t.disableHinting || !1,
                        t.ellipsis) {
                            var r, o = t.ellipsis;
                            t._ellipsisPtr = i(o),
                            t._ellipsisLen = eg(o)
                        } else
                            t._ellipsisPtr = D,
                            t._ellipsisLen = 0;
                        return null == t.heightMultiplier && (t.heightMultiplier = -1),
                        t.maxLines = t.maxLines || 0,
                        t.replaceTabCharacters = t.replaceTabCharacters || !1,
                        t.strutStyle = ((r = (r = t.strutStyle) || {}).strutEnabled = r.strutEnabled || !1,
                        r.strutEnabled && Array.isArray(r.fontFamilies) && r.fontFamilies.length ? (r._fontFamiliesPtr = a(r.fontFamilies),
                        r._fontFamiliesLen = r.fontFamilies.length) : (r._fontFamiliesPtr = D,
                        r._fontFamiliesLen = 0),
                        r.fontStyle = n(r.fontStyle),
                        null == r.fontSize && (r.fontSize = -1),
                        null == r.heightMultiplier && (r.heightMultiplier = -1),
                        r.halfLeading = r.halfLeading || !1,
                        r.leading = r.leading || 0,
                        r.forceStrutHeight = r.forceStrutHeight || !1,
                        r),
                        t.textAlign = t.textAlign || e.TextAlign.Start,
                        t.textDirection = t.textDirection || e.TextDirection.LTR,
                        t.textHeightBehavior = t.textHeightBehavior || e.TextHeightBehavior.All,
                        t.textStyle = e.TextStyle(t.textStyle),
                        t.applyRoundingHack = !1 !== t.applyRoundingHack,
                        t
                    }
                    ,
                    e.TextStyle = function(t) {
                        return t.color || (t.color = e.BLACK),
                        t.decoration = t.decoration || 0,
                        t.decorationThickness = t.decorationThickness || 0,
                        t.decorationStyle = t.decorationStyle || e.DecorationStyle.Solid,
                        t.textBaseline = t.textBaseline || e.TextBaseline.Alphabetic,
                        null == t.fontSize && (t.fontSize = -1),
                        t.letterSpacing = t.letterSpacing || 0,
                        t.wordSpacing = t.wordSpacing || 0,
                        null == t.heightMultiplier && (t.heightMultiplier = -1),
                        t.halfLeading = t.halfLeading || !1,
                        t.fontStyle = n(t.fontStyle),
                        t
                    }
                    ;
                    var o = {};
                    function i(t) {
                        if (o[t])
                            return o[t];
                        var r = eg(t) + 1
                          , n = e._malloc(r);
                        return ey(t, n, r),
                        o[t] = n,
                        n
                    }
                    var s = e._malloc(16)
                      , c = e._malloc(16)
                      , u = e._malloc(16);
                    function f(t) {
                        if (t._colorPtr = E(t.color),
                        t._foregroundColorPtr = D,
                        t._backgroundColorPtr = D,
                        t._decorationColorPtr = D,
                        t.foregroundColor && (t._foregroundColorPtr = E(t.foregroundColor, s)),
                        t.backgroundColor && (t._backgroundColorPtr = E(t.backgroundColor, c)),
                        t.decorationColor && (t._decorationColorPtr = E(t.decorationColor, u)),
                        Array.isArray(t.fontFamilies) && t.fontFamilies.length ? (t._fontFamiliesPtr = a(t.fontFamilies),
                        t._fontFamiliesLen = t.fontFamilies.length) : (t._fontFamiliesPtr = D,
                        t._fontFamiliesLen = 0),
                        t.locale) {
                            var r = t.locale;
                            t._localePtr = i(r),
                            t._localeLen = eg(r)
                        } else
                            t._localePtr = D,
                            t._localeLen = 0;
                        if (Array.isArray(t.shadows) && t.shadows.length) {
                            var n = t.shadows
                              , o = n.map(function(t) {
                                return t.color || e.BLACK
                            })
                              , l = n.map(function(e) {
                                return e.blurRadius || 0
                            });
                            t._shadowLen = n.length;
                            for (var f = e._malloc(8 * n.length), h = f / 4, d = 0; d < n.length; d++) {
                                var p = n[d].offset || [0, 0];
                                e.HEAPF32[h] = p[0],
                                e.HEAPF32[h + 1] = p[1],
                                h += 2
                            }
                            t._shadowColorsPtr = S(o).colorPtr,
                            t._shadowOffsetsPtr = f,
                            t._shadowBlurRadiiPtr = P(l, "HEAPF32")
                        } else
                            t._shadowLen = 0,
                            t._shadowColorsPtr = D,
                            t._shadowOffsetsPtr = D,
                            t._shadowBlurRadiiPtr = D;
                        if (Array.isArray(t.fontFeatures) && t.fontFeatures.length) {
                            var g = t.fontFeatures
                              , m = g.map(function(e) {
                                return e.name
                            })
                              , y = g.map(function(e) {
                                return e.value
                            });
                            t._fontFeatureLen = g.length,
                            t._fontFeatureNamesPtr = a(m),
                            t._fontFeatureValuesPtr = P(y, "HEAPU32")
                        } else
                            t._fontFeatureLen = 0,
                            t._fontFeatureNamesPtr = D,
                            t._fontFeatureValuesPtr = D;
                        if (Array.isArray(t.fontVariations) && t.fontVariations.length) {
                            var _ = t.fontVariations
                              , v = _.map(function(e) {
                                return e.axis
                            })
                              , b = _.map(function(e) {
                                return e.value
                            });
                            t._fontVariationLen = _.length,
                            t._fontVariationAxesPtr = a(v),
                            t._fontVariationValuesPtr = P(b, "HEAPF32")
                        } else
                            t._fontVariationLen = 0,
                            t._fontVariationAxesPtr = D,
                            t._fontVariationValuesPtr = D
                    }
                    function h(t) {
                        e._free(t._fontFamiliesPtr),
                        e._free(t._shadowColorsPtr),
                        e._free(t._shadowOffsetsPtr),
                        e._free(t._shadowBlurRadiiPtr),
                        e._free(t._fontFeatureNamesPtr),
                        e._free(t._fontFeatureValuesPtr),
                        e._free(t._fontVariationAxesPtr),
                        e._free(t._fontVariationValuesPtr)
                    }
                    e.ParagraphBuilder.Make = function(t, r) {
                        f(t.textStyle);
                        var n = e.ParagraphBuilder._Make(t, r);
                        return h(t.textStyle),
                        n
                    }
                    ,
                    e.ParagraphBuilder.MakeFromFontProvider = function(t, r) {
                        f(t.textStyle);
                        var n = e.ParagraphBuilder._MakeFromFontProvider(t, r);
                        return h(t.textStyle),
                        n
                    }
                    ,
                    e.ParagraphBuilder.MakeFromFontCollection = function(t, r) {
                        f(t.textStyle);
                        var n = e.ParagraphBuilder._MakeFromFontCollection(t, r);
                        return h(t.textStyle),
                        n
                    }
                    ,
                    e.ParagraphBuilder.ShapeText = function(t, r, n) {
                        let a = 0;
                        for (let e of r)
                            a += e.length;
                        if (a !== t.length)
                            throw "Accumulated block lengths must equal text.length";
                        return e.ParagraphBuilder._ShapeText(t, r, n)
                    }
                    ,
                    e.ParagraphBuilder.prototype.pushStyle = function(e) {
                        f(e),
                        this._pushStyle(e),
                        h(e)
                    }
                    ,
                    e.ParagraphBuilder.prototype.pushPaintStyle = function(e, t, r) {
                        f(e),
                        this._pushPaintStyle(e, t, r),
                        h(e)
                    }
                    ,
                    e.ParagraphBuilder.prototype.addPlaceholder = function(t, r, n, a, o) {
                        t = t || 0,
                        r = r || 0,
                        n = n || e.PlaceholderAlignment.Baseline,
                        a = a || e.TextBaseline.Alphabetic,
                        o = o || 0,
                        this._addPlaceholder(t, r, n, a, o)
                    }
                    ,
                    e.ParagraphBuilder.prototype.setWordsUtf8 = function(e) {
                        var t = P(e, "HEAPU32");
                        this._setWordsUtf8(t, e && e.length || 0),
                        l(t, e)
                    }
                    ,
                    e.ParagraphBuilder.prototype.setWordsUtf16 = function(e) {
                        var t = P(e, "HEAPU32");
                        this._setWordsUtf16(t, e && e.length || 0),
                        l(t, e)
                    }
                    ,
                    e.ParagraphBuilder.prototype.setGraphemeBreaksUtf8 = function(e) {
                        var t = P(e, "HEAPU32");
                        this._setGraphemeBreaksUtf8(t, e && e.length || 0),
                        l(t, e)
                    }
                    ,
                    e.ParagraphBuilder.prototype.setGraphemeBreaksUtf16 = function(e) {
                        var t = P(e, "HEAPU32");
                        this._setGraphemeBreaksUtf16(t, e && e.length || 0),
                        l(t, e)
                    }
                    ,
                    e.ParagraphBuilder.prototype.setLineBreaksUtf8 = function(e) {
                        var t = P(e, "HEAPU32");
                        this._setLineBreaksUtf8(t, e && e.length || 0),
                        l(t, e)
                    }
                    ,
                    e.ParagraphBuilder.prototype.setLineBreaksUtf16 = function(e) {
                        var t = P(e, "HEAPU32");
                        this._setLineBreaksUtf16(t, e && e.length || 0),
                        l(t, e)
                    }
                }),
                t.MakeManagedAnimation = function(e, r, n, a, o) {
                    if (!t._MakeManagedAnimation)
                        throw "Not compiled with MakeManagedAnimation";
                    if (n || (n = ""),
                    !r)
                        return t._MakeManagedAnimation(e, 0, D, D, D, n, a, o);
                    for (var i = [], s = [], l = [], c = Object.keys(r || {}), u = 0; u < c.length; u++) {
                        var f = c[u]
                          , h = new Uint8Array(r[f])
                          , d = t._malloc(h.byteLength);
                        t.HEAPU8.set(h, d),
                        s.push(d),
                        l.push(h.byteLength);
                        var p = eg(f) + 1
                          , g = t._malloc(p);
                        ey(f, g, p),
                        i.push(g)
                    }
                    var m = P(i, "HEAPU32")
                      , y = P(s, "HEAPU32")
                      , _ = P(l, "HEAPU32")
                      , v = t._MakeManagedAnimation(e, c.length, m, y, _, n, a, o);
                    return t._free(m),
                    t._free(y),
                    t._free(_),
                    v
                }
                ,
                t.SlottableTextProperty = function(e) {
                    return e.text = e.text || "",
                    e.textSize = e.textSize || 0,
                    e.minTextSize = e.minTextSize || 0,
                    e.maxTextSize = e.maxTextSize || Number.MAX_VALUE,
                    e.strokeWidth = e.strokeWidth || 0,
                    e.lineHeight = e.lineHeight || 0,
                    e.lineShift = e.lineShift || 0,
                    e.ascent = e.ascent || 0,
                    e.maxLines = e.maxLines || 0,
                    e.horizAlign = e.horizAlign || t.TextAlign.Left,
                    e.vertAlign = e.vertAlign || t.VerticalTextAlign.Top,
                    e.strokeJoin = e.strokeJoin || t.StrokeJoin.Miter,
                    e.direction = e.direction || t.TextDirection.LTR,
                    e.linebreak = e.linebreak || t.LineBreakType.HardLineBreak,
                    e.resize = e.resize || t.ResizePolicy.None,
                    e.fillColor || (e.fillColor = t.TRANSPARENT),
                    e.strokeColor || (e.strokeColor = t.TRANSPARENT),
                    e.boundingBox || (e.boundingBox = [0, 0, 0, 0]),
                    e
                }
                ,
                e._extraInitializations = e._extraInitializations || [],
                e._extraInitializations.push(function() {
                    e.Animation.prototype.render = function(e, t) {
                        R(t, v),
                        this._render(e, v)
                    }
                    ,
                    e.Animation.prototype.size = function(e) {
                        this._size(v);
                        var t = d.toTypedArray();
                        return e ? (e[0] = t[0],
                        e[1] = t[1],
                        e) : t.slice(0, 2)
                    }
                    ,
                    e.ManagedAnimation && (e.ManagedAnimation.prototype.render = function(e, t) {
                        R(t, v),
                        this._render(e, v)
                    }
                    ,
                    e.ManagedAnimation.prototype.seek = function(e, t) {
                        this._seek(e, v);
                        var r = d.toTypedArray();
                        return t ? (t.set(r),
                        t) : r.slice()
                    }
                    ,
                    e.ManagedAnimation.prototype.seekFrame = function(e, t) {
                        this._seekFrame(e, v);
                        var r = d.toTypedArray();
                        return t ? (t.set(r),
                        t) : r.slice()
                    }
                    ,
                    e.ManagedAnimation.prototype.setColor = function(e, t) {
                        var r = E(t);
                        return this._setColor(e, r)
                    }
                    ,
                    e.ManagedAnimation.prototype.setColorSlot = function(e, t) {
                        var r = E(t);
                        return this._setColorSlot(e, r)
                    }
                    ,
                    e.ManagedAnimation.prototype.getColorSlot = function(e) {
                        this._getColorSlot(e, _);
                        var t = B(_);
                        return -1 == t[0] ? null : t
                    }
                    ,
                    e.ManagedAnimation.prototype.setVec2Slot = function(e, t) {
                        return P(t, "HEAPF32", C),
                        this._setVec2Slot(e, C)
                    }
                    ,
                    e.ManagedAnimation.prototype.getVec2Slot = function(e) {
                        this._getVec2Slot(e, C);
                        var t = p.toTypedArray();
                        return -1 === t[2] ? null : t.slice(0, 2)
                    }
                    ,
                    e.ManagedAnimation.prototype.setTextSlot = function(e, t) {
                        var r = E(t.fillColor, _)
                          , n = E(t.strokeColor, v)
                          , a = R(t.boundingBox, b);
                        return t._fillColorPtr = r,
                        t._strokeColorPtr = n,
                        t._boundingBoxPtr = a,
                        this._setTextSlot(e, t)
                    }
                    ,
                    e.ManagedAnimation.prototype.setTransform = function(e, t, r, n, a, o, i) {
                        let s = P([t[0], t[1], r[0], r[1], n[0], n[1], a, o, i], "HEAPF32", m);
                        return this._setTransform(e, s)
                    }
                    ,
                    e.ManagedAnimation.prototype.size = function(e) {
                        this._size(v);
                        var t = d.toTypedArray();
                        return e ? (e[0] = t[0],
                        e[1] = t[1],
                        e) : t.slice(0, 2)
                    }
                    )
                }),
                t._extraInitializations = t._extraInitializations || [],
                t._extraInitializations.push(function() {
                    t.Path.prototype.op = function(e, t) {
                        return this._op(e, t) ? this : null
                    }
                    ,
                    t.Path.prototype.simplify = function() {
                        return this._simplify() ? this : null
                    }
                }),
                t._extraInitializations = t._extraInitializations || [],
                t._extraInitializations.push(function() {
                    t.Canvas.prototype.drawText = function(e, r, n, a, o) {
                        var i = eg(e)
                          , s = t._malloc(i + 1);
                        ey(e, s, i + 1),
                        this._drawSimpleText(s, i, r, n, o, a),
                        t._free(s)
                    }
                    ,
                    t.Canvas.prototype.drawGlyphs = function(e, r, n, a, o, i) {
                        if (!(2 * e.length <= r.length))
                            throw "Not enough positions for the array of gyphs";
                        t.setCurrentContext(this._context);
                        let s = P(e, "HEAPU16")
                          , c = P(r, "HEAPF32");
                        this._drawGlyphs(e.length, s, c, n, a, o, i),
                        l(c, r),
                        l(s, e)
                    }
                    ,
                    t.Font.prototype.getGlyphBounds = function(e, r, n) {
                        var a = P(e, "HEAPU16")
                          , o = t._malloc(16 * e.length);
                        this._getGlyphWidthBounds(a, e.length, D, o, r || null);
                        var i = new Float32Array(t.HEAPU8.buffer,o,4 * e.length);
                        if (l(a, e),
                        n)
                            return n.set(i),
                            t._free(o),
                            n;
                        var s = Float32Array.from(i);
                        return t._free(o),
                        s
                    }
                    ,
                    t.Font.prototype.getGlyphIDs = function(e, r, n) {
                        r || (r = e.length);
                        var a = eg(e) + 1
                          , o = t._malloc(a);
                        ey(e, o, a);
                        var i = t._malloc(2 * r)
                          , s = this._getGlyphIDs(o, a - 1, r, i);
                        if (t._free(o),
                        s < 0)
                            return t._free(i),
                            null;
                        var l = new Uint16Array(t.HEAPU8.buffer,i,s);
                        if (n)
                            return n.set(l),
                            t._free(i),
                            n;
                        var c = Uint16Array.from(l);
                        return t._free(i),
                        c
                    }
                    ,
                    t.Font.prototype.getGlyphIntercepts = function(e, t, r, n) {
                        var a = P(e, "HEAPU16")
                          , o = P(t, "HEAPF32");
                        return this._getGlyphIntercepts(a, e.length, !c(e), o, t.length, !c(t), r, n)
                    }
                    ,
                    t.Font.prototype.getGlyphWidths = function(e, r, n) {
                        var a = P(e, "HEAPU16")
                          , o = t._malloc(4 * e.length);
                        this._getGlyphWidthBounds(a, e.length, o, D, r || null);
                        var i = new Float32Array(t.HEAPU8.buffer,o,e.length);
                        if (l(a, e),
                        n)
                            return n.set(i),
                            t._free(o),
                            n;
                        var s = Float32Array.from(i);
                        return t._free(o),
                        s
                    }
                    ,
                    t.FontMgr.FromData = function() {
                        if (!arguments.length)
                            return null;
                        var e = arguments;
                        if (1 === e.length && Array.isArray(e[0]) && (e = arguments[0]),
                        !e.length)
                            return null;
                        for (var r = [], n = [], a = 0; a < e.length; a++) {
                            var o = new Uint8Array(e[a])
                              , i = P(o, "HEAPU8");
                            r.push(i),
                            n.push(o.byteLength)
                        }
                        var s = P(r, "HEAPU32")
                          , l = P(n, "HEAPU32")
                          , c = t.FontMgr._fromData(s, l, e.length);
                        return t._free(s),
                        t._free(l),
                        c
                    }
                    ,
                    t.Typeface.MakeTypefaceFromData = function(e) {
                        var r = new Uint8Array(e)
                          , n = P(r, "HEAPU8");
                        return t.Typeface._MakeTypefaceFromData(n, r.byteLength) || null
                    }
                    ,
                    t.Typeface.MakeFreeTypeFaceFromData = t.Typeface.MakeTypefaceFromData,
                    t.Typeface.prototype.getGlyphIDs = function(e, r, n) {
                        r || (r = e.length);
                        var a = eg(e) + 1
                          , o = t._malloc(a);
                        ey(e, o, a);
                        var i = t._malloc(2 * r)
                          , s = this._getGlyphIDs(o, a - 1, r, i);
                        if (t._free(o),
                        s < 0)
                            return t._free(i),
                            null;
                        var l = new Uint16Array(t.HEAPU8.buffer,i,s);
                        if (n)
                            return n.set(l),
                            t._free(i),
                            n;
                        var c = Uint16Array.from(l);
                        return t._free(i),
                        c
                    }
                    ,
                    t.TextBlob.MakeOnPath = function(e, r, n, a) {
                        if (e && e.length && r && r.countPoints()) {
                            if (1 === r.countPoints())
                                return this.MakeFromText(e, n);
                            a || (a = 0);
                            for (var o = n.getGlyphIDs(e), i = n.getGlyphWidths(o), s = [], l = new t.ContourMeasureIter(r,!1,1), c = l.next(), u = a, f = new Float32Array(4), h = 0; h < e.length && c; h++) {
                                var d = i[h];
                                if ((u += d / 2) > c.length()) {
                                    if (c.delete(),
                                    !(c = l.next())) {
                                        e = e.substring(0, h);
                                        break
                                    }
                                    u = d / 2
                                }
                                c.getPosTan(u, f);
                                var p = f[0]
                                  , g = f[1]
                                  , m = f[2]
                                  , y = f[3]
                                  , _ = p - d / 2 * m
                                  , v = g - d / 2 * y;
                                s.push(m, y, _, v),
                                u += d / 2
                            }
                            var b = this.MakeFromRSXform(e, s, n);
                            return c && c.delete(),
                            l.delete(),
                            b
                        }
                    }
                    ,
                    t.TextBlob.MakeFromRSXform = function(e, r, n) {
                        var a = eg(e) + 1
                          , o = t._malloc(a);
                        ey(e, o, a);
                        var i = P(r, "HEAPF32")
                          , s = t.TextBlob._MakeFromRSXform(o, a - 1, i, n);
                        return (t._free(o),
                        s) ? s : null
                    }
                    ,
                    t.TextBlob.MakeFromRSXformGlyphs = function(e, r, n) {
                        var a = P(e, "HEAPU16")
                          , o = P(r, "HEAPF32")
                          , i = t.TextBlob._MakeFromRSXformGlyphs(a, 2 * e.length, o, n);
                        return (l(a, e),
                        i) ? i : null
                    }
                    ,
                    t.TextBlob.MakeFromGlyphs = function(e, r) {
                        var n = P(e, "HEAPU16")
                          , a = t.TextBlob._MakeFromGlyphs(n, 2 * e.length, r);
                        return (l(n, e),
                        a) ? a : null
                    }
                    ,
                    t.TextBlob.MakeFromText = function(e, r) {
                        var n = eg(e) + 1
                          , a = t._malloc(n);
                        ey(e, a, n);
                        var o = t.TextBlob._MakeFromText(a, n - 1, r);
                        return (t._free(a),
                        o) ? o : null
                    }
                    ,
                    t.MallocGlyphIDs = function(e) {
                        return t.Malloc(Uint16Array, e)
                    }
                }),
                t._extraInitializations = t._extraInitializations || [],
                t._extraInitializations.push(function() {
                    t.MakePicture = function(e) {
                        e = new Uint8Array(e);
                        var r = t._malloc(e.byteLength);
                        return t.HEAPU8.set(e, r),
                        t._MakePicture(r, e.byteLength) || null
                    }
                }),
                t._extraInitializations = t._extraInitializations || [],
                t._extraInitializations.push(function() {
                    t.RuntimeEffect.Make = function(e, r) {
                        return t.RuntimeEffect._Make(e, {
                            onError: r || function(e) {
                                console.log("RuntimeEffect error", e)
                            }
                        })
                    }
                    ,
                    t.RuntimeEffect.MakeForBlender = function(e, r) {
                        return t.RuntimeEffect._MakeForBlender(e, {
                            onError: r || function(e) {
                                console.log("RuntimeEffect error", e)
                            }
                        })
                    }
                    ,
                    t.RuntimeEffect.prototype.makeShader = function(e, t) {
                        var r = !e._ck
                          , n = P(e, "HEAPF32")
                          , a = k(t);
                        return this._makeShader(n, 4 * e.length, r, a)
                    }
                    ,
                    t.RuntimeEffect.prototype.makeShaderWithChildren = function(e, t, r) {
                        for (var n = !e._ck, a = P(e, "HEAPF32"), o = k(r), i = [], s = 0; s < t.length; s++)
                            i.push(t[s].$$.ptr);
                        var l = P(i, "HEAPU32");
                        return this._makeShaderWithChildren(a, 4 * e.length, n, l, i.length, o)
                    }
                    ,
                    t.RuntimeEffect.prototype.makeBlender = function(e) {
                        var t = !e._ck
                          , r = P(e, "HEAPF32");
                        return this._makeBlender(r, 4 * e.length, t)
                    }
                }),
                function() {
                    function e(e) {
                        for (var t = 0; t < e.length; t++)
                            if (void 0 !== e[t] && !Number.isFinite(e[t]))
                                return !1;
                        return !0
                    }
                    t._testing = {};
                    var n, o = {
                        aliceblue: Float32Array.of(.941, .973, 1, 1),
                        antiquewhite: Float32Array.of(.98, .922, .843, 1),
                        aqua: Float32Array.of(0, 1, 1, 1),
                        aquamarine: Float32Array.of(.498, 1, .831, 1),
                        azure: Float32Array.of(.941, 1, 1, 1),
                        beige: Float32Array.of(.961, .961, .863, 1),
                        bisque: Float32Array.of(1, .894, .769, 1),
                        black: Float32Array.of(0, 0, 0, 1),
                        blanchedalmond: Float32Array.of(1, .922, .804, 1),
                        blue: Float32Array.of(0, 0, 1, 1),
                        blueviolet: Float32Array.of(.541, .169, .886, 1),
                        brown: Float32Array.of(.647, .165, .165, 1),
                        burlywood: Float32Array.of(.871, .722, .529, 1),
                        cadetblue: Float32Array.of(.373, .62, .627, 1),
                        chartreuse: Float32Array.of(.498, 1, 0, 1),
                        chocolate: Float32Array.of(.824, .412, .118, 1),
                        coral: Float32Array.of(1, .498, .314, 1),
                        cornflowerblue: Float32Array.of(.392, .584, .929, 1),
                        cornsilk: Float32Array.of(1, .973, .863, 1),
                        crimson: Float32Array.of(.863, .078, .235, 1),
                        cyan: Float32Array.of(0, 1, 1, 1),
                        darkblue: Float32Array.of(0, 0, .545, 1),
                        darkcyan: Float32Array.of(0, .545, .545, 1),
                        darkgoldenrod: Float32Array.of(.722, .525, .043, 1),
                        darkgray: Float32Array.of(.663, .663, .663, 1),
                        darkgreen: Float32Array.of(0, .392, 0, 1),
                        darkgrey: Float32Array.of(.663, .663, .663, 1),
                        darkkhaki: Float32Array.of(.741, .718, .42, 1),
                        darkmagenta: Float32Array.of(.545, 0, .545, 1),
                        darkolivegreen: Float32Array.of(.333, .42, .184, 1),
                        darkorange: Float32Array.of(1, .549, 0, 1),
                        darkorchid: Float32Array.of(.6, .196, .8, 1),
                        darkred: Float32Array.of(.545, 0, 0, 1),
                        darksalmon: Float32Array.of(.914, .588, .478, 1),
                        darkseagreen: Float32Array.of(.561, .737, .561, 1),
                        darkslateblue: Float32Array.of(.282, .239, .545, 1),
                        darkslategray: Float32Array.of(.184, .31, .31, 1),
                        darkslategrey: Float32Array.of(.184, .31, .31, 1),
                        darkturquoise: Float32Array.of(0, .808, .82, 1),
                        darkviolet: Float32Array.of(.58, 0, .827, 1),
                        deeppink: Float32Array.of(1, .078, .576, 1),
                        deepskyblue: Float32Array.of(0, .749, 1, 1),
                        dimgray: Float32Array.of(.412, .412, .412, 1),
                        dimgrey: Float32Array.of(.412, .412, .412, 1),
                        dodgerblue: Float32Array.of(.118, .565, 1, 1),
                        firebrick: Float32Array.of(.698, .133, .133, 1),
                        floralwhite: Float32Array.of(1, .98, .941, 1),
                        forestgreen: Float32Array.of(.133, .545, .133, 1),
                        fuchsia: Float32Array.of(1, 0, 1, 1),
                        gainsboro: Float32Array.of(.863, .863, .863, 1),
                        ghostwhite: Float32Array.of(.973, .973, 1, 1),
                        gold: Float32Array.of(1, .843, 0, 1),
                        goldenrod: Float32Array.of(.855, .647, .125, 1),
                        gray: Float32Array.of(.502, .502, .502, 1),
                        green: Float32Array.of(0, .502, 0, 1),
                        greenyellow: Float32Array.of(.678, 1, .184, 1),
                        grey: Float32Array.of(.502, .502, .502, 1),
                        honeydew: Float32Array.of(.941, 1, .941, 1),
                        hotpink: Float32Array.of(1, .412, .706, 1),
                        indianred: Float32Array.of(.804, .361, .361, 1),
                        indigo: Float32Array.of(.294, 0, .51, 1),
                        ivory: Float32Array.of(1, 1, .941, 1),
                        khaki: Float32Array.of(.941, .902, .549, 1),
                        lavender: Float32Array.of(.902, .902, .98, 1),
                        lavenderblush: Float32Array.of(1, .941, .961, 1),
                        lawngreen: Float32Array.of(.486, .988, 0, 1),
                        lemonchiffon: Float32Array.of(1, .98, .804, 1),
                        lightblue: Float32Array.of(.678, .847, .902, 1),
                        lightcoral: Float32Array.of(.941, .502, .502, 1),
                        lightcyan: Float32Array.of(.878, 1, 1, 1),
                        lightgoldenrodyellow: Float32Array.of(.98, .98, .824, 1),
                        lightgray: Float32Array.of(.827, .827, .827, 1),
                        lightgreen: Float32Array.of(.565, .933, .565, 1),
                        lightgrey: Float32Array.of(.827, .827, .827, 1),
                        lightpink: Float32Array.of(1, .714, .757, 1),
                        lightsalmon: Float32Array.of(1, .627, .478, 1),
                        lightseagreen: Float32Array.of(.125, .698, .667, 1),
                        lightskyblue: Float32Array.of(.529, .808, .98, 1),
                        lightslategray: Float32Array.of(.467, .533, .6, 1),
                        lightslategrey: Float32Array.of(.467, .533, .6, 1),
                        lightsteelblue: Float32Array.of(.69, .769, .871, 1),
                        lightyellow: Float32Array.of(1, 1, .878, 1),
                        lime: Float32Array.of(0, 1, 0, 1),
                        limegreen: Float32Array.of(.196, .804, .196, 1),
                        linen: Float32Array.of(.98, .941, .902, 1),
                        magenta: Float32Array.of(1, 0, 1, 1),
                        maroon: Float32Array.of(.502, 0, 0, 1),
                        mediumaquamarine: Float32Array.of(.4, .804, .667, 1),
                        mediumblue: Float32Array.of(0, 0, .804, 1),
                        mediumorchid: Float32Array.of(.729, .333, .827, 1),
                        mediumpurple: Float32Array.of(.576, .439, .859, 1),
                        mediumseagreen: Float32Array.of(.235, .702, .443, 1),
                        mediumslateblue: Float32Array.of(.482, .408, .933, 1),
                        mediumspringgreen: Float32Array.of(0, .98, .604, 1),
                        mediumturquoise: Float32Array.of(.282, .82, .8, 1),
                        mediumvioletred: Float32Array.of(.78, .082, .522, 1),
                        midnightblue: Float32Array.of(.098, .098, .439, 1),
                        mintcream: Float32Array.of(.961, 1, .98, 1),
                        mistyrose: Float32Array.of(1, .894, .882, 1),
                        moccasin: Float32Array.of(1, .894, .71, 1),
                        navajowhite: Float32Array.of(1, .871, .678, 1),
                        navy: Float32Array.of(0, 0, .502, 1),
                        oldlace: Float32Array.of(.992, .961, .902, 1),
                        olive: Float32Array.of(.502, .502, 0, 1),
                        olivedrab: Float32Array.of(.42, .557, .137, 1),
                        orange: Float32Array.of(1, .647, 0, 1),
                        orangered: Float32Array.of(1, .271, 0, 1),
                        orchid: Float32Array.of(.855, .439, .839, 1),
                        palegoldenrod: Float32Array.of(.933, .91, .667, 1),
                        palegreen: Float32Array.of(.596, .984, .596, 1),
                        paleturquoise: Float32Array.of(.686, .933, .933, 1),
                        palevioletred: Float32Array.of(.859, .439, .576, 1),
                        papayawhip: Float32Array.of(1, .937, .835, 1),
                        peachpuff: Float32Array.of(1, .855, .725, 1),
                        peru: Float32Array.of(.804, .522, .247, 1),
                        pink: Float32Array.of(1, .753, .796, 1),
                        plum: Float32Array.of(.867, .627, .867, 1),
                        powderblue: Float32Array.of(.69, .878, .902, 1),
                        purple: Float32Array.of(.502, 0, .502, 1),
                        rebeccapurple: Float32Array.of(.4, .2, .6, 1),
                        red: Float32Array.of(1, 0, 0, 1),
                        rosybrown: Float32Array.of(.737, .561, .561, 1),
                        royalblue: Float32Array.of(.255, .412, .882, 1),
                        saddlebrown: Float32Array.of(.545, .271, .075, 1),
                        salmon: Float32Array.of(.98, .502, .447, 1),
                        sandybrown: Float32Array.of(.957, .643, .376, 1),
                        seagreen: Float32Array.of(.18, .545, .341, 1),
                        seashell: Float32Array.of(1, .961, .933, 1),
                        sienna: Float32Array.of(.627, .322, .176, 1),
                        silver: Float32Array.of(.753, .753, .753, 1),
                        skyblue: Float32Array.of(.529, .808, .922, 1),
                        slateblue: Float32Array.of(.416, .353, .804, 1),
                        slategray: Float32Array.of(.439, .502, .565, 1),
                        slategrey: Float32Array.of(.439, .502, .565, 1),
                        snow: Float32Array.of(1, .98, .98, 1),
                        springgreen: Float32Array.of(0, 1, .498, 1),
                        steelblue: Float32Array.of(.275, .51, .706, 1),
                        tan: Float32Array.of(.824, .706, .549, 1),
                        teal: Float32Array.of(0, .502, .502, 1),
                        thistle: Float32Array.of(.847, .749, .847, 1),
                        tomato: Float32Array.of(1, .388, .278, 1),
                        transparent: Float32Array.of(0, 0, 0, 0),
                        turquoise: Float32Array.of(.251, .878, .816, 1),
                        violet: Float32Array.of(.933, .51, .933, 1),
                        wheat: Float32Array.of(.961, .871, .702, 1),
                        white: Float32Array.of(1, 1, 1, 1),
                        whitesmoke: Float32Array.of(.961, .961, .961, 1),
                        yellow: Float32Array.of(1, 1, 0, 1),
                        yellowgreen: Float32Array.of(.604, .804, .196, 1)
                    };
                    function i(e) {
                        var r = t.getColorComponents(e)
                          , n = r[0]
                          , a = r[1]
                          , o = r[2]
                          , i = r[3];
                        return 1 === i ? (n = n.toString(16).toLowerCase(),
                        a = a.toString(16).toLowerCase(),
                        o = o.toString(16).toLowerCase(),
                        "#" + (n = 1 === n.length ? "0" + n : n) + (a = 1 === a.length ? "0" + a : a) + (o = 1 === o.length ? "0" + o : o)) : "rgba(" + n + ", " + a + ", " + o + ", " + (i = 0 === i || 1 === i ? i : i.toFixed(8)) + ")"
                    }
                    function s(e) {
                        return t.parseColorString(e, o)
                    }
                    t._testing.parseColor = s,
                    t._testing.colorToString = i;
                    var l = RegExp("(italic|oblique|normal|)\\s*(small-caps|normal|)\\s*(bold|bolder|lighter|[1-9]00|normal|)\\s*([\\d\\.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)(.+)");
                    function c(e) {
                        var t = l.exec(e);
                        if (!t)
                            return null;
                        var r = parseFloat(t[4])
                          , n = 16;
                        switch (t[5]) {
                        case "em":
                        case "rem":
                        case "pc":
                            n = 16 * r;
                            break;
                        case "pt":
                            n = 4 * r / 3;
                            break;
                        case "px":
                            n = r;
                            break;
                        case "in":
                            n = 96 * r;
                            break;
                        case "cm":
                            n = 96 * r / 2.54;
                            break;
                        case "mm":
                            n = 96 / 25.4 * r;
                            break;
                        case "q":
                            n = 96 / 25.4 / 4 * r;
                            break;
                        case "%":
                            n = 16 / 75 * r
                        }
                        return {
                            style: t[1],
                            variant: t[2],
                            weight: t[3],
                            sizePx: n,
                            family: t[6].trim()
                        }
                    }
                    function u() {
                        n || (n = {
                            "Noto Mono": {
                                "*": t.Typeface.GetDefault()
                            },
                            monospace: {
                                "*": t.Typeface.GetDefault()
                            }
                        })
                    }
                    function f(a) {
                        this._canvas = a,
                        this._paint = new t.Paint,
                        this._paint.setAntiAlias(!0),
                        this._paint.setStrokeMiter(10),
                        this._paint.setStrokeCap(t.StrokeCap.Butt),
                        this._paint.setStrokeJoin(t.StrokeJoin.Miter),
                        this._fontString = "10px monospace",
                        this._font = new t.Font(t.Typeface.GetDefault(),10),
                        this._font.setSubpixel(!0),
                        this._strokeStyle = t.BLACK,
                        this._fillStyle = t.BLACK,
                        this._shadowBlur = 0,
                        this._shadowColor = t.TRANSPARENT,
                        this._shadowOffsetX = 0,
                        this._shadowOffsetY = 0,
                        this._globalAlpha = 1,
                        this._strokeWidth = 1,
                        this._lineDashOffset = 0,
                        this._lineDashList = [],
                        this._globalCompositeOperation = t.BlendMode.SrcOver,
                        this._paint.setStrokeWidth(this._strokeWidth),
                        this._paint.setBlendMode(this._globalCompositeOperation),
                        this._currentPath = new t.Path,
                        this._currentTransform = t.Matrix.identity(),
                        this._canvasStateStack = [],
                        this._toCleanUp = [],
                        this._dispose = function() {
                            this._currentPath.delete(),
                            this._paint.delete(),
                            this._font.delete(),
                            this._toCleanUp.forEach(function(e) {
                                e._dispose()
                            })
                        }
                        ,
                        Object.defineProperty(this, "currentTransform", {
                            enumerable: !0,
                            get: function() {
                                return {
                                    a: this._currentTransform[0],
                                    c: this._currentTransform[1],
                                    e: this._currentTransform[2],
                                    b: this._currentTransform[3],
                                    d: this._currentTransform[4],
                                    f: this._currentTransform[5]
                                }
                            },
                            set: function(e) {
                                e.a && this.setTransform(e.a, e.b, e.c, e.d, e.e, e.f)
                            }
                        }),
                        Object.defineProperty(this, "fillStyle", {
                            enumerable: !0,
                            get: function() {
                                return r(this._fillStyle) ? i(this._fillStyle) : this._fillStyle
                            },
                            set: function(e) {
                                "string" == typeof e ? this._fillStyle = s(e) : e._getShader && (this._fillStyle = e)
                            }
                        }),
                        Object.defineProperty(this, "font", {
                            enumerable: !0,
                            get: function() {
                                return this._fontString
                            },
                            set: function(e) {
                                var r, a, o, i, s, l = (a = ((r = i = c(e)).style || "normal") + "|" + (r.variant || "normal") + "|" + (r.weight || "normal"),
                                o = r.family,
                                u(),
                                s = n[o] ? n[o][a] || n[o]["*"] : t.Typeface.GetDefault(),
                                i.typeface = s,
                                i);
                                l && (this._font.setSize(l.sizePx),
                                this._font.setTypeface(l.typeface),
                                this._fontString = e)
                            }
                        }),
                        Object.defineProperty(this, "globalAlpha", {
                            enumerable: !0,
                            get: function() {
                                return this._globalAlpha
                            },
                            set: function(e) {
                                !isFinite(e) || e < 0 || e > 1 || (this._globalAlpha = e)
                            }
                        }),
                        Object.defineProperty(this, "globalCompositeOperation", {
                            enumerable: !0,
                            get: function() {
                                switch (this._globalCompositeOperation) {
                                case t.BlendMode.SrcOver:
                                    return "source-over";
                                case t.BlendMode.DstOver:
                                    return "destination-over";
                                case t.BlendMode.Src:
                                    return "copy";
                                case t.BlendMode.Dst:
                                    return "destination";
                                case t.BlendMode.Clear:
                                    return "clear";
                                case t.BlendMode.SrcIn:
                                    return "source-in";
                                case t.BlendMode.DstIn:
                                    return "destination-in";
                                case t.BlendMode.SrcOut:
                                    return "source-out";
                                case t.BlendMode.DstOut:
                                    return "destination-out";
                                case t.BlendMode.SrcATop:
                                    return "source-atop";
                                case t.BlendMode.DstATop:
                                    return "destination-atop";
                                case t.BlendMode.Xor:
                                    return "xor";
                                case t.BlendMode.Plus:
                                    return "lighter";
                                case t.BlendMode.Multiply:
                                    return "multiply";
                                case t.BlendMode.Screen:
                                    return "screen";
                                case t.BlendMode.Overlay:
                                    return "overlay";
                                case t.BlendMode.Darken:
                                    return "darken";
                                case t.BlendMode.Lighten:
                                    return "lighten";
                                case t.BlendMode.ColorDodge:
                                    return "color-dodge";
                                case t.BlendMode.ColorBurn:
                                    return "color-burn";
                                case t.BlendMode.HardLight:
                                    return "hard-light";
                                case t.BlendMode.SoftLight:
                                    return "soft-light";
                                case t.BlendMode.Difference:
                                    return "difference";
                                case t.BlendMode.Exclusion:
                                    return "exclusion";
                                case t.BlendMode.Hue:
                                    return "hue";
                                case t.BlendMode.Saturation:
                                    return "saturation";
                                case t.BlendMode.Color:
                                    return "color";
                                case t.BlendMode.Luminosity:
                                    return "luminosity"
                                }
                            },
                            set: function(e) {
                                switch (e) {
                                case "source-over":
                                    this._globalCompositeOperation = t.BlendMode.SrcOver;
                                    break;
                                case "destination-over":
                                    this._globalCompositeOperation = t.BlendMode.DstOver;
                                    break;
                                case "copy":
                                    this._globalCompositeOperation = t.BlendMode.Src;
                                    break;
                                case "destination":
                                    this._globalCompositeOperation = t.BlendMode.Dst;
                                    break;
                                case "clear":
                                    this._globalCompositeOperation = t.BlendMode.Clear;
                                    break;
                                case "source-in":
                                    this._globalCompositeOperation = t.BlendMode.SrcIn;
                                    break;
                                case "destination-in":
                                    this._globalCompositeOperation = t.BlendMode.DstIn;
                                    break;
                                case "source-out":
                                    this._globalCompositeOperation = t.BlendMode.SrcOut;
                                    break;
                                case "destination-out":
                                    this._globalCompositeOperation = t.BlendMode.DstOut;
                                    break;
                                case "source-atop":
                                    this._globalCompositeOperation = t.BlendMode.SrcATop;
                                    break;
                                case "destination-atop":
                                    this._globalCompositeOperation = t.BlendMode.DstATop;
                                    break;
                                case "xor":
                                    this._globalCompositeOperation = t.BlendMode.Xor;
                                    break;
                                case "lighter":
                                case "plus-lighter":
                                    this._globalCompositeOperation = t.BlendMode.Plus;
                                    break;
                                case "plus-darker":
                                    throw "plus-darker is not supported";
                                case "multiply":
                                    this._globalCompositeOperation = t.BlendMode.Multiply;
                                    break;
                                case "screen":
                                    this._globalCompositeOperation = t.BlendMode.Screen;
                                    break;
                                case "overlay":
                                    this._globalCompositeOperation = t.BlendMode.Overlay;
                                    break;
                                case "darken":
                                    this._globalCompositeOperation = t.BlendMode.Darken;
                                    break;
                                case "lighten":
                                    this._globalCompositeOperation = t.BlendMode.Lighten;
                                    break;
                                case "color-dodge":
                                    this._globalCompositeOperation = t.BlendMode.ColorDodge;
                                    break;
                                case "color-burn":
                                    this._globalCompositeOperation = t.BlendMode.ColorBurn;
                                    break;
                                case "hard-light":
                                    this._globalCompositeOperation = t.BlendMode.HardLight;
                                    break;
                                case "soft-light":
                                    this._globalCompositeOperation = t.BlendMode.SoftLight;
                                    break;
                                case "difference":
                                    this._globalCompositeOperation = t.BlendMode.Difference;
                                    break;
                                case "exclusion":
                                    this._globalCompositeOperation = t.BlendMode.Exclusion;
                                    break;
                                case "hue":
                                    this._globalCompositeOperation = t.BlendMode.Hue;
                                    break;
                                case "saturation":
                                    this._globalCompositeOperation = t.BlendMode.Saturation;
                                    break;
                                case "color":
                                    this._globalCompositeOperation = t.BlendMode.Color;
                                    break;
                                case "luminosity":
                                    this._globalCompositeOperation = t.BlendMode.Luminosity;
                                    break;
                                default:
                                    return
                                }
                                this._paint.setBlendMode(this._globalCompositeOperation)
                            }
                        }),
                        Object.defineProperty(this, "imageSmoothingEnabled", {
                            enumerable: !0,
                            get: function() {
                                return !0
                            },
                            set: function(e) {}
                        }),
                        Object.defineProperty(this, "imageSmoothingQuality", {
                            enumerable: !0,
                            get: function() {
                                return "high"
                            },
                            set: function(e) {}
                        }),
                        Object.defineProperty(this, "lineCap", {
                            enumerable: !0,
                            get: function() {
                                switch (this._paint.getStrokeCap()) {
                                case t.StrokeCap.Butt:
                                    return "butt";
                                case t.StrokeCap.Round:
                                    return "round";
                                case t.StrokeCap.Square:
                                    return "square"
                                }
                            },
                            set: function(e) {
                                switch (e) {
                                case "butt":
                                    this._paint.setStrokeCap(t.StrokeCap.Butt);
                                    return;
                                case "round":
                                    this._paint.setStrokeCap(t.StrokeCap.Round);
                                    return;
                                case "square":
                                    this._paint.setStrokeCap(t.StrokeCap.Square);
                                    return
                                }
                            }
                        }),
                        Object.defineProperty(this, "lineDashOffset", {
                            enumerable: !0,
                            get: function() {
                                return this._lineDashOffset
                            },
                            set: function(e) {
                                isFinite(e) && (this._lineDashOffset = e)
                            }
                        }),
                        Object.defineProperty(this, "lineJoin", {
                            enumerable: !0,
                            get: function() {
                                switch (this._paint.getStrokeJoin()) {
                                case t.StrokeJoin.Miter:
                                    return "miter";
                                case t.StrokeJoin.Round:
                                    return "round";
                                case t.StrokeJoin.Bevel:
                                    return "bevel"
                                }
                            },
                            set: function(e) {
                                switch (e) {
                                case "miter":
                                    this._paint.setStrokeJoin(t.StrokeJoin.Miter);
                                    return;
                                case "round":
                                    this._paint.setStrokeJoin(t.StrokeJoin.Round);
                                    return;
                                case "bevel":
                                    this._paint.setStrokeJoin(t.StrokeJoin.Bevel);
                                    return
                                }
                            }
                        }),
                        Object.defineProperty(this, "lineWidth", {
                            enumerable: !0,
                            get: function() {
                                return this._paint.getStrokeWidth()
                            },
                            set: function(e) {
                                !(e <= 0) && e && (this._strokeWidth = e,
                                this._paint.setStrokeWidth(e))
                            }
                        }),
                        Object.defineProperty(this, "miterLimit", {
                            enumerable: !0,
                            get: function() {
                                return this._paint.getStrokeMiter()
                            },
                            set: function(e) {
                                !(e <= 0) && e && this._paint.setStrokeMiter(e)
                            }
                        }),
                        Object.defineProperty(this, "shadowBlur", {
                            enumerable: !0,
                            get: function() {
                                return this._shadowBlur
                            },
                            set: function(e) {
                                !(e < 0) && isFinite(e) && (this._shadowBlur = e)
                            }
                        }),
                        Object.defineProperty(this, "shadowColor", {
                            enumerable: !0,
                            get: function() {
                                return i(this._shadowColor)
                            },
                            set: function(e) {
                                this._shadowColor = s(e)
                            }
                        }),
                        Object.defineProperty(this, "shadowOffsetX", {
                            enumerable: !0,
                            get: function() {
                                return this._shadowOffsetX
                            },
                            set: function(e) {
                                isFinite(e) && (this._shadowOffsetX = e)
                            }
                        }),
                        Object.defineProperty(this, "shadowOffsetY", {
                            enumerable: !0,
                            get: function() {
                                return this._shadowOffsetY
                            },
                            set: function(e) {
                                isFinite(e) && (this._shadowOffsetY = e)
                            }
                        }),
                        Object.defineProperty(this, "strokeStyle", {
                            enumerable: !0,
                            get: function() {
                                return i(this._strokeStyle)
                            },
                            set: function(e) {
                                "string" == typeof e ? this._strokeStyle = s(e) : e._getShader && (this._strokeStyle = e)
                            }
                        }),
                        this.arc = function(e, t, r, n, a, o) {
                            b(this._currentPath, e, t, r, r, 0, n, a, o)
                        }
                        ,
                        this.arcTo = function(e, t, r, n, a) {
                            m(this._currentPath, e, t, r, n, a)
                        }
                        ,
                        this.beginPath = function() {
                            this._currentPath.delete(),
                            this._currentPath = new t.Path
                        }
                        ,
                        this.bezierCurveTo = function(e, t, r, n, a, o) {
                            y(this._currentPath, e, t, r, n, a, o)
                        }
                        ,
                        this.clearRect = function(e, r, n, a) {
                            this._paint.setStyle(t.PaintStyle.Fill),
                            this._paint.setBlendMode(t.BlendMode.Clear),
                            this._canvas.drawRect(t.XYWHRect(e, r, n, a), this._paint),
                            this._paint.setBlendMode(this._globalCompositeOperation)
                        }
                        ,
                        this.clip = function(e, r) {
                            "string" == typeof e ? (r = e,
                            e = this._currentPath) : e && e._getPath && (e = e._getPath()),
                            e || (e = this._currentPath);
                            var n = e.copy();
                            r && "evenodd" === r.toLowerCase() ? n.setFillType(t.FillType.EvenOdd) : n.setFillType(t.FillType.Winding),
                            this._canvas.clipPath(n, t.ClipOp.Intersect, !0),
                            n.delete()
                        }
                        ,
                        this.closePath = function() {
                            _(this._currentPath)
                        }
                        ,
                        this.createImageData = function() {
                            if (1 == arguments.length) {
                                var e = arguments[0]
                                  , t = 4 * e.width * e.height;
                                return new p(new Uint8ClampedArray(t),e.width,e.height)
                            }
                            if (2 == arguments.length) {
                                var r = arguments[0]
                                  , n = arguments[1]
                                  , t = 4 * r * n;
                                return new p(new Uint8ClampedArray(t),r,n)
                            }
                            throw "createImageData expects 1 or 2 arguments, got " + arguments.length
                        }
                        ,
                        this.createLinearGradient = function(t, r, n, a) {
                            if (e(arguments)) {
                                var o = new g(t,r,n,a);
                                return this._toCleanUp.push(o),
                                o
                            }
                        }
                        ,
                        this.createPattern = function(e, t) {
                            var r = new P(e,t);
                            return this._toCleanUp.push(r),
                            r
                        }
                        ,
                        this.createRadialGradient = function(t, r, n, a, o, i) {
                            if (e(arguments)) {
                                var s = new S(t,r,n,a,o,i);
                                return this._toCleanUp.push(s),
                                s
                            }
                        }
                        ,
                        this.drawImage = function(e) {
                            e instanceof d && (e = e.getSkImage());
                            var r = this._fillPaint();
                            if (3 == arguments.length || 5 == arguments.length)
                                var n = t.XYWHRect(arguments[1], arguments[2], arguments[3] || e.width(), arguments[4] || e.height())
                                  , a = t.XYWHRect(0, 0, e.width(), e.height());
                            else if (9 == arguments.length)
                                var n = t.XYWHRect(arguments[5], arguments[6], arguments[7], arguments[8])
                                  , a = t.XYWHRect(arguments[1], arguments[2], arguments[3], arguments[4]);
                            else
                                throw "invalid number of args for drawImage, need 3, 5, or 9; got " + arguments.length;
                            this._canvas.drawImageRect(e, a, n, r, !1),
                            r.dispose()
                        }
                        ,
                        this.ellipse = function(e, t, r, n, a, o, i, s) {
                            b(this._currentPath, e, t, r, n, a, o, i, s)
                        }
                        ,
                        this._fillPaint = function() {
                            var e = this._paint.copy();
                            if (e.setStyle(t.PaintStyle.Fill),
                            r(this._fillStyle)) {
                                var n = t.multiplyByAlpha(this._fillStyle, this._globalAlpha);
                                e.setColor(n)
                            } else {
                                var a = this._fillStyle._getShader(this._currentTransform);
                                e.setColor(t.Color(0, 0, 0, this._globalAlpha)),
                                e.setShader(a)
                            }
                            return e.dispose = function() {
                                this.delete()
                            }
                            ,
                            e
                        }
                        ,
                        this.fill = function(e, r) {
                            if ("string" == typeof e ? (r = e,
                            e = this._currentPath) : e && e._getPath && (e = e._getPath()),
                            "evenodd" === r)
                                this._currentPath.setFillType(t.FillType.EvenOdd);
                            else if ("nonzero" !== r && r)
                                throw "invalid fill rule";
                            else
                                this._currentPath.setFillType(t.FillType.Winding);
                            e || (e = this._currentPath);
                            var n = this._fillPaint()
                              , a = this._shadowPaint(n);
                            a && (this._canvas.save(),
                            this._applyShadowOffsetMatrix(),
                            this._canvas.drawPath(e, a),
                            this._canvas.restore(),
                            a.dispose()),
                            this._canvas.drawPath(e, n),
                            n.dispose()
                        }
                        ,
                        this.fillRect = function(e, r, n, a) {
                            var o = this._fillPaint()
                              , i = this._shadowPaint(o);
                            i && (this._canvas.save(),
                            this._applyShadowOffsetMatrix(),
                            this._canvas.drawRect(t.XYWHRect(e, r, n, a), i),
                            this._canvas.restore(),
                            i.dispose()),
                            this._canvas.drawRect(t.XYWHRect(e, r, n, a), o),
                            o.dispose()
                        }
                        ,
                        this.fillText = function(e, r, n, a) {
                            var o = this._fillPaint()
                              , i = t.TextBlob.MakeFromText(e, this._font)
                              , s = this._shadowPaint(o);
                            s && (this._canvas.save(),
                            this._applyShadowOffsetMatrix(),
                            this._canvas.drawTextBlob(i, r, n, s),
                            this._canvas.restore(),
                            s.dispose()),
                            this._canvas.drawTextBlob(i, r, n, o),
                            i.delete(),
                            o.dispose()
                        }
                        ,
                        this.getImageData = function(e, r, n, a) {
                            var o = this._canvas.readPixels(e, r, {
                                width: n,
                                height: a,
                                colorType: t.ColorType.RGBA_8888,
                                alphaType: t.AlphaType.Unpremul,
                                colorSpace: t.ColorSpace.SRGB
                            });
                            return o ? new p(new Uint8ClampedArray(o.buffer),n,a) : null
                        }
                        ,
                        this.getLineDash = function() {
                            return this._lineDashList.slice()
                        }
                        ,
                        this._mapToLocalCoordinates = function(e) {
                            var r = t.Matrix.invert(this._currentTransform);
                            return t.Matrix.mapPoints(r, e),
                            e
                        }
                        ,
                        this.isPointInPath = function(e, r, n) {
                            var a = arguments;
                            if (3 === a.length)
                                var o = this._currentPath;
                            else if (4 === a.length) {
                                var o = a[0];
                                e = a[1],
                                r = a[2],
                                n = a[3]
                            } else
                                throw "invalid arg count, need 3 or 4, got " + a.length;
                            if (!isFinite(e) || !isFinite(r) || !("nonzero" === (n = n || "nonzero") || "evenodd" === n))
                                return !1;
                            var i = this._mapToLocalCoordinates([e, r]);
                            return e = i[0],
                            r = i[1],
                            o.setFillType("nonzero" === n ? t.FillType.Winding : t.FillType.EvenOdd),
                            o.contains(e, r)
                        }
                        ,
                        this.isPointInStroke = function(e, r) {
                            var n = arguments;
                            if (2 === n.length)
                                var a = this._currentPath;
                            else if (3 === n.length) {
                                var a = n[0];
                                e = n[1],
                                r = n[2]
                            } else
                                throw "invalid arg count, need 2 or 3, got " + n.length;
                            if (!isFinite(e) || !isFinite(r))
                                return !1;
                            var o = this._mapToLocalCoordinates([e, r]);
                            e = o[0],
                            r = o[1];
                            var i = a.copy();
                            i.setFillType(t.FillType.Winding),
                            i.stroke({
                                width: this.lineWidth,
                                miter_limit: this.miterLimit,
                                cap: this._paint.getStrokeCap(),
                                join: this._paint.getStrokeJoin(),
                                precision: .3
                            });
                            var s = i.contains(e, r);
                            return i.delete(),
                            s
                        }
                        ,
                        this.lineTo = function(e, t) {
                            C(this._currentPath, e, t)
                        }
                        ,
                        this.measureText = function(e) {
                            let t = this._font.getGlyphIDs(e)
                              , r = this._font.getGlyphWidths(t)
                              , n = 0;
                            for (let e of r)
                                n += e;
                            return {
                                width: n
                            }
                        }
                        ,
                        this.moveTo = function(e, t) {
                            A(this._currentPath, e, t)
                        }
                        ,
                        this.putImageData = function(r, n, a, o, i, s, l) {
                            if (e([n, a, o, i, s, l])) {
                                if (void 0 === o) {
                                    this._canvas.writePixels(r.data, r.width, r.height, n, a);
                                    return
                                }
                                if (o = o || 0,
                                i = i || 0,
                                s = s || r.width,
                                l = l || r.height,
                                s < 0 && (o += s,
                                s = Math.abs(s)),
                                l < 0 && (i += l,
                                l = Math.abs(l)),
                                o < 0 && (s += o,
                                o = 0),
                                i < 0 && (l += i,
                                i = 0),
                                !(s <= 0) && !(l <= 0)) {
                                    var c = t.MakeImage({
                                        width: r.width,
                                        height: r.height,
                                        alphaType: t.AlphaType.Unpremul,
                                        colorType: t.ColorType.RGBA_8888,
                                        colorSpace: t.ColorSpace.SRGB
                                    }, r.data, 4 * r.width)
                                      , u = t.XYWHRect(o, i, s, l)
                                      , f = t.XYWHRect(n + o, a + i, s, l)
                                      , h = t.Matrix.invert(this._currentTransform);
                                    this._canvas.save(),
                                    this._canvas.concat(h),
                                    this._canvas.drawImageRect(c, u, f, null, !1),
                                    this._canvas.restore(),
                                    c.delete()
                                }
                            }
                        }
                        ,
                        this.quadraticCurveTo = function(e, t, r, n) {
                            w(this._currentPath, e, t, r, n)
                        }
                        ,
                        this.rect = function(e, t, r, n) {
                            x(this._currentPath, e, t, r, n)
                        }
                        ,
                        this.resetTransform = function() {
                            this._currentPath.transform(this._currentTransform);
                            var e = t.Matrix.invert(this._currentTransform);
                            this._canvas.concat(e),
                            this._currentTransform = this._canvas.getTotalMatrix()
                        }
                        ,
                        this.restore = function() {
                            var e = this._canvasStateStack.pop();
                            if (e) {
                                var r = t.Matrix.multiply(this._currentTransform, t.Matrix.invert(e.ctm));
                                this._currentPath.transform(r),
                                this._paint.delete(),
                                this._paint = e.paint,
                                this._lineDashList = e.ldl,
                                this._strokeWidth = e.sw,
                                this._strokeStyle = e.ss,
                                this._fillStyle = e.fs,
                                this._shadowOffsetX = e.sox,
                                this._shadowOffsetY = e.soy,
                                this._shadowBlur = e.sb,
                                this._shadowColor = e.shc,
                                this._globalAlpha = e.ga,
                                this._globalCompositeOperation = e.gco,
                                this._lineDashOffset = e.ldo,
                                this._fontString = e.fontstr,
                                this._canvas.restore(),
                                this._currentTransform = this._canvas.getTotalMatrix()
                            }
                        }
                        ,
                        this.rotate = function(e) {
                            if (isFinite(e)) {
                                var r = t.Matrix.rotated(-e);
                                this._currentPath.transform(r),
                                this._canvas.rotate(U(e), 0, 0),
                                this._currentTransform = this._canvas.getTotalMatrix()
                            }
                        }
                        ,
                        this.save = function() {
                            if (this._fillStyle._copy) {
                                var e = this._fillStyle._copy();
                                this._toCleanUp.push(e)
                            } else
                                var e = this._fillStyle;
                            if (this._strokeStyle._copy) {
                                var t = this._strokeStyle._copy();
                                this._toCleanUp.push(t)
                            } else
                                var t = this._strokeStyle;
                            this._canvasStateStack.push({
                                ctm: this._currentTransform.slice(),
                                ldl: this._lineDashList.slice(),
                                sw: this._strokeWidth,
                                ss: t,
                                fs: e,
                                sox: this._shadowOffsetX,
                                soy: this._shadowOffsetY,
                                sb: this._shadowBlur,
                                shc: this._shadowColor,
                                ga: this._globalAlpha,
                                ldo: this._lineDashOffset,
                                gco: this._globalCompositeOperation,
                                paint: this._paint.copy(),
                                fontstr: this._fontString
                            }),
                            this._canvas.save()
                        }
                        ,
                        this.scale = function(r, n) {
                            if (e(arguments)) {
                                var a = t.Matrix.scaled(1 / r, 1 / n);
                                this._currentPath.transform(a),
                                this._canvas.scale(r, n),
                                this._currentTransform = this._canvas.getTotalMatrix()
                            }
                        }
                        ,
                        this.setLineDash = function(e) {
                            for (var t = 0; t < e.length; t++)
                                if (!isFinite(e[t]) || e[t] < 0)
                                    return;
                            e.length % 2 == 1 && Array.prototype.push.apply(e, e),
                            this._lineDashList = e
                        }
                        ,
                        this.setTransform = function(t, r, n, a, o, i) {
                            e(arguments) && (this.resetTransform(),
                            this.transform(t, r, n, a, o, i))
                        }
                        ,
                        this._applyShadowOffsetMatrix = function() {
                            var e = t.Matrix.invert(this._currentTransform);
                            this._canvas.concat(e),
                            this._canvas.concat(t.Matrix.translated(this._shadowOffsetX, this._shadowOffsetY)),
                            this._canvas.concat(this._currentTransform)
                        }
                        ,
                        this._shadowPaint = function(e) {
                            var r = t.multiplyByAlpha(this._shadowColor, this._globalAlpha);
                            if (!t.getColorComponents(r)[3] || !(this._shadowBlur || this._shadowOffsetY || this._shadowOffsetX))
                                return null;
                            var n = e.copy();
                            n.setColor(r);
                            var a = t.MaskFilter.MakeBlur(t.BlurStyle.Normal, this._shadowBlur / 2, !1);
                            return n.setMaskFilter(a),
                            n.dispose = function() {
                                a.delete(),
                                this.delete()
                            }
                            ,
                            n
                        }
                        ,
                        this._strokePaint = function() {
                            var e = this._paint.copy();
                            if (e.setStyle(t.PaintStyle.Stroke),
                            r(this._strokeStyle)) {
                                var n = t.multiplyByAlpha(this._strokeStyle, this._globalAlpha);
                                e.setColor(n)
                            } else {
                                var a = this._strokeStyle._getShader(this._currentTransform);
                                e.setColor(t.Color(0, 0, 0, this._globalAlpha)),
                                e.setShader(a)
                            }
                            if (e.setStrokeWidth(this._strokeWidth),
                            this._lineDashList.length) {
                                var o = t.PathEffect.MakeDash(this._lineDashList, this._lineDashOffset);
                                e.setPathEffect(o)
                            }
                            return e.dispose = function() {
                                o && o.delete(),
                                this.delete()
                            }
                            ,
                            e
                        }
                        ,
                        this.stroke = function(e) {
                            e = e ? e._getPath() : this._currentPath;
                            var t = this._strokePaint()
                              , r = this._shadowPaint(t);
                            r && (this._canvas.save(),
                            this._applyShadowOffsetMatrix(),
                            this._canvas.drawPath(e, r),
                            this._canvas.restore(),
                            r.dispose()),
                            this._canvas.drawPath(e, t),
                            t.dispose()
                        }
                        ,
                        this.strokeRect = function(e, r, n, a) {
                            var o = this._strokePaint()
                              , i = this._shadowPaint(o);
                            i && (this._canvas.save(),
                            this._applyShadowOffsetMatrix(),
                            this._canvas.drawRect(t.XYWHRect(e, r, n, a), i),
                            this._canvas.restore(),
                            i.dispose()),
                            this._canvas.drawRect(t.XYWHRect(e, r, n, a), o),
                            o.dispose()
                        }
                        ,
                        this.strokeText = function(e, r, n, a) {
                            var o = this._strokePaint()
                              , i = t.TextBlob.MakeFromText(e, this._font)
                              , s = this._shadowPaint(o);
                            s && (this._canvas.save(),
                            this._applyShadowOffsetMatrix(),
                            this._canvas.drawTextBlob(i, r, n, s),
                            this._canvas.restore(),
                            s.dispose()),
                            this._canvas.drawTextBlob(i, r, n, o),
                            i.delete(),
                            o.dispose()
                        }
                        ,
                        this.translate = function(r, n) {
                            if (e(arguments)) {
                                var a = t.Matrix.translated(-r, -n);
                                this._currentPath.transform(a),
                                this._canvas.translate(r, n),
                                this._currentTransform = this._canvas.getTotalMatrix()
                            }
                        }
                        ,
                        this.transform = function(e, r, n, a, o, i) {
                            var s = [e, n, o, r, a, i, 0, 0, 1]
                              , l = t.Matrix.invert(s);
                            this._currentPath.transform(l),
                            this._canvas.concat(s),
                            this._currentTransform = this._canvas.getTotalMatrix()
                        }
                        ,
                        this.addHitRegion = function() {}
                        ,
                        this.clearHitRegions = function() {}
                        ,
                        this.drawFocusIfNeeded = function() {}
                        ,
                        this.removeHitRegion = function() {}
                        ,
                        this.scrollPathIntoView = function() {}
                        ,
                        Object.defineProperty(this, "canvas", {
                            value: null,
                            writable: !1
                        })
                    }
                    function h(e) {
                        this._surface = e,
                        this._context = new f(e.getCanvas()),
                        this._toCleanup = [],
                        this.decodeImage = function(e) {
                            var r = t.MakeImageFromEncoded(e);
                            if (!r)
                                throw "Invalid input";
                            return this._toCleanup.push(r),
                            new d(r)
                        }
                        ,
                        this.loadFont = function(e, r) {
                            var a, o, i = t.Typeface.MakeTypefaceFromData(e);
                            if (!i)
                                return null;
                            this._toCleanup.push(i),
                            a = (r.style || "normal") + "|" + (r.variant || "normal") + "|" + (r.weight || "normal"),
                            o = r.family,
                            u(),
                            n[o] || (n[o] = {
                                "*": i
                            }),
                            n[o][a] = i
                        }
                        ,
                        this.makePath2D = function(e) {
                            var t = new T(e);
                            return this._toCleanup.push(t._getPath()),
                            t
                        }
                        ,
                        this.getContext = function(e) {
                            return "2d" === e ? this._context : null
                        }
                        ,
                        this.toDataURL = function(e, r) {
                            this._surface.flush();
                            var n = this._surface.makeImageSnapshot();
                            if (n) {
                                e = e || "image/png";
                                var o = t.ImageFormat.PNG;
                                "image/jpeg" === e && (o = t.ImageFormat.JPEG),
                                r = r || .92;
                                var i = n.encodeToBytes(o, r);
                                if (i)
                                    return n.delete(),
                                    "data:" + e + ";base64," + function(e) {
                                        if (void 0 !== a)
                                            return a.from(e).toString("base64");
                                        for (var t, r = 0, n = e.length, o = ""; r < n; )
                                            t = e.slice(r, Math.min(r + 32768, n)),
                                            o += String.fromCharCode.apply(null, t),
                                            r += 32768;
                                        return btoa(o)
                                    }(i)
                            }
                        }
                        ,
                        this.dispose = function() {
                            this._context._dispose(),
                            this._toCleanup.forEach(function(e) {
                                e.delete()
                            }),
                            this._surface.dispose()
                        }
                    }
                    function d(e) {
                        this._skImage = e,
                        this.width = e.width(),
                        this.height = e.height(),
                        this.naturalWidth = this.width,
                        this.naturalHeight = this.height,
                        this.getSkImage = function() {
                            return e
                        }
                    }
                    function p(e, t, r) {
                        if (!t || 0 === r)
                            throw "invalid dimensions, width and height must be non-zero";
                        if (e.length % 4)
                            throw "arr must be a multiple of 4";
                        r = r || e.length / (4 * t),
                        Object.defineProperty(this, "data", {
                            value: e,
                            writable: !1
                        }),
                        Object.defineProperty(this, "height", {
                            value: r,
                            writable: !1
                        }),
                        Object.defineProperty(this, "width", {
                            value: t,
                            writable: !1
                        })
                    }
                    function g(e, r, n, a) {
                        this._shader = null,
                        this._colors = [],
                        this._pos = [],
                        this.addColorStop = function(e, t) {
                            if (e < 0 || e > 1 || !isFinite(e))
                                throw "offset must be between 0 and 1 inclusively";
                            t = s(t);
                            var r = this._pos.indexOf(e);
                            if (-1 !== r)
                                this._colors[r] = t;
                            else {
                                for (r = 0; r < this._pos.length && !(this._pos[r] > e); r++)
                                    ;
                                this._pos.splice(r, 0, e),
                                this._colors.splice(r, 0, t)
                            }
                        }
                        ,
                        this._copy = function() {
                            var t = new g(e,r,n,a);
                            return t._colors = this._colors.slice(),
                            t._pos = this._pos.slice(),
                            t
                        }
                        ,
                        this._dispose = function() {
                            this._shader && (this._shader.delete(),
                            this._shader = null)
                        }
                        ,
                        this._getShader = function(o) {
                            var i = [e, r, n, a];
                            t.Matrix.mapPoints(o, i);
                            var s = i[0]
                              , l = i[1]
                              , c = i[2]
                              , u = i[3];
                            return this._dispose(),
                            this._shader = t.Shader.MakeLinearGradient([s, l], [c, u], this._colors, this._pos, t.TileMode.Clamp),
                            this._shader
                        }
                    }
                    function m(t, r, n, a, o, i) {
                        if (e([r, n, a, o, i])) {
                            if (i < 0)
                                throw "radii cannot be negative";
                            t.isEmpty() && t.moveTo(r, n),
                            t.arcToTangent(r, n, a, o, i)
                        }
                    }
                    function y(t, r, n, a, o, i, s) {
                        e([r, n, a, o, i, s]) && (t.isEmpty() && t.moveTo(r, n),
                        t.cubicTo(r, n, a, o, i, s))
                    }
                    function _(e) {
                        if (!e.isEmpty()) {
                            var t = e.getBounds();
                            (t[3] - t[1] || t[2] - t[0]) && e.close()
                        }
                    }
                    function v(e, r, n, a, o, i, s) {
                        var l = U(s - i)
                          , c = U(i)
                          , u = t.LTRBRect(r - a, n - o, r + a, n + o);
                        if (1e-5 > Math.abs(Math.abs(l) - 360)) {
                            var f = l / 2;
                            e.arcToOval(u, c, f, !1),
                            e.arcToOval(u, c + f, f, !1);
                            return
                        }
                        e.arcToOval(u, c, l, !1)
                    }
                    function b(r, n, a, o, i, s, l, c, u) {
                        if (e([n, a, o, i, s, l, c])) {
                            if (o < 0 || i < 0)
                                throw "radii cannot be negative";
                            var f = 2 * Math.PI
                              , h = l % f;
                            h < 0 && (h += f);
                            var d = h - l;
                            if (l = h,
                            c += d,
                            !u && c - l >= f ? c = l + f : u && l - c >= f ? c = l - f : !u && l > c ? c = l + (f - (l - c) % f) : u && l < c && (c = l - (f - (c - l) % f)),
                            !s) {
                                v(r, n, a, o, i, l, c);
                                return
                            }
                            var p = t.Matrix.rotated(s, n, a)
                              , g = t.Matrix.rotated(-s, n, a);
                            r.transform(g),
                            v(r, n, a, o, i, l, c),
                            r.transform(p)
                        }
                    }
                    function C(t, r, n) {
                        e([r, n]) && (t.isEmpty() && t.moveTo(r, n),
                        t.lineTo(r, n))
                    }
                    function A(t, r, n) {
                        e([r, n]) && t.moveTo(r, n)
                    }
                    function w(t, r, n, a, o) {
                        e([r, n, a, o]) && (t.isEmpty() && t.moveTo(r, n),
                        t.quadTo(r, n, a, o))
                    }
                    function x(r, n, a, o, i) {
                        var s = t.XYWHRect(n, a, o, i);
                        e(s) && r.addRect(s)
                    }
                    function T(e) {
                        this._path = null,
                        "string" == typeof e ? this._path = t.Path.MakeFromSVGString(e) : e && e._getPath ? this._path = e._getPath().copy() : this._path = new t.Path,
                        this._getPath = function() {
                            return this._path
                        }
                        ,
                        this.addPath = function(e, t) {
                            t || (t = {
                                a: 1,
                                c: 0,
                                e: 0,
                                b: 0,
                                d: 1,
                                f: 0
                            }),
                            this._path.addPath(e._getPath(), [t.a, t.c, t.e, t.b, t.d, t.f])
                        }
                        ,
                        this.arc = function(e, t, r, n, a, o) {
                            b(this._path, e, t, r, r, 0, n, a, o)
                        }
                        ,
                        this.arcTo = function(e, t, r, n, a) {
                            m(this._path, e, t, r, n, a)
                        }
                        ,
                        this.bezierCurveTo = function(e, t, r, n, a, o) {
                            y(this._path, e, t, r, n, a, o)
                        }
                        ,
                        this.closePath = function() {
                            _(this._path)
                        }
                        ,
                        this.ellipse = function(e, t, r, n, a, o, i, s) {
                            b(this._path, e, t, r, n, a, o, i, s)
                        }
                        ,
                        this.lineTo = function(e, t) {
                            C(this._path, e, t)
                        }
                        ,
                        this.moveTo = function(e, t) {
                            A(this._path, e, t)
                        }
                        ,
                        this.quadraticCurveTo = function(e, t, r, n) {
                            w(this._path, e, t, r, n)
                        }
                        ,
                        this.rect = function(e, t, r, n) {
                            x(this._path, e, t, r, n)
                        }
                    }
                    function P(r, n) {
                        switch (this._shader = null,
                        r instanceof d && (r = r.getSkImage()),
                        this._image = r,
                        this._transform = t.Matrix.identity(),
                        "" === n && (n = "repeat"),
                        n) {
                        case "repeat-x":
                            this._tileX = t.TileMode.Repeat,
                            this._tileY = t.TileMode.Decal;
                            break;
                        case "repeat-y":
                            this._tileX = t.TileMode.Decal,
                            this._tileY = t.TileMode.Repeat;
                            break;
                        case "repeat":
                            this._tileX = t.TileMode.Repeat,
                            this._tileY = t.TileMode.Repeat;
                            break;
                        case "no-repeat":
                            this._tileX = t.TileMode.Decal,
                            this._tileY = t.TileMode.Decal;
                            break;
                        default:
                            throw "invalid repetition mode " + n
                        }
                        this.setTransform = function(t) {
                            var r = [t.a, t.c, t.e, t.b, t.d, t.f, 0, 0, 1];
                            e(r) && (this._transform = r)
                        }
                        ,
                        this._copy = function() {
                            var e = new P;
                            return e._tileX = this._tileX,
                            e._tileY = this._tileY,
                            e
                        }
                        ,
                        this._dispose = function() {
                            this._shader && (this._shader.delete(),
                            this._shader = null)
                        }
                        ,
                        this._getShader = function(e) {
                            return this._dispose(),
                            this._shader = this._image.makeShaderCubic(this._tileX, this._tileY, 1 / 3, 1 / 3, this._transform),
                            this._shader
                        }
                    }
                    function S(e, r, n, a, o, i) {
                        this._shader = null,
                        this._colors = [],
                        this._pos = [],
                        this.addColorStop = function(e, t) {
                            if (e < 0 || e > 1 || !isFinite(e))
                                throw "offset must be between 0 and 1 inclusively";
                            t = s(t);
                            var r = this._pos.indexOf(e);
                            if (-1 !== r)
                                this._colors[r] = t;
                            else {
                                for (r = 0; r < this._pos.length && !(this._pos[r] > e); r++)
                                    ;
                                this._pos.splice(r, 0, e),
                                this._colors.splice(r, 0, t)
                            }
                        }
                        ,
                        this._copy = function() {
                            var t = new S(e,r,n,a,o,i);
                            return t._colors = this._colors.slice(),
                            t._pos = this._pos.slice(),
                            t
                        }
                        ,
                        this._dispose = function() {
                            this._shader && (this._shader.delete(),
                            this._shader = null)
                        }
                        ,
                        this._getShader = function(s) {
                            var l = [e, r, a, o];
                            t.Matrix.mapPoints(s, l);
                            var c = l[0]
                              , u = l[1]
                              , f = l[2]
                              , h = l[3]
                              , d = (Math.abs(s[0]) + Math.abs(s[4])) / 2
                              , p = n * d
                              , g = i * d;
                            return this._dispose(),
                            this._shader = t.Shader.MakeTwoPointConicalGradient([c, u], p, [f, h], g, this._colors, this._pos, t.TileMode.Clamp),
                            this._shader
                        }
                    }
                    t._testing.parseFontString = c,
                    t.MakeCanvas = function(e, r) {
                        var n = t.MakeSurface(e, r);
                        return n ? new h(n) : null
                    }
                    ,
                    t.ImageData = function() {
                        if (2 == arguments.length) {
                            var e = arguments[0]
                              , t = arguments[1];
                            return new p(new Uint8ClampedArray(4 * e * t),e,t)
                        }
                        if (3 == arguments.length) {
                            var r = arguments[0];
                            if (r.prototype.constructor !== Uint8ClampedArray)
                                throw "bytes must be given as a Uint8ClampedArray";
                            var e = arguments[1]
                              , t = arguments[2];
                            if (r % 4)
                                throw "bytes must be given in a multiple of 4";
                            if (r % e)
                                throw "bytes must divide evenly by width";
                            if (t && t !== r / (4 * e))
                                throw "invalid height given";
                            return t = r / (4 * e),
                            new p(r,e,t)
                        }
                        throw "invalid number of arguments - takes 2 or 3, saw " + arguments.length
                    }
                }()
            }(e);
            var t, i, s, l, c, u, f, h, d, p, g, m, y, _, v, b, C, A, w, x, T, P, S, F, k, E, M, I = Object.assign({}, e), B = "./this.program", R = (e, t) => {
                throw t
            }
            , L = "function" == typeof importScripts, O = "object" == typeof o && "object" == typeof o.versions && "string" == typeof o.versions.node;
            if (e.ENVIRONMENT)
                throw Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -sENVIRONMENT=web or -sENVIRONMENT=node)");
            var D = "";
            if (O) {
                if (void 0 === o || !o.release || "node" !== o.release.name)
                    throw Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");
                var U = o.versions.node
                  , G = U.split(".").slice(0, 3);
                if ((G = 1e4 * G[0] + 100 * G[1] + 1 * G[2].split("-")[0]) < 16e4)
                    throw Error("This emscripten-generated code requires node v16.0.0 (detected v" + U + ")");
                var W = r(73289)
                  , H = r(15153);
                D = L ? H.dirname(D) + "/" : "//",
                l = (e, t) => (e = ei(e) ? new URL(e) : H.normalize(e),
                W.readFileSync(e, t ? void 0 : "utf8")),
                u = e => {
                    var t = l(e, !0);
                    return t.buffer || (t = new Uint8Array(t)),
                    z(t.buffer),
                    t
                }
                ,
                c = function(e, t, r) {
                    let n = !(arguments.length > 3) || void 0 === arguments[3] || arguments[3];
                    e = ei(e) ? new URL(e) : H.normalize(e),
                    W.readFile(e, n ? void 0 : "utf8", (e, a) => {
                        e ? r(e) : t(n ? a.buffer : a)
                    }
                    )
                }
                ,
                !e.thisProgram && o.argv.length > 1 && (B = o.argv[1].replace(/\\/g, "/")),
                o.argv.slice(2),
                R = (e, t) => {
                    throw o.exitCode = e,
                    t
                }
                ,
                e.inspect = () => "[Emscripten Module object]"
            } else
                L ? D = self.location.href : "undefined" != typeof document && document.currentScript && (D = document.currentScript.src),
                n && (D = n),
                D = 0 !== D.indexOf("blob:") ? D.substr(0, D.replace(/[?#].*/, "").lastIndexOf("/") + 1) : "",
                l = e => {
                    var t = new XMLHttpRequest;
                    return t.open("GET", e, !1),
                    t.send(null),
                    t.responseText
                }
                ,
                L && (u = e => {
                    var t = new XMLHttpRequest;
                    return t.open("GET", e, !1),
                    t.responseType = "arraybuffer",
                    t.send(null),
                    new Uint8Array(t.response)
                }
                ),
                c = (e, t, r) => {
                    var n = new XMLHttpRequest;
                    n.open("GET", e, !0),
                    n.responseType = "arraybuffer",
                    n.onload = () => {
                        if (200 == n.status || 0 == n.status && n.response) {
                            t(n.response);
                            return
                        }
                        r()
                    }
                    ,
                    n.onerror = r,
                    n.send(null)
                }
                ;
            var j = e.print || console.log.bind(console)
              , $ = e.printErr || console.error.bind(console);
            Object.assign(e, I),
            I = null,
            Object.getOwnPropertyDescriptor(e, t = "fetchSettings") && en("`Module.".concat(t, "` was supplied but `").concat(t, "` not included in INCOMING_MODULE_JS_API")),
            e.arguments && e.arguments,
            eu("arguments", "arguments_"),
            e.thisProgram && (B = e.thisProgram),
            eu("thisProgram", "thisProgram"),
            e.quit && (R = e.quit),
            eu("quit", "quit_"),
            z(void 0 === e.memoryInitializerPrefixURL, "Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),
            z(void 0 === e.pthreadMainPrefixURL, "Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),
            z(void 0 === e.cdInitializerPrefixURL, "Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),
            z(void 0 === e.filePackagePrefixURL, "Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),
            z(void 0 === e.read, "Module.read option was removed (modify read_ in JS)"),
            z(void 0 === e.readAsync, "Module.readAsync option was removed (modify readAsync in JS)"),
            z(void 0 === e.readBinary, "Module.readBinary option was removed (modify readBinary in JS)"),
            z(void 0 === e.setWindowTitle, "Module.setWindowTitle option was removed (modify setWindowTitle in JS)"),
            z(void 0 === e.TOTAL_MEMORY, "Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),
            eu("asm", "wasmExports"),
            eu("read", "read_"),
            eu("readAsync", "readAsync"),
            eu("readBinary", "readBinary"),
            eu("setWindowTitle", "setWindowTitle"),
            z(!0, "shell environment detected but not enabled at build time.  Add 'shell' to `-sENVIRONMENT` to enable."),
            e.wasmBinary && (f = e.wasmBinary),
            eu("wasmBinary", "wasmBinary");
            var N = e.noExitRuntime || !0;
            eu("noExitRuntime", "noExitRuntime"),
            "object" != typeof WebAssembly && en("no native wasm support detected");
            var V = !1;
            function z(e, t) {
                e || en("Assertion failed" + (t ? ": " + t : ""))
            }
            function Y() {
                var t = h.buffer;
                e.HEAP8 = p = new Int8Array(t),
                e.HEAP16 = m = new Int16Array(t),
                e.HEAP32 = _ = new Int32Array(t),
                e.HEAPU8 = g = new Uint8Array(t),
                e.HEAPU16 = y = new Uint16Array(t),
                e.HEAPU32 = v = new Uint32Array(t),
                e.HEAPF32 = b = new Float32Array(t),
                e.HEAPF64 = C = new Float64Array(t)
            }
            function X() {
                if (!V) {
                    var e = rf();
                    0 == e && (e += 4);
                    var t = v[e >> 2]
                      , r = v[e + 4 >> 2];
                    (34821223 != t || 2310721022 != r) && en("Stack overflow! Stack cookie has been overwritten at ".concat(eC(e), ", expected hex dwords 0x89BACDFE and 0x2135467, but received ").concat(eC(r), " ").concat(eC(t))),
                    1668509029 != v[0] && en("Runtime error: The application has corrupted its heap memory area (address zero)!")
                }
            }
            z(!e.STACK_SIZE, "STACK_SIZE can no longer be set at runtime.  Use -sSTACK_SIZE at link time"),
            z("undefined" != typeof Int32Array && "undefined" != typeof Float64Array && void 0 != Int32Array.prototype.subarray && void 0 != Int32Array.prototype.set, "JS engine does not provide full typed array support"),
            z(!e.wasmMemory, "Use of `wasmMemory` detected.  Use -sIMPORTED_MEMORY to define wasmMemory externally"),
            z(!e.INITIAL_MEMORY, "Detected runtime INITIAL_MEMORY setting.  Use -sIMPORTED_MEMORY to define wasmMemory dynamically"),
            function() {
                var e = new Int16Array(1)
                  , t = new Int8Array(e.buffer);
                if (e[0] = 25459,
                115 !== t[0] || 99 !== t[1])
                    throw "Runtime error: expected the system to be little-endian! (Run with -sSUPPORT_BIG_ENDIAN to bypass)"
            }();
            var q = []
              , J = []
              , Q = []
              , K = !1;
            z(Math.imul, "This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),
            z(Math.fround, "This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),
            z(Math.clz32, "This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),
            z(Math.trunc, "This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");
            var Z = 0
              , ee = null
              , et = null
              , er = {};
            function en(t) {
                e.onAbort && e.onAbort(t),
                $(t = "Aborted(" + t + ")"),
                V = !0;
                var r = new WebAssembly.RuntimeError(t);
                throw s(r),
                r
            }
            var ea = {
                error() {
                    en("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with -sFORCE_FILESYSTEM")
                },
                init() {
                    ea.error()
                },
                createDataFile() {
                    ea.error()
                },
                createPreloadedFile() {
                    ea.error()
                },
                createLazyFile() {
                    ea.error()
                },
                open() {
                    ea.error()
                },
                mkdev() {
                    ea.error()
                },
                registerDevice() {
                    ea.error()
                },
                analyzePath() {
                    ea.error()
                },
                ErrnoError() {
                    ea.error()
                }
            };
            function eo(e) {
                return e.startsWith("data:application/octet-stream;base64,")
            }
            function ei(e) {
                return e.startsWith("file://")
            }
            function es(e) {
                return function() {
                    z(K, "native function `".concat(e, "` called before runtime initialization"));
                    var t = d[e];
                    return z(t, "exported native function `".concat(e, "` not found")),
                    t.apply(null, arguments)
                }
            }
            function el(e) {
                if (e == w && f)
                    return new Uint8Array(f);
                if (u)
                    return u(e);
                throw "both async and sync fetching of the wasm failed"
            }
            function ec(e, t, r) {
                return (function(e) {
                    if (!f) {
                        if ("function" == typeof fetch && !ei(e))
                            return fetch(e, {
                                credentials: "same-origin"
                            }).then(t => {
                                if (!t.ok)
                                    throw "failed to load wasm binary file at '" + e + "'";
                                return t.arrayBuffer()
                            }
                            ).catch( () => el(e));
                        if (c)
                            return new Promise( (t, r) => {
                                c(e, e => t(new Uint8Array(e)), r)
                            }
                            )
                    }
                    return Promise.resolve().then( () => el(e))
                }
                )(e).then(e => WebAssembly.instantiate(e, t)).then(e => e).then(r, e => {
                    $("failed to asynchronously prepare wasm: " + e),
                    ei(w) && $("warning: Loading from a file URI (" + w + ") is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing"),
                    en(e)
                }
                )
            }
            function eu(t, r) {
                let n = !(arguments.length > 2) || void 0 === arguments[2] || arguments[2];
                Object.getOwnPropertyDescriptor(e, t) || Object.defineProperty(e, t, {
                    configurable: !0,
                    get() {
                        en("`Module.".concat(t, "` has been replaced by `").concat(r, "`") + (n ? " (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)" : ""))
                    }
                })
            }
            function ef(e) {
                return "FS_createPath" === e || "FS_createDataFile" === e || "FS_createPreloadedFile" === e || "FS_unlink" === e || "addRunDependency" === e || "FS_createLazyFile" === e || "FS_createDevice" === e || "removeRunDependency" === e
            }
            function eh(t) {
                Object.getOwnPropertyDescriptor(e, t) || Object.defineProperty(e, t, {
                    configurable: !0,
                    get() {
                        var e = "'" + t + "' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the Emscripten FAQ)";
                        ef(t) && (e += ". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),
                        en(e)
                    }
                })
            }
            function ed(e) {
                this.name = "ExitStatus",
                this.message = "Program terminated with exit(".concat(e, ")"),
                this.status = e
            }
            e.FS_createDataFile = ea.createDataFile,
            e.FS_createPreloadedFile = ea.createPreloadedFile,
            eo(w = "canvaskit.wasm") || (S = w,
            w = e.locateFile ? e.locateFile(S, D) : D + S),
            F = "buffer",
            "undefined" != typeof globalThis && Object.defineProperty(globalThis, F, {
                configurable: !0,
                get() {
                    eA("`" + F + "` is not longer defined by emscripten. Please use HEAP8.buffer or wasmMemory.buffer")
                }
            });
            var ep = t => {
                for (; t.length > 0; )
                    t.shift()(e)
            }
              , eg = e => {
                for (var t = 0, r = 0; r < e.length; ++r) {
                    var n = e.charCodeAt(r);
                    n <= 127 ? t++ : n <= 2047 ? t += 2 : n >= 55296 && n <= 57343 ? (t += 4,
                    ++r) : t += 3
                }
                return t
            }
              , em = (e, t, r, n) => {
                if (z("string" == typeof e),
                !(n > 0))
                    return 0;
                for (var a = r, o = r + n - 1, i = 0; i < e.length; ++i) {
                    var s = e.charCodeAt(i);
                    if (s >= 55296 && s <= 57343 && (s = 65536 + ((1023 & s) << 10) | 1023 & e.charCodeAt(++i)),
                    s <= 127) {
                        if (r >= o)
                            break;
                        t[r++] = s
                    } else if (s <= 2047) {
                        if (r + 1 >= o)
                            break;
                        t[r++] = 192 | s >> 6,
                        t[r++] = 128 | 63 & s
                    } else if (s <= 65535) {
                        if (r + 2 >= o)
                            break;
                        t[r++] = 224 | s >> 12,
                        t[r++] = 128 | s >> 6 & 63,
                        t[r++] = 128 | 63 & s
                    } else {
                        if (r + 3 >= o)
                            break;
                        s > 1114111 && eA("Invalid Unicode code point " + eC(s) + " encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF)."),
                        t[r++] = 240 | s >> 18,
                        t[r++] = 128 | s >> 12 & 63,
                        t[r++] = 128 | s >> 6 & 63,
                        t[r++] = 128 | 63 & s
                    }
                }
                return t[r] = 0,
                r - a
            }
              , ey = (e, t, r) => (z("number" == typeof r, "stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),
            em(e, g, t, r))
              , e_ = "undefined" != typeof TextDecoder ? new TextDecoder("utf8") : void 0
              , ev = (e, t, r) => {
                for (var n = t + r, a = t; e[a] && !(a >= n); )
                    ++a;
                if (a - t > 16 && e.buffer && e_)
                    return e_.decode(e.subarray(t, a));
                for (var o = ""; t < a; ) {
                    var i = e[t++];
                    if (!(128 & i)) {
                        o += String.fromCharCode(i);
                        continue
                    }
                    var s = 63 & e[t++];
                    if ((224 & i) == 192) {
                        o += String.fromCharCode((31 & i) << 6 | s);
                        continue
                    }
                    var l = 63 & e[t++];
                    if ((240 & i) == 224 ? i = (15 & i) << 12 | s << 6 | l : ((248 & i) != 240 && eA("Invalid UTF-8 leading byte " + eC(i) + " encountered when deserializing a UTF-8 string in wasm memory to a JS string!"),
                    i = (7 & i) << 18 | s << 12 | l << 6 | 63 & e[t++]),
                    i < 65536)
                        o += String.fromCharCode(i);
                    else {
                        var c = i - 65536;
                        o += String.fromCharCode(55296 | c >> 10, 56320 | 1023 & c)
                    }
                }
                return o
            }
              , eb = (e, t) => (z("number" == typeof e),
            e ? ev(g, e, t) : "")
              , eC = e => (z("number" == typeof e),
            "0x" + (e >>>= 0).toString(16).padStart(8, "0"))
              , eA = e => {
                eA.shown || (eA.shown = {}),
                eA.shown[e] || (eA.shown[e] = 1,
                O && (e = "warning: " + e),
                $(e))
            }
              , ew = {
                varargs: void 0,
                get: () => (z(void 0 != ew.varargs),
                ew.varargs += 4,
                _[ew.varargs - 4 >> 2]),
                getStr: e => eb(e)
            }
              , ex = {};
            function eT(e) {
                for (; e.length; ) {
                    var t = e.pop();
                    e.pop()(t)
                }
            }
            function eP(e) {
                return this.fromWireType(_[e >> 2])
            }
            var eS = {}
              , eF = {}
              , ek = {}
              , eE = void 0;
            function eM(e) {
                throw new eE(e)
            }
            function eI(e, t, r) {
                function n(t) {
                    var n = r(t);
                    n.length !== e.length && eM("Mismatched type converter count");
                    for (var a = 0; a < e.length; ++a)
                        eU(e[a], n[a])
                }
                e.forEach(function(e) {
                    ek[e] = t
                });
                var a = Array(t.length)
                  , o = []
                  , i = 0;
                t.forEach( (e, t) => {
                    eF.hasOwnProperty(e) ? a[t] = eF[e] : (o.push(e),
                    eS.hasOwnProperty(e) || (eS[e] = []),
                    eS[e].push( () => {
                        a[t] = eF[e],
                        ++i === o.length && n(a)
                    }
                    ))
                }
                ),
                0 === o.length && n(a)
            }
            function eB(e) {
                switch (e) {
                case 1:
                    return 0;
                case 2:
                    return 1;
                case 4:
                    return 2;
                case 8:
                    return 3;
                default:
                    throw TypeError("Unknown type size: ".concat(e))
                }
            }
            var eR = void 0;
            function eL(e) {
                for (var t = "", r = e; g[r]; )
                    t += eR[g[r++]];
                return t
            }
            var eO = void 0;
            function eD(e) {
                throw new eO(e)
            }
            function eU(e, t) {
                let r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                if (!("argPackAdvance"in t))
                    throw TypeError("registerType registeredInstance requires argPackAdvance");
                return function(e, t) {
                    let r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                    var n = t.name;
                    if (e || eD('type "'.concat(n, '" must have a positive integer typeid pointer')),
                    eF.hasOwnProperty(e)) {
                        if (r.ignoreDuplicateRegistrations)
                            return;
                        eD("Cannot register type '".concat(n, "' twice"))
                    }
                    if (eF[e] = t,
                    delete ek[e],
                    eS.hasOwnProperty(e)) {
                        var a = eS[e];
                        delete eS[e],
                        a.forEach(e => e())
                    }
                }(e, t, r)
            }
            function eG(e) {
                eD(e.$$.ptrType.registeredClass.name + " instance already deleted")
            }
            var eW = !1;
            function eH(e) {}
            function ej(e) {
                e.count.value -= 1,
                0 === e.count.value && (e.smartPtr ? e.smartPtrType.rawDestructor(e.smartPtr) : e.ptrType.registeredClass.rawDestructor(e.ptr))
            }
            var e$ = {}
              , eN = [];
            function eV() {
                for (; eN.length; ) {
                    var e = eN.pop();
                    e.$$.deleteScheduled = !1,
                    e.delete()
                }
            }
            var ez = void 0
              , eY = {};
            function eX(e, t) {
                return t.ptrType && t.ptr || eM("makeClassHandle requires ptr and ptrType"),
                !!t.smartPtrType != !!t.smartPtr && eM("Both smartPtrType and smartPtr must be specified"),
                t.count = {
                    value: 1
                },
                eJ(Object.create(e, {
                    $$: {
                        value: t
                    }
                }))
            }
            function eq(e) {
                var t, r = this.getPointee(e);
                if (!r)
                    return this.destructor(e),
                    null;
                var n = eY[function(e, t) {
                    for (void 0 === t && eD("ptr should not be undefined"); e.baseClass; )
                        t = e.upcast(t),
                        e = e.baseClass;
                    return t
                }(this.registeredClass, r)];
                if (void 0 !== n) {
                    if (0 === n.$$.count.value)
                        return n.$$.ptr = r,
                        n.$$.smartPtr = e,
                        n.clone();
                    var a = n.clone();
                    return this.destructor(e),
                    a
                }
                function o() {
                    return this.isSmartPointer ? eX(this.registeredClass.instancePrototype, {
                        ptrType: this.pointeeType,
                        ptr: r,
                        smartPtrType: this,
                        smartPtr: e
                    }) : eX(this.registeredClass.instancePrototype, {
                        ptrType: this,
                        ptr: e
                    })
                }
                var i = e$[this.registeredClass.getActualType(r)];
                if (!i)
                    return o.call(this);
                t = this.isConst ? i.constPointerType : i.pointerType;
                var s = function e(t, r, n) {
                    if (r === n)
                        return t;
                    if (void 0 === n.baseClass)
                        return null;
                    var a = e(t, r, n.baseClass);
                    return null === a ? null : n.downcast(a)
                }(r, this.registeredClass, t.registeredClass);
                return null === s ? o.call(this) : this.isSmartPointer ? eX(t.registeredClass.instancePrototype, {
                    ptrType: t,
                    ptr: s,
                    smartPtrType: this,
                    smartPtr: e
                }) : eX(t.registeredClass.instancePrototype, {
                    ptrType: t,
                    ptr: s
                })
            }
            var eJ = function(e) {
                return "undefined" == typeof FinalizationRegistry ? (eJ = e => e,
                e) : (eW = new FinalizationRegistry(e => {
                    console.warn(e.leakWarning.stack.replace(/^Error: /, "")),
                    ej(e.$$)
                }
                ),
                eJ = e => {
                    var t = e.$$;
                    if (t.smartPtr) {
                        var r = {
                            $$: t
                        }
                          , n = t.ptrType.registeredClass;
                        r.leakWarning = Error("Embind found a leaked C++ instance ".concat(n.name, " <").concat(eC(t.ptr), ">.\n") + "We'll free it automatically in this case, but this functionality is not reliable across various environments.\nMake sure to invoke .delete() manually once you're done with the instance instead.\nOriginally allocated"),
                        "captureStackTrace"in Error && Error.captureStackTrace(r.leakWarning, eq),
                        eW.register(e, r, e)
                    }
                    return e
                }
                ,
                eH = e => eW.unregister(e),
                eJ(e))
            };
            function eQ() {}
            function eK(e) {
                if (void 0 === e)
                    return "_unknown";
                var t = (e = e.replace(/[^a-zA-Z0-9_]/g, "$")).charCodeAt(0);
                return t >= 48 && t <= 57 ? "_".concat(e) : e
            }
            function eZ(e, t) {
                return ({
                    [e = eK(e)]: function() {
                        return t.apply(this, arguments)
                    }
                })[e]
            }
            function e1(e, t, r) {
                if (void 0 === e[t].overloadTable) {
                    var n = e[t];
                    e[t] = function() {
                        return e[t].overloadTable.hasOwnProperty(arguments.length) || eD("Function '".concat(r, "' called with an invalid number of arguments (").concat(arguments.length, ") - expects one of (").concat(e[t].overloadTable, ")!")),
                        e[t].overloadTable[arguments.length].apply(this, arguments)
                    }
                    ,
                    e[t].overloadTable = [],
                    e[t].overloadTable[n.argCount] = n
                }
            }
            function e2(t, r, n) {
                e.hasOwnProperty(t) ? ((void 0 === n || void 0 !== e[t].overloadTable && void 0 !== e[t].overloadTable[n]) && eD("Cannot register public name '".concat(t, "' twice")),
                e1(e, t, t),
                e.hasOwnProperty(n) && eD("Cannot register multiple overloads of a function with the same number of arguments (".concat(n, ")!")),
                e[t].overloadTable[n] = r) : (e[t] = r,
                void 0 !== n && (e[t].numArguments = n))
            }
            function e0(e, t, r, n, a, o, i, s) {
                this.name = e,
                this.constructor = t,
                this.instancePrototype = r,
                this.rawDestructor = n,
                this.baseClass = a,
                this.getActualType = o,
                this.upcast = i,
                this.downcast = s,
                this.pureVirtualFunctions = []
            }
            function e3(e, t, r) {
                for (; t !== r; )
                    t.upcast || eD("Expected null or instance of ".concat(r.name, ", got an instance of ").concat(t.name)),
                    e = t.upcast(e),
                    t = t.baseClass;
                return e
            }
            function e4(e, t) {
                if (null === t)
                    return this.isReference && eD("null is not a valid ".concat(this.name)),
                    0;
                t.$$ || eD('Cannot pass "'.concat(tp(t), '" as a ').concat(this.name)),
                t.$$.ptr || eD("Cannot pass deleted object as a pointer of type ".concat(this.name));
                var r = t.$$.ptrType.registeredClass;
                return e3(t.$$.ptr, r, this.registeredClass)
            }
            function e8(e, t) {
                if (null === t)
                    return (this.isReference && eD("null is not a valid ".concat(this.name)),
                    this.isSmartPointer) ? (r = this.rawConstructor(),
                    null !== e && e.push(this.rawDestructor, r),
                    r) : 0;
                t.$$ || eD('Cannot pass "'.concat(tp(t), '" as a ').concat(this.name)),
                t.$$.ptr || eD("Cannot pass deleted object as a pointer of type ".concat(this.name)),
                !this.isConst && t.$$.ptrType.isConst && eD("Cannot convert argument of type ".concat(t.$$.smartPtrType ? t.$$.smartPtrType.name : t.$$.ptrType.name, " to parameter type ").concat(this.name));
                var r, n = t.$$.ptrType.registeredClass;
                if (r = e3(t.$$.ptr, n, this.registeredClass),
                this.isSmartPointer)
                    switch (void 0 === t.$$.smartPtr && eD("Passing raw pointer to smart pointer is illegal"),
                    this.sharingPolicy) {
                    case 0:
                        t.$$.smartPtrType === this ? r = t.$$.smartPtr : eD("Cannot convert argument of type ".concat(t.$$.smartPtrType ? t.$$.smartPtrType.name : t.$$.ptrType.name, " to parameter type ").concat(this.name));
                        break;
                    case 1:
                        r = t.$$.smartPtr;
                        break;
                    case 2:
                        if (t.$$.smartPtrType === this)
                            r = t.$$.smartPtr;
                        else {
                            var a = t.clone();
                            r = this.rawShare(r, th.toHandle(function() {
                                a.delete()
                            })),
                            null !== e && e.push(this.rawDestructor, r)
                        }
                        break;
                    default:
                        eD("Unsupporting sharing policy")
                    }
                return r
            }
            function e6(e, t) {
                if (null === t)
                    return this.isReference && eD("null is not a valid ".concat(this.name)),
                    0;
                t.$$ || eD('Cannot pass "'.concat(tp(t), '" as a ').concat(this.name)),
                t.$$.ptr || eD("Cannot pass deleted object as a pointer of type ".concat(this.name)),
                t.$$.ptrType.isConst && eD("Cannot convert argument of type ".concat(t.$$.ptrType.name, " to parameter type ").concat(this.name));
                var r = t.$$.ptrType.registeredClass;
                return e3(t.$$.ptr, r, this.registeredClass)
            }
            function e5(e, t, r, n, a, o, i, s, l, c, u) {
                this.name = e,
                this.registeredClass = t,
                this.isReference = r,
                this.isConst = n,
                this.isSmartPointer = a,
                this.pointeeType = o,
                this.sharingPolicy = i,
                this.rawGetPointee = s,
                this.rawConstructor = l,
                this.rawShare = c,
                this.rawDestructor = u,
                a || void 0 !== t.baseClass ? this.toWireType = e8 : (n ? this.toWireType = e4 : this.toWireType = e6,
                this.destructorFunction = null)
            }
            function e7(t, r, n) {
                e.hasOwnProperty(t) || eM("Replacing nonexistant public symbol"),
                void 0 !== e[t].overloadTable && void 0 !== n ? e[t].overloadTable[n] = r : (e[t] = r,
                e[t].argCount = n)
            }
            var e9 = (t, r, n) => {
                z("dynCall_" + t in e, "bad function pointer type - dynCall function not found for sig '".concat(t, "'")),
                n && n.length ? z(n.length === t.substring(1).replace(/j/g, "--").length) : z(1 == t.length);
                var a = e["dynCall_" + t];
                return n && n.length ? a.apply(null, [r].concat(n)) : a.call(null, r)
            }
              , te = e => A.get(e)
              , tt = (e, t, r) => e.includes("j") ? e9(e, t, r) : (z(te(t), "missing table entry in dynCall: ".concat(t)),
            te(t).apply(null, r))
              , tr = (e, t) => {
                z(e.includes("j") || e.includes("p"), "getDynCaller should only be called with i64 sigs");
                var r = [];
                return function() {
                    return r.length = 0,
                    Object.assign(r, arguments),
                    tt(e, t, r)
                }
            }
            ;
            function tn(e, t) {
                var r = (e = eL(e)).includes("j") ? tr(e, t) : te(t);
                return "function" != typeof r && eD("unknown function pointer with signature ".concat(e, ": ").concat(t)),
                r
            }
            var ta = void 0;
            function to(e) {
                var t = rl(e)
                  , r = eL(t);
                return ri(t),
                r
            }
            function ti(e, t) {
                var r = []
                  , n = {};
                throw t.forEach(function e(t) {
                    if (!n[t] && !eF[t]) {
                        if (ek[t]) {
                            ek[t].forEach(e);
                            return
                        }
                        r.push(t),
                        n[t] = !0
                    }
                }),
                new ta("".concat(e, ": ") + r.map(to).join([", "]))
            }
            function ts(e, t, r, n, a, o) {
                var i = t.length;
                i < 2 && eD("argTypes array size mismatch! Must at least get return value and 'this' types!"),
                z(!o, "Async bindings are only supported with JSPI.");
                for (var s = null !== t[1] && null !== r, l = !1, c = 1; c < t.length; ++c)
                    if (null !== t[c] && void 0 === t[c].destructorFunction) {
                        l = !0;
                        break
                    }
                var u = "void" !== t[0].name
                  , f = i - 2
                  , h = Array(f)
                  , d = []
                  , p = [];
                return function() {
                    arguments.length !== f && eD("function ".concat(e, " called with ").concat(arguments.length, " arguments, expected ").concat(f, " args!")),
                    p.length = 0,
                    d.length = s ? 2 : 1,
                    d[0] = a,
                    s && (r = t[1].toWireType(p, this),
                    d[1] = r);
                    for (var r, o = 0; o < f; ++o)
                        h[o] = t[o + 2].toWireType(p, arguments[o]),
                        d.push(h[o]);
                    return function(e) {
                        if (l)
                            eT(p);
                        else
                            for (var n = s ? 1 : 2; n < t.length; n++) {
                                var a = 1 === n ? r : h[n - 2];
                                null !== t[n].destructorFunction && t[n].destructorFunction(a)
                            }
                        if (u)
                            return t[0].fromWireType(e)
                    }(n.apply(null, d))
                }
            }
            function tl(e, t) {
                for (var r = [], n = 0; n < e; n++)
                    r.push(v[t + 4 * n >> 2]);
                return r
            }
            function tc() {
                this.allocated = [void 0],
                this.freelist = []
            }
            var tu = new tc;
            function tf(e) {
                e >= tu.reserved && 0 == --tu.get(e).refcount && tu.free(e)
            }
            var th = {
                toValue: e => (e || eD("Cannot use deleted val. handle = " + e),
                tu.get(e).value),
                toHandle: e => {
                    switch (e) {
                    case void 0:
                        return 1;
                    case null:
                        return 2;
                    case !0:
                        return 3;
                    case !1:
                        return 4;
                    default:
                        return tu.allocate({
                            refcount: 1,
                            value: e
                        })
                    }
                }
            };
            function td(e, t) {
                var r = eF[e];
                return void 0 === r && eD(t + " has unknown type " + to(e)),
                r
            }
            function tp(e) {
                if (null === e)
                    return "null";
                var t = typeof e;
                return "object" === t || "array" === t || "function" === t ? e.toString() : "" + e
            }
            var tg = "undefined" != typeof TextDecoder ? new TextDecoder("utf-16le") : void 0
              , tm = (e, t) => {
                z(e % 2 == 0, "Pointer passed to UTF16ToString must be aligned to two bytes!");
                for (var r = e, n = r >> 1, a = n + t / 2; !(n >= a) && y[n]; )
                    ++n;
                if ((r = n << 1) - e > 32 && tg)
                    return tg.decode(g.subarray(e, r));
                for (var o = "", i = 0; !(i >= t / 2); ++i) {
                    var s = m[e + 2 * i >> 1];
                    if (0 == s)
                        break;
                    o += String.fromCharCode(s)
                }
                return o
            }
              , ty = (e, t, r) => {
                if (z(t % 2 == 0, "Pointer passed to stringToUTF16 must be aligned to two bytes!"),
                z("number" == typeof r, "stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),
                void 0 === r && (r = 2147483647),
                r < 2)
                    return 0;
                for (var n = t, a = (r -= 2) < 2 * e.length ? r / 2 : e.length, o = 0; o < a; ++o) {
                    var i = e.charCodeAt(o);
                    m[t >> 1] = i,
                    t += 2
                }
                return m[t >> 1] = 0,
                t - n
            }
              , t_ = e => 2 * e.length
              , tv = (e, t) => {
                z(e % 4 == 0, "Pointer passed to UTF32ToString must be aligned to four bytes!");
                for (var r = 0, n = ""; !(r >= t / 4); ) {
                    var a = _[e + 4 * r >> 2];
                    if (0 == a)
                        break;
                    if (++r,
                    a >= 65536) {
                        var o = a - 65536;
                        n += String.fromCharCode(55296 | o >> 10, 56320 | 1023 & o)
                    } else
                        n += String.fromCharCode(a)
                }
                return n
            }
              , tb = (e, t, r) => {
                if (z(t % 4 == 0, "Pointer passed to stringToUTF32 must be aligned to four bytes!"),
                z("number" == typeof r, "stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),
                void 0 === r && (r = 2147483647),
                r < 4)
                    return 0;
                for (var n = t, a = n + r - 4, o = 0; o < e.length; ++o) {
                    var i = e.charCodeAt(o);
                    if (i >= 55296 && i <= 57343 && (i = 65536 + ((1023 & i) << 10) | 1023 & e.charCodeAt(++o)),
                    _[t >> 2] = i,
                    (t += 4) + 4 > a)
                        break
                }
                return _[t >> 2] = 0,
                t - n
            }
              , tC = e => {
                for (var t = 0, r = 0; r < e.length; ++r) {
                    var n = e.charCodeAt(r);
                    n >= 55296 && n <= 57343 && ++r,
                    t += 4
                }
                return t
            }
              , tA = {};
            function tw(e) {
                var t = tA[e];
                return void 0 === t ? eL(e) : t
            }
            var tx = [];
            function tT() {
                if ("object" == typeof globalThis)
                    return globalThis;
                function e(e) {
                    e.$$$embind_global$$$ = e;
                    var t = "object" == typeof $$$embind_global$$$ && e.$$$embind_global$$$ == e;
                    return t || delete e.$$$embind_global$$$,
                    t
                }
                if ("object" == typeof $$$embind_global$$$ || ("object" == typeof r.g && e(r.g) ? $$$embind_global$$$ = r.g : "object" == typeof self && e(self) && ($$$embind_global$$$ = self),
                "object" == typeof $$$embind_global$$$))
                    return $$$embind_global$$$;
                throw Error("unable to get global object.")
            }
            var tP = []
              , tS = {};
            function tF(e, t) {
                return z(e == e >>> 0 || e == (0 | e)),
                z(t === (0 | t)),
                t + 2097152 >>> 0 < 4194305 - !!e ? (e >>> 0) + 4294967296 * t : NaN
            }
            x = () => performance.now();
            var tk = () => 2147483648
              , tE = e => {
                en("Cannot enlarge memory arrays to size ".concat(e, " bytes (OOM). If you want malloc to return NULL (0) instead of this abort, do not link with -sABORTING_MALLOC (that is, the default when growth is enabled is to not abort, but you have overridden that)"))
            }
              , tM = e => {
                var t = h.buffer
                  , r = e - t.byteLength + 65535 >>> 16;
                try {
                    return h.grow(r),
                    Y(),
                    1
                } catch (r) {
                    $("growMemory: Attempted to grow heap from ".concat(t.byteLength, " bytes to ").concat(e, " bytes, but got error: ").concat(r))
                }
            }
              , tI = {
                counter: 1,
                buffers: [],
                programs: [],
                framebuffers: [],
                renderbuffers: [],
                textures: [],
                shaders: [],
                vaos: [],
                contexts: [],
                offscreenCanvases: {},
                queries: [],
                samplers: [],
                transformFeedbacks: [],
                syncs: [],
                stringCache: {},
                stringiCache: {},
                unpackAlignment: 4,
                recordError: function(e) {
                    tI.lastError || (tI.lastError = e)
                },
                getNewId: function(e) {
                    for (var t = tI.counter++, r = e.length; r < t; r++)
                        e[r] = null;
                    return t
                },
                getSource: function(e, t, r, n) {
                    for (var a = "", o = 0; o < t; ++o) {
                        var i = n ? _[n + 4 * o >> 2] : -1;
                        a += eb(_[r + 4 * o >> 2], i < 0 ? void 0 : i)
                    }
                    return a
                },
                createContext: function(e, t) {
                    e.getContextSafariWebGL2Fixed || (e.getContextSafariWebGL2Fixed = e.getContext,
                    e.getContext = function(t, r) {
                        var n = e.getContextSafariWebGL2Fixed(t, r);
                        return "webgl" == t == n instanceof WebGLRenderingContext ? n : null
                    }
                    );
                    var r = t.majorVersion > 1 ? e.getContext("webgl2", t) : e.getContext("webgl", t);
                    return r ? tI.registerContext(r, t) : 0
                },
                registerContext: function(e, t) {
                    var r = tI.getNewId(tI.contexts)
                      , n = {
                        handle: r,
                        attributes: t,
                        version: t.majorVersion,
                        GLctx: e
                    };
                    return e.canvas && (e.canvas.GLctxObject = n),
                    tI.contexts[r] = n,
                    (void 0 === t.enableExtensionsByDefault || t.enableExtensionsByDefault) && tI.initExtensions(n),
                    r
                },
                makeContextCurrent: function(t) {
                    return tI.currentContext = tI.contexts[t],
                    e.ctx = T = tI.currentContext && tI.currentContext.GLctx,
                    !(t && !T)
                },
                getContext: function(e) {
                    return tI.contexts[e]
                },
                deleteContext: function(e) {
                    tI.currentContext === tI.contexts[e] && (tI.currentContext = null),
                    "object" == typeof JSEvents && JSEvents.removeAllHandlersOnTarget(tI.contexts[e].GLctx.canvas),
                    tI.contexts[e] && tI.contexts[e].GLctx.canvas && (tI.contexts[e].GLctx.canvas.GLctxObject = void 0),
                    tI.contexts[e] = null
                },
                initExtensions: function(e) {
                    if (e || (e = tI.currentContext),
                    !e.initExtensionsDone) {
                        e.initExtensionsDone = !0;
                        var t, r, n, a = e.GLctx;
                        (t = a.getExtension("ANGLE_instanced_arrays")) && (a.vertexAttribDivisor = function(e, r) {
                            t.vertexAttribDivisorANGLE(e, r)
                        }
                        ,
                        a.drawArraysInstanced = function(e, r, n, a) {
                            t.drawArraysInstancedANGLE(e, r, n, a)
                        }
                        ,
                        a.drawElementsInstanced = function(e, r, n, a, o) {
                            t.drawElementsInstancedANGLE(e, r, n, a, o)
                        }
                        ),
                        (r = a.getExtension("OES_vertex_array_object")) && (a.createVertexArray = function() {
                            return r.createVertexArrayOES()
                        }
                        ,
                        a.deleteVertexArray = function(e) {
                            r.deleteVertexArrayOES(e)
                        }
                        ,
                        a.bindVertexArray = function(e) {
                            r.bindVertexArrayOES(e)
                        }
                        ,
                        a.isVertexArray = function(e) {
                            return r.isVertexArrayOES(e)
                        }
                        ),
                        (n = a.getExtension("WEBGL_draw_buffers")) && (a.drawBuffers = function(e, t) {
                            n.drawBuffersWEBGL(e, t)
                        }
                        ),
                        a.dibvbi = a.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"),
                        a.mdibvbi = a.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance"),
                        e.version >= 2 && (a.disjointTimerQueryExt = a.getExtension("EXT_disjoint_timer_query_webgl2")),
                        (e.version < 2 || !a.disjointTimerQueryExt) && (a.disjointTimerQueryExt = a.getExtension("EXT_disjoint_timer_query")),
                        a.multiDrawWebgl = a.getExtension("WEBGL_multi_draw"),
                        (a.getSupportedExtensions() || []).forEach(function(e) {
                            e.includes("lose_context") || e.includes("debug") || a.getExtension(e)
                        })
                    }
                }
            }
              , tB = {}
              , tR = () => B || "./this.program"
              , tL = () => {
                if (!tL.strings) {
                    var e = {
                        USER: "web_user",
                        LOGNAME: "web_user",
                        PATH: "/",
                        PWD: "/",
                        HOME: "/home/<USER>",
                        LANG: ("object" == typeof navigator && navigator.languages && navigator.languages[0] || "C").replace("-", "_") + ".UTF-8",
                        _: tR()
                    };
                    for (var t in tB)
                        void 0 === tB[t] ? delete e[t] : e[t] = tB[t];
                    var r = [];
                    for (var t in e)
                        r.push("".concat(t, "=").concat(e[t]));
                    tL.strings = r
                }
                return tL.strings
            }
              , tO = (e, t) => {
                for (var r = 0; r < e.length; ++r)
                    z(e.charCodeAt(r) === (255 & e.charCodeAt(r))),
                    p[t++ >> 0] = e.charCodeAt(r);
                p[t >> 0] = 0
            }
              , tD = t => {
                N || (e.onExit && e.onExit(t),
                V = !0),
                R(t, new ed(t))
            }
              , tU = [null, [], []]
              , tG = (e, t) => {
                var r = tU[e];
                z(r),
                0 === t || 10 === t ? ((1 === e ? j : $)(ev(r, 0)),
                r.length = 0) : r.push(t)
            }
              , tW = () => {
                rs(0),
                tU[1].length && tG(1, 10),
                tU[2].length && tG(2, 10)
            }
            ;
            function tH(e) {
                T.bindVertexArray(tI.vaos[e])
            }
            function tj(e, t) {
                return z(t === (0 | t)),
                (e >>> 0) + 4294967296 * t
            }
            function t$(e, t) {
                for (var r = 0; r < e; r++) {
                    var n = _[t + 4 * r >> 2];
                    T.deleteVertexArray(tI.vaos[n]),
                    tI.vaos[n] = null
                }
            }
            var tN = [];
            function tV(e, t, r, n) {
                T.drawElements(e, t, r, n)
            }
            function tz(e, t, r, n) {
                for (var a = 0; a < e; a++) {
                    var o = T[r]()
                      , i = o && tI.getNewId(n);
                    o ? (o.name = i,
                    n[i] = o) : tI.recordError(1282),
                    _[t + 4 * a >> 2] = i
                }
            }
            function tY(e, t) {
                tz(e, t, "createVertexArray", tI.vaos)
            }
            function tX(e, t, r) {
                if (!t) {
                    tI.recordError(1281);
                    return
                }
                var n, a, o = void 0;
                switch (e) {
                case 36346:
                    o = 1;
                    break;
                case 36344:
                    0 != r && 1 != r && tI.recordError(1280);
                    return;
                case 34814:
                case 36345:
                    o = 0;
                    break;
                case 34466:
                    var i = T.getParameter(34467);
                    o = i ? i.length : 0;
                    break;
                case 33309:
                    if (tI.currentContext.version < 2) {
                        tI.recordError(1282);
                        return
                    }
                    o = 2 * (T.getSupportedExtensions() || []).length;
                    break;
                case 33307:
                case 33308:
                    if (tI.currentContext.version < 2) {
                        tI.recordError(1280);
                        return
                    }
                    o = 33307 == e ? 3 : 0
                }
                if (void 0 === o) {
                    var s = T.getParameter(e);
                    switch (typeof s) {
                    case "number":
                        o = s;
                        break;
                    case "boolean":
                        o = s ? 1 : 0;
                        break;
                    case "string":
                        tI.recordError(1280);
                        return;
                    case "object":
                        if (null === s)
                            switch (e) {
                            case 34964:
                            case 35725:
                            case 34965:
                            case 36006:
                            case 36007:
                            case 32873:
                            case 34229:
                            case 36662:
                            case 36663:
                            case 35053:
                            case 35055:
                            case 36010:
                            case 35097:
                            case 35869:
                            case 32874:
                            case 36389:
                            case 35983:
                            case 35368:
                            case 34068:
                                o = 0;
                                break;
                            default:
                                tI.recordError(1280);
                                return
                            }
                        else if (s instanceof Float32Array || s instanceof Uint32Array || s instanceof Int32Array || s instanceof Array) {
                            for (var l = 0; l < s.length; ++l)
                                switch (r) {
                                case 0:
                                    _[t + 4 * l >> 2] = s[l];
                                    break;
                                case 2:
                                    b[t + 4 * l >> 2] = s[l];
                                    break;
                                case 4:
                                    p[t + l >> 0] = s[l] ? 1 : 0
                                }
                            return
                        } else
                            try {
                                o = 0 | s.name
                            } catch (t) {
                                tI.recordError(1280),
                                $("GL_INVALID_ENUM in glGet" + r + "v: Unknown object returned from WebGL getParameter(" + e + ")! (error: " + t + ")");
                                return
                            }
                        break;
                    default:
                        tI.recordError(1280),
                        $("GL_INVALID_ENUM in glGet" + r + "v: Native code calling glGet" + r + "v(" + e + ") and it returns " + s + " of type " + typeof s + "!");
                        return
                    }
                }
                switch (r) {
                case 1:
                    n = o,
                    v[t >> 2] = n,
                    v[t + 4 >> 2] = (n - v[t >> 2]) / 4294967296,
                    (a = n >= 0 ? v[t >> 2] + 4294967296 * v[t + 4 >> 2] : v[t >> 2] + 4294967296 * _[t + 4 >> 2]) != n && eA("writeI53ToI64() out of range: serialized JS Number " + n + " to Wasm heap as bytes lo=" + eC(v[t >> 2]) + ", hi=" + eC(v[t + 4 >> 2]) + ", which deserializes back to " + a + " instead!");
                    break;
                case 0:
                    _[t >> 2] = o;
                    break;
                case 2:
                    b[t >> 2] = o;
                    break;
                case 4:
                    p[t >> 0] = o ? 1 : 0
                }
            }
            var tq = e => {
                var t = eg(e) + 1
                  , r = ro(t);
                return r && ey(e, r, t),
                r
            }
              , tJ = e => parseInt(e);
            function tQ(e) {
                return "]" == e.slice(-1) && e.lastIndexOf("[")
            }
            function tK(e) {
                return 0 == (e -= 5120) ? p : 1 == e ? g : 2 == e ? m : 4 == e ? _ : 6 == e ? b : 5 == e || 28922 == e || 28520 == e || 30779 == e || 30782 == e ? v : y
            }
            function tZ(e) {
                return 31 - Math.clz32(e.BYTES_PER_ELEMENT)
            }
            function t1(e, t, r, n, a, o) {
                var i, s = tK(e), l = tZ(s), c = n * ((({
                    5: 3,
                    6: 4,
                    8: 2,
                    29502: 3,
                    29504: 4,
                    26917: 2,
                    26918: 2,
                    29846: 3,
                    29847: 4
                })[t - 6402] || 1) * (1 << l) * r + (i = tI.unpackAlignment) - 1 & -i);
                return s.subarray(a >> l, a + c >> l)
            }
            function t2(e) {
                var t = T.currentProgram;
                if (t) {
                    var r = t.uniformLocsById[e];
                    return "number" == typeof r && (t.uniformLocsById[e] = r = T.getUniformLocation(t, t.uniformArrayNamesById[e] + (r > 0 ? "[" + r + "]" : ""))),
                    r
                }
                tI.recordError(1282)
            }
            var t0 = []
              , t3 = []
              , t4 = e => e % 4 == 0 && (e % 100 != 0 || e % 400 == 0)
              , t8 = (e, t) => {
                for (var r = 0, n = 0; n <= t; r += e[n++])
                    ;
                return r
            }
              , t6 = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
              , t5 = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
              , t7 = (e, t) => {
                for (var r = new Date(e.getTime()); t > 0; ) {
                    var n = t4(r.getFullYear())
                      , a = r.getMonth()
                      , o = (n ? t6 : t5)[a];
                    if (t > o - r.getDate())
                        t -= o - r.getDate() + 1,
                        r.setDate(1),
                        a < 11 ? r.setMonth(a + 1) : (r.setMonth(0),
                        r.setFullYear(r.getFullYear() + 1));
                    else {
                        r.setDate(r.getDate() + t);
                        break
                    }
                }
                return r
            }
              , t9 = (e, t) => {
                z(e.length >= 0, "writeArrayToMemory array must have a length (should be an array or typed array)"),
                p.set(e, t)
            }
              , re = (e, t, r, n) => {
                var a, o, i, s = _[n + 40 >> 2], l = {
                    tm_sec: _[n >> 2],
                    tm_min: _[n + 4 >> 2],
                    tm_hour: _[n + 8 >> 2],
                    tm_mday: _[n + 12 >> 2],
                    tm_mon: _[n + 16 >> 2],
                    tm_year: _[n + 20 >> 2],
                    tm_wday: _[n + 24 >> 2],
                    tm_yday: _[n + 28 >> 2],
                    tm_isdst: _[n + 32 >> 2],
                    tm_gmtoff: _[n + 36 >> 2],
                    tm_zone: s ? eb(s) : ""
                }, c = eb(r), u = {
                    "%c": "%a %b %d %H:%M:%S %Y",
                    "%D": "%m/%d/%y",
                    "%F": "%Y-%m-%d",
                    "%h": "%b",
                    "%r": "%I:%M:%S %p",
                    "%R": "%H:%M",
                    "%T": "%H:%M:%S",
                    "%x": "%m/%d/%y",
                    "%X": "%H:%M:%S",
                    "%Ec": "%c",
                    "%EC": "%C",
                    "%Ex": "%m/%d/%y",
                    "%EX": "%H:%M:%S",
                    "%Ey": "%y",
                    "%EY": "%Y",
                    "%Od": "%d",
                    "%Oe": "%e",
                    "%OH": "%H",
                    "%OI": "%I",
                    "%Om": "%m",
                    "%OM": "%M",
                    "%OS": "%S",
                    "%Ou": "%u",
                    "%OU": "%U",
                    "%OV": "%V",
                    "%Ow": "%w",
                    "%OW": "%W",
                    "%Oy": "%y"
                };
                for (var f in u)
                    c = c.replace(RegExp(f, "g"), u[f]);
                var h = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
                  , d = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
                function p(e, t, r) {
                    for (var n = "number" == typeof e ? e.toString() : e || ""; n.length < t; )
                        n = r[0] + n;
                    return n
                }
                function g(e, t) {
                    return p(e, t, "0")
                }
                function m(e, t) {
                    var r;
                    function n(e) {
                        return e < 0 ? -1 : e > 0 ? 1 : 0
                    }
                    return 0 === (r = n(e.getFullYear() - t.getFullYear())) && 0 === (r = n(e.getMonth() - t.getMonth())) && (r = n(e.getDate() - t.getDate())),
                    r
                }
                function y(e) {
                    switch (e.getDay()) {
                    case 0:
                        return new Date(e.getFullYear() - 1,11,29);
                    case 1:
                        return e;
                    case 2:
                        return new Date(e.getFullYear(),0,3);
                    case 3:
                        return new Date(e.getFullYear(),0,2);
                    case 4:
                        return new Date(e.getFullYear(),0,1);
                    case 5:
                        return new Date(e.getFullYear() - 1,11,31);
                    case 6:
                        return new Date(e.getFullYear() - 1,11,30)
                    }
                }
                function v(e) {
                    var t = t7(new Date(e.tm_year + 1900,0,1), e.tm_yday)
                      , r = new Date(t.getFullYear(),0,4)
                      , n = new Date(t.getFullYear() + 1,0,4)
                      , a = y(r)
                      , o = y(n);
                    return 0 >= m(a, t) ? 0 >= m(o, t) ? t.getFullYear() + 1 : t.getFullYear() : t.getFullYear() - 1
                }
                var b = {
                    "%a": e => h[e.tm_wday].substring(0, 3),
                    "%A": e => h[e.tm_wday],
                    "%b": e => d[e.tm_mon].substring(0, 3),
                    "%B": e => d[e.tm_mon],
                    "%C": e => g((e.tm_year + 1900) / 100 | 0, 2),
                    "%d": e => g(e.tm_mday, 2),
                    "%e": e => p(e.tm_mday, 2, " "),
                    "%g": e => v(e).toString().substring(2),
                    "%G": e => v(e),
                    "%H": e => g(e.tm_hour, 2),
                    "%I": e => {
                        var t = e.tm_hour;
                        return 0 == t ? t = 12 : t > 12 && (t -= 12),
                        g(t, 2)
                    }
                    ,
                    "%j": e => g(e.tm_mday + t8(t4(e.tm_year + 1900) ? t6 : t5, e.tm_mon - 1), 3),
                    "%m": e => g(e.tm_mon + 1, 2),
                    "%M": e => g(e.tm_min, 2),
                    "%n": () => "\n",
                    "%p": e => e.tm_hour >= 0 && e.tm_hour < 12 ? "AM" : "PM",
                    "%S": e => g(e.tm_sec, 2),
                    "%t": () => "	",
                    "%u": e => e.tm_wday || 7,
                    "%U": e => g(Math.floor((e.tm_yday + 7 - e.tm_wday) / 7), 2),
                    "%V": e => {
                        var t = Math.floor((e.tm_yday + 7 - (e.tm_wday + 6) % 7) / 7);
                        if ((e.tm_wday + 371 - e.tm_yday - 2) % 7 <= 2 && t++,
                        t) {
                            if (53 == t) {
                                var r = (e.tm_wday + 371 - e.tm_yday) % 7;
                                4 == r || 3 == r && t4(e.tm_year) || (t = 1)
                            }
                        } else {
                            t = 52;
                            var n = (e.tm_wday + 7 - e.tm_yday - 1) % 7;
                            (4 == n || 5 == n && t4(e.tm_year % 400 - 1)) && t++
                        }
                        return g(t, 2)
                    }
                    ,
                    "%w": e => e.tm_wday,
                    "%W": e => g(Math.floor((e.tm_yday + 7 - (e.tm_wday + 6) % 7) / 7), 2),
                    "%y": e => (e.tm_year + 1900).toString().substring(2),
                    "%Y": e => e.tm_year + 1900,
                    "%z": e => {
                        var t = e.tm_gmtoff;
                        return (t >= 0 ? "+" : "-") + String("0000" + (t = (t = Math.abs(t) / 60) / 60 * 100 + t % 60)).slice(-4)
                    }
                    ,
                    "%Z": e => e.tm_zone,
                    "%%": () => "%"
                };
                for (var f in c = c.replace(/%%/g, "\0\0"),
                b)
                    c.includes(f) && (c = c.replace(RegExp(f, "g"), b[f](l)));
                var C = (o = Array(eg(a = c = c.replace(/\0\0/g, "%")) + 1),
                em(a, o, 0, o.length),
                o);
                return C.length > t ? 0 : (t9(C, e),
                C.length - 1)
            }
            ;
            eE = e.InternalError = class extends Error {
                constructor(e) {
                    super(e),
                    this.name = "InternalError"
                }
            }
            ,
            function() {
                for (var e = Array(256), t = 0; t < 256; ++t)
                    e[t] = String.fromCharCode(t);
                eR = e
            }(),
            eO = e.BindingError = class extends Error {
                constructor(e) {
                    super(e),
                    this.name = "BindingError"
                }
            }
            ,
            eQ.prototype.isAliasOf = function(e) {
                if (!(this instanceof eQ) || !(e instanceof eQ))
                    return !1;
                for (var t = this.$$.ptrType.registeredClass, r = this.$$.ptr, n = e.$$.ptrType.registeredClass, a = e.$$.ptr; t.baseClass; )
                    r = t.upcast(r),
                    t = t.baseClass;
                for (; n.baseClass; )
                    a = n.upcast(a),
                    n = n.baseClass;
                return t === n && r === a
            }
            ,
            eQ.prototype.clone = function() {
                if (this.$$.ptr || eG(this),
                this.$$.preservePointerOnDelete)
                    return this.$$.count.value += 1,
                    this;
                var e, t = eJ(Object.create(Object.getPrototypeOf(this), {
                    $$: {
                        value: {
                            count: (e = this.$$).count,
                            deleteScheduled: e.deleteScheduled,
                            preservePointerOnDelete: e.preservePointerOnDelete,
                            ptr: e.ptr,
                            ptrType: e.ptrType,
                            smartPtr: e.smartPtr,
                            smartPtrType: e.smartPtrType
                        }
                    }
                }));
                return t.$$.count.value += 1,
                t.$$.deleteScheduled = !1,
                t
            }
            ,
            eQ.prototype.delete = function() {
                this.$$.ptr || eG(this),
                this.$$.deleteScheduled && !this.$$.preservePointerOnDelete && eD("Object already scheduled for deletion"),
                eH(this),
                ej(this.$$),
                this.$$.preservePointerOnDelete || (this.$$.smartPtr = void 0,
                this.$$.ptr = void 0)
            }
            ,
            eQ.prototype.isDeleted = function() {
                return !this.$$.ptr
            }
            ,
            eQ.prototype.deleteLater = function() {
                return this.$$.ptr || eG(this),
                this.$$.deleteScheduled && !this.$$.preservePointerOnDelete && eD("Object already scheduled for deletion"),
                eN.push(this),
                1 === eN.length && ez && ez(eV),
                this.$$.deleteScheduled = !0,
                this
            }
            ,
            e.getInheritedInstanceCount = function() {
                return Object.keys(eY).length
            }
            ,
            e.getLiveInheritedInstances = function() {
                var e = [];
                for (var t in eY)
                    eY.hasOwnProperty(t) && e.push(eY[t]);
                return e
            }
            ,
            e.flushPendingDeletes = eV,
            e.setDelayFunction = function(e) {
                ez = e,
                eN.length && ez && ez(eV)
            }
            ,
            e5.prototype.getPointee = function(e) {
                return this.rawGetPointee && (e = this.rawGetPointee(e)),
                e
            }
            ,
            e5.prototype.destructor = function(e) {
                this.rawDestructor && this.rawDestructor(e)
            }
            ,
            e5.prototype.argPackAdvance = 8,
            e5.prototype.readValueFromPointer = eP,
            e5.prototype.deleteObject = function(e) {
                null !== e && e.delete()
            }
            ,
            e5.prototype.fromWireType = eq,
            ta = e.UnboundTypeError = (k = Error,
            (M = eZ(E = "UnboundTypeError", function(e) {
                this.name = E,
                this.message = e;
                var t = Error(e).stack;
                void 0 !== t && (this.stack = this.toString() + "\n" + t.replace(/^Error(:[^\n]*)?\n/, ""))
            })).prototype = Object.create(k.prototype),
            M.prototype.constructor = M,
            M.prototype.toString = function() {
                return void 0 === this.message ? this.name : "".concat(this.name, ": ").concat(this.message)
            }
            ,
            M),
            Object.assign(tc.prototype, {
                get(e) {
                    return z(void 0 !== this.allocated[e], "invalid handle: ".concat(e)),
                    this.allocated[e]
                },
                has(e) {
                    return void 0 !== this.allocated[e]
                },
                allocate(e) {
                    var t = this.freelist.pop() || this.allocated.length;
                    return this.allocated[t] = e,
                    t
                },
                free(e) {
                    z(void 0 !== this.allocated[e]),
                    this.allocated[e] = void 0,
                    this.freelist.push(e)
                }
            }),
            tu.allocated.push({
                value: void 0
            }, {
                value: null
            }, {
                value: !0
            }, {
                value: !1
            }),
            tu.reserved = tu.allocated.length,
            e.count_emval_handles = function() {
                for (var e = 0, t = tu.reserved; t < tu.allocated.length; ++t)
                    void 0 !== tu.allocated[t] && ++e;
                return e
            }
            ;
            for (var rt = 0; rt < 32; ++rt)
                tN.push(Array(rt));
            for (var rr = new Float32Array(288), rt = 0; rt < 288; ++rt)
                t0[rt] = rr.subarray(0, rt + 1);
            for (var rn = new Int32Array(288), rt = 0; rt < 288; ++rt)
                t3[rt] = rn.subarray(0, rt + 1);
            var ra = {
                __assert_fail: (e, t, r, n) => {
                    en("Assertion failed: ".concat(eb(e), ", at: ") + [t ? eb(t) : "unknown filename", r, n ? eb(n) : "unknown function"])
                }
                ,
                __syscall_fcntl64: function(e, t, r) {
                    return ew.varargs = r,
                    0
                },
                __syscall_fstat64: (e, t) => {
                    en("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM")
                }
                ,
                __syscall_ioctl: function(e, t, r) {
                    return ew.varargs = r,
                    0
                },
                __syscall_lstat64: (e, t) => {
                    en("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM")
                }
                ,
                __syscall_newfstatat: (e, t, r, n) => {
                    en("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM")
                }
                ,
                __syscall_openat: function(e, t, r, n) {
                    ew.varargs = n,
                    en("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM")
                },
                __syscall_stat64: (e, t) => {
                    en("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM")
                }
                ,
                _embind_finalize_value_object: function(e) {
                    var t = ex[e];
                    delete ex[e];
                    var r = t.rawConstructor
                      , n = t.rawDestructor
                      , a = t.fields;
                    eI([e], a.map(e => e.getterReturnType).concat(a.map(e => e.setterArgumentType)), e => {
                        var o = {};
                        return a.forEach( (t, r) => {
                            var n = t.fieldName
                              , i = e[r]
                              , s = t.getter
                              , l = t.getterContext
                              , c = e[r + a.length]
                              , u = t.setter
                              , f = t.setterContext;
                            o[n] = {
                                read: e => i.fromWireType(s(l, e)),
                                write: (e, t) => {
                                    var r = [];
                                    u(f, e, c.toWireType(r, t)),
                                    eT(r)
                                }
                            }
                        }
                        ),
                        [{
                            name: t.name,
                            fromWireType: function(e) {
                                var t = {};
                                for (var r in o)
                                    t[r] = o[r].read(e);
                                return n(e),
                                t
                            },
                            toWireType: function(e, t) {
                                for (var a in o)
                                    if (!(a in t))
                                        throw TypeError('Missing field: "'.concat(a, '"'));
                                var i = r();
                                for (a in o)
                                    o[a].write(i, t[a]);
                                return null !== e && e.push(n, i),
                                i
                            },
                            argPackAdvance: 8,
                            readValueFromPointer: eP,
                            destructorFunction: n
                        }]
                    }
                    )
                },
                _embind_register_bigint: function(e, t, r, n, a) {},
                _embind_register_bool: function(e, t, r, n, a) {
                    var o = eB(r);
                    eU(e, {
                        name: t = eL(t),
                        fromWireType: function(e) {
                            return !!e
                        },
                        toWireType: function(e, t) {
                            return t ? n : a
                        },
                        argPackAdvance: 8,
                        readValueFromPointer: function(e) {
                            var n;
                            if (1 === r)
                                n = p;
                            else if (2 === r)
                                n = m;
                            else if (4 === r)
                                n = _;
                            else
                                throw TypeError("Unknown boolean type size: " + t);
                            return this.fromWireType(n[e >> o])
                        },
                        destructorFunction: null
                    })
                },
                _embind_register_class: function(e, t, r, n, a, o, i, s, l, c, u, f, h) {
                    u = eL(u),
                    o = tn(a, o),
                    s && (s = tn(i, s)),
                    c && (c = tn(l, c)),
                    h = tn(f, h);
                    var d = eK(u);
                    e2(d, function() {
                        ti("Cannot construct ".concat(u, " due to unbound types"), [n])
                    }),
                    eI([e, t, r], n ? [n] : [], function(t) {
                        t = t[0],
                        a = n ? (r = t.registeredClass).instancePrototype : eQ.prototype;
                        var r, a, i = eZ(d, function() {
                            if (Object.getPrototypeOf(this) !== l)
                                throw new eO("Use 'new' to construct " + u);
                            if (void 0 === f.constructor_body)
                                throw new eO(u + " has no accessible constructor");
                            var e = f.constructor_body[arguments.length];
                            if (void 0 === e)
                                throw new eO("Tried to invoke ctor of ".concat(u, " with invalid number of parameters (").concat(arguments.length, ") - expected (").concat(Object.keys(f.constructor_body).toString(), ") parameters instead!"));
                            return e.apply(this, arguments)
                        }), l = Object.create(a, {
                            constructor: {
                                value: i
                            }
                        });
                        i.prototype = l;
                        var f = new e0(u,i,l,h,r,o,s,c);
                        f.baseClass && (void 0 === f.baseClass.__derivedClasses && (f.baseClass.__derivedClasses = []),
                        f.baseClass.__derivedClasses.push(f));
                        var p = new e5(u,f,!0,!1,!1)
                          , g = new e5(u + "*",f,!1,!1,!1)
                          , m = new e5(u + " const*",f,!1,!0,!1);
                        return e$[e] = {
                            pointerType: g,
                            constPointerType: m
                        },
                        e7(d, i),
                        [p, g, m]
                    })
                },
                _embind_register_class_class_function: function(e, t, r, n, a, o, i, s) {
                    var l = tl(r, n);
                    t = eL(t),
                    o = tn(a, o),
                    eI([], [e], function(e) {
                        e = e[0];
                        var n = "".concat(e.name, ".").concat(t);
                        function a() {
                            ti("Cannot call ".concat(n, " due to unbound types"), l)
                        }
                        t.startsWith("@@") && (t = Symbol[t.substring(2)]);
                        var c = e.registeredClass.constructor;
                        return void 0 === c[t] ? (a.argCount = r - 1,
                        c[t] = a) : (e1(c, t, n),
                        c[t].overloadTable[r - 1] = a),
                        eI([], l, function(a) {
                            var l = ts(n, [a[0], null].concat(a.slice(1)), null, o, i, s);
                            if (void 0 === c[t].overloadTable ? (l.argCount = r - 1,
                            c[t] = l) : c[t].overloadTable[r - 1] = l,
                            e.registeredClass.__derivedClasses)
                                for (let r of e.registeredClass.__derivedClasses)
                                    r.constructor.hasOwnProperty(t) || (r.constructor[t] = l);
                            return []
                        }),
                        []
                    })
                },
                _embind_register_class_constructor: function(e, t, r, n, a, o) {
                    z(t > 0);
                    var i = tl(t, r);
                    a = tn(n, a),
                    eI([], [e], function(e) {
                        e = e[0];
                        var r = "constructor ".concat(e.name);
                        if (void 0 === e.registeredClass.constructor_body && (e.registeredClass.constructor_body = []),
                        void 0 !== e.registeredClass.constructor_body[t - 1])
                            throw new eO("Cannot register multiple constructors with identical number of parameters (".concat(t - 1, ") for class '").concat(e.name, "'! Overload resolution is currently only performed using the parameter count, not actual type info!"));
                        return e.registeredClass.constructor_body[t - 1] = () => {
                            ti("Cannot construct ".concat(e.name, " due to unbound types"), i)
                        }
                        ,
                        eI([], i, function(n) {
                            return n.splice(1, 0, null),
                            e.registeredClass.constructor_body[t - 1] = ts(r, n, null, a, o),
                            []
                        }),
                        []
                    })
                },
                _embind_register_class_function: function(e, t, r, n, a, o, i, s, l) {
                    var c = tl(r, n);
                    t = eL(t),
                    o = tn(a, o),
                    eI([], [e], function(e) {
                        e = e[0];
                        var n = "".concat(e.name, ".").concat(t);
                        function a() {
                            ti("Cannot call ".concat(n, " due to unbound types"), c)
                        }
                        t.startsWith("@@") && (t = Symbol[t.substring(2)]),
                        s && e.registeredClass.pureVirtualFunctions.push(t);
                        var u = e.registeredClass.instancePrototype
                          , f = u[t];
                        return void 0 === f || void 0 === f.overloadTable && f.className !== e.name && f.argCount === r - 2 ? (a.argCount = r - 2,
                        a.className = e.name,
                        u[t] = a) : (e1(u, t, n),
                        u[t].overloadTable[r - 2] = a),
                        eI([], c, function(a) {
                            var s = ts(n, a, e, o, i, l);
                            return void 0 === u[t].overloadTable ? (s.argCount = r - 2,
                            u[t] = s) : u[t].overloadTable[r - 2] = s,
                            []
                        }),
                        []
                    })
                },
                _embind_register_constant: function(t, r, n) {
                    t = eL(t),
                    eI([], [r], function(r) {
                        return r = r[0],
                        e[t] = r.fromWireType(n),
                        []
                    })
                },
                _embind_register_emval: function(e, t) {
                    eU(e, {
                        name: t = eL(t),
                        fromWireType: function(e) {
                            var t = th.toValue(e);
                            return tf(e),
                            t
                        },
                        toWireType: function(e, t) {
                            return th.toHandle(t)
                        },
                        argPackAdvance: 8,
                        readValueFromPointer: eP,
                        destructorFunction: null
                    })
                },
                _embind_register_enum: function(e, t, r, n) {
                    var a = eB(r);
                    function o() {}
                    t = eL(t),
                    o.values = {},
                    eU(e, {
                        name: t,
                        constructor: o,
                        fromWireType: function(e) {
                            return this.constructor.values[e]
                        },
                        toWireType: function(e, t) {
                            return t.value
                        },
                        argPackAdvance: 8,
                        readValueFromPointer: function(e, t, r) {
                            switch (t) {
                            case 0:
                                return function(e) {
                                    var t = r ? p : g;
                                    return this.fromWireType(t[e])
                                }
                                ;
                            case 1:
                                return function(e) {
                                    var t = r ? m : y;
                                    return this.fromWireType(t[e >> 1])
                                }
                                ;
                            case 2:
                                return function(e) {
                                    var t = r ? _ : v;
                                    return this.fromWireType(t[e >> 2])
                                }
                                ;
                            default:
                                throw TypeError("Unknown integer type: " + e)
                            }
                        }(t, a, n),
                        destructorFunction: null
                    }),
                    e2(t, o)
                },
                _embind_register_enum_value: function(e, t, r) {
                    var n = td(e, "enum");
                    t = eL(t);
                    var a = n.constructor
                      , o = Object.create(n.constructor.prototype, {
                        value: {
                            value: r
                        },
                        constructor: {
                            value: eZ("".concat(n.name, "_").concat(t), function() {})
                        }
                    });
                    a.values[r] = o,
                    a[t] = o
                },
                _embind_register_float: function(e, t, r) {
                    var n = eB(r);
                    eU(e, {
                        name: t = eL(t),
                        fromWireType: function(e) {
                            return e
                        },
                        toWireType: function(e, t) {
                            if ("number" != typeof t && "boolean" != typeof t)
                                throw TypeError("Cannot convert ".concat(tp(t), " to ").concat(this.name));
                            return t
                        },
                        argPackAdvance: 8,
                        readValueFromPointer: function(e, t) {
                            switch (t) {
                            case 2:
                                return function(e) {
                                    return this.fromWireType(b[e >> 2])
                                }
                                ;
                            case 3:
                                return function(e) {
                                    return this.fromWireType(C[e >> 3])
                                }
                                ;
                            default:
                                throw TypeError("Unknown float type: " + e)
                            }
                        }(t, n),
                        destructorFunction: null
                    })
                },
                _embind_register_function: function(e, t, r, n, a, o, i) {
                    var s = tl(t, r);
                    e = eL(e),
                    a = tn(n, a),
                    e2(e, function() {
                        ti("Cannot call ".concat(e, " due to unbound types"), s)
                    }, t - 1),
                    eI([], s, function(r) {
                        var n = [r[0], null].concat(r.slice(1));
                        return e7(e, ts(e, n, null, a, o, i), t - 1),
                        []
                    })
                },
                _embind_register_integer: function(e, t, r, n, a) {
                    t = eL(t),
                    -1 === a && (a = 4294967295);
                    var o = eB(r)
                      , i = e => e;
                    if (0 === n) {
                        var s = 32 - 8 * r;
                        i = e => e << s >>> s
                    }
                    var l = t.includes("unsigned")
                      , c = (e, r) => {
                        if ("number" != typeof e && "boolean" != typeof e)
                            throw TypeError('Cannot convert "'.concat(tp(e), '" to ').concat(r));
                        if (e < n || e > a)
                            throw TypeError('Passing a number "'.concat(tp(e), '" from JS side to C/C++ side to an argument of type "').concat(t, '", which is outside the valid range [').concat(n, ", ").concat(a, "]!"))
                    }
                    ;
                    eU(e, {
                        name: t,
                        fromWireType: i,
                        toWireType: l ? function(e, t) {
                            return c(t, this.name),
                            t >>> 0
                        }
                        : function(e, t) {
                            return c(t, this.name),
                            t
                        }
                        ,
                        argPackAdvance: 8,
                        readValueFromPointer: function(e, t, r) {
                            switch (t) {
                            case 0:
                                return r ? function(e) {
                                    return p[e]
                                }
                                : function(e) {
                                    return g[e]
                                }
                                ;
                            case 1:
                                return r ? function(e) {
                                    return m[e >> 1]
                                }
                                : function(e) {
                                    return y[e >> 1]
                                }
                                ;
                            case 2:
                                return r ? function(e) {
                                    return _[e >> 2]
                                }
                                : function(e) {
                                    return v[e >> 2]
                                }
                                ;
                            default:
                                throw TypeError("Unknown integer type: " + e)
                            }
                        }(t, o, 0 !== n),
                        destructorFunction: null
                    })
                },
                _embind_register_memory_view: function(e, t, r) {
                    var n = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array][t];
                    function a(e) {
                        e >>= 2;
                        var t = v
                          , r = t[e]
                          , a = t[e + 1];
                        return new n(t.buffer,a,r)
                    }
                    eU(e, {
                        name: r = eL(r),
                        fromWireType: a,
                        argPackAdvance: 8,
                        readValueFromPointer: a
                    }, {
                        ignoreDuplicateRegistrations: !0
                    })
                },
                _embind_register_smart_ptr: function(e, t, r, n, a, o, i, s, l, c, u, f) {
                    r = eL(r),
                    o = tn(a, o),
                    s = tn(i, s),
                    c = tn(l, c),
                    f = tn(u, f),
                    eI([e], [t], function(e) {
                        return e = e[0],
                        [new e5(r,e.registeredClass,!1,!1,!0,e,n,o,s,c,f)]
                    })
                },
                _embind_register_std_string: function(e, t) {
                    var r = "std::string" === (t = eL(t));
                    eU(e, {
                        name: t,
                        fromWireType: function(e) {
                            var t, n = v[e >> 2], a = e + 4;
                            if (r)
                                for (var o = a, i = 0; i <= n; ++i) {
                                    var s = a + i;
                                    if (i == n || 0 == g[s]) {
                                        var l = s - o
                                          , c = eb(o, l);
                                        void 0 === t ? t = c : t += "\0" + c,
                                        o = s + 1
                                    }
                                }
                            else {
                                for (var u = Array(n), i = 0; i < n; ++i)
                                    u[i] = String.fromCharCode(g[a + i]);
                                t = u.join("")
                            }
                            return ri(e),
                            t
                        },
                        toWireType: function(e, t) {
                            t instanceof ArrayBuffer && (t = new Uint8Array(t));
                            var n, a = "string" == typeof t;
                            a || t instanceof Uint8Array || t instanceof Uint8ClampedArray || t instanceof Int8Array || eD("Cannot pass non-string to std::string");
                            var o = ro(4 + (n = r && a ? eg(t) : t.length) + 1)
                              , i = o + 4;
                            if (v[o >> 2] = n,
                            r && a)
                                ey(t, i, n + 1);
                            else if (a)
                                for (var s = 0; s < n; ++s) {
                                    var l = t.charCodeAt(s);
                                    l > 255 && (ri(i),
                                    eD("String has UTF-16 code units that do not fit in 8 bits")),
                                    g[i + s] = l
                                }
                            else
                                for (var s = 0; s < n; ++s)
                                    g[i + s] = t[s];
                            return null !== e && e.push(ri, o),
                            o
                        },
                        argPackAdvance: 8,
                        readValueFromPointer: eP,
                        destructorFunction: function(e) {
                            ri(e)
                        }
                    })
                },
                _embind_register_std_wstring: function(e, t, r) {
                    var n, a, o, i, s;
                    r = eL(r),
                    2 === t ? (n = tm,
                    a = ty,
                    i = t_,
                    o = () => y,
                    s = 1) : 4 === t && (n = tv,
                    a = tb,
                    i = tC,
                    o = () => v,
                    s = 2),
                    eU(e, {
                        name: r,
                        fromWireType: function(e) {
                            for (var r, a = v[e >> 2], i = o(), l = e + 4, c = 0; c <= a; ++c) {
                                var u = e + 4 + c * t;
                                if (c == a || 0 == i[u >> s]) {
                                    var f = u - l
                                      , h = n(l, f);
                                    void 0 === r ? r = h : r += "\0" + h,
                                    l = u + t
                                }
                            }
                            return ri(e),
                            r
                        },
                        toWireType: function(e, n) {
                            "string" != typeof n && eD("Cannot pass non-string to C++ string type ".concat(r));
                            var o = i(n)
                              , l = ro(4 + o + t);
                            return v[l >> 2] = o >> s,
                            a(n, l + 4, o + t),
                            null !== e && e.push(ri, l),
                            l
                        },
                        argPackAdvance: 8,
                        readValueFromPointer: eP,
                        destructorFunction: function(e) {
                            ri(e)
                        }
                    })
                },
                _embind_register_value_object: function(e, t, r, n, a, o) {
                    ex[e] = {
                        name: eL(t),
                        rawConstructor: tn(r, n),
                        rawDestructor: tn(a, o),
                        fields: []
                    }
                },
                _embind_register_value_object_field: function(e, t, r, n, a, o, i, s, l, c) {
                    ex[e].fields.push({
                        fieldName: eL(t),
                        getterReturnType: r,
                        getter: tn(n, a),
                        getterContext: o,
                        setterArgumentType: i,
                        setter: tn(s, l),
                        setterContext: c
                    })
                },
                _embind_register_void: function(e, t) {
                    eU(e, {
                        isVoid: !0,
                        name: t = eL(t),
                        argPackAdvance: 0,
                        fromWireType: function() {},
                        toWireType: function(e, t) {}
                    })
                },
                _emscripten_get_now_is_monotonic: () => !0,
                _emscripten_throw_longjmp: () => {
                    throw 1 / 0
                }
                ,
                _emval_as: function(e, t, r) {
                    e = th.toValue(e),
                    t = td(t, "emval::as");
                    var n = []
                      , a = th.toHandle(n);
                    return v[r >> 2] = a,
                    t.toWireType(n, e)
                },
                _emval_call_method: function(e, t, r, n, a) {
                    var o;
                    return (e = tx[e])(t = th.toValue(t), r = tw(r), (o = [],
                    v[n >> 2] = th.toHandle(o),
                    o), a)
                },
                _emval_call_void_method: function(e, t, r, n) {
                    (e = tx[e])(t = th.toValue(t), r = tw(r), null, n)
                },
                _emval_decref: tf,
                _emval_get_global: function(e) {
                    return 0 === e ? th.toHandle(tT()) : (e = tw(e),
                    th.toHandle(tT()[e]))
                },
                _emval_get_method_caller: function(e, t) {
                    var r, n, a = function(e, t) {
                        for (var r = Array(e), n = 0; n < e; ++n)
                            r[n] = td(v[t + 4 * n >> 2], "parameter " + n);
                        return r
                    }(e, t), o = a[0], i = o.name + "_$" + a.slice(1).map(function(e) {
                        return e.name
                    }).join("_") + "$", s = tP[i];
                    if (void 0 !== s)
                        return s;
                    var l = Array(e - 1);
                    return r = (t, r, n, i) => {
                        for (var s = 0, c = 0; c < e - 1; ++c)
                            l[c] = a[c + 1].readValueFromPointer(i + s),
                            s += a[c + 1].argPackAdvance;
                        for (var u = t[r].apply(t, l), c = 0; c < e - 1; ++c)
                            a[c + 1].deleteObject && a[c + 1].deleteObject(l[c]);
                        if (!o.isVoid)
                            return o.toWireType(n, u)
                    }
                    ,
                    n = tx.length,
                    tx.push(r),
                    s = n,
                    tP[i] = s,
                    s
                },
                _emval_get_property: function(e, t) {
                    return e = th.toValue(e),
                    t = th.toValue(t),
                    th.toHandle(e[t])
                },
                _emval_incref: function(e) {
                    e > 4 && (tu.get(e).refcount += 1)
                },
                _emval_new: function(e, t, r, n) {
                    e = th.toValue(e);
                    var a, o = tS[t];
                    return o || (a = Array(t + 1),
                    o = function(e, r, n) {
                        a[0] = e;
                        for (var o = 0; o < t; ++o) {
                            var i = td(v[r + 4 * o >> 2], "parameter " + o);
                            a[o + 1] = i.readValueFromPointer(n),
                            n += i.argPackAdvance
                        }
                        var s = new (e.bind.apply(e, a));
                        return th.toHandle(s)
                    }
                    ,
                    tS[t] = o),
                    o(e, r, n)
                },
                _emval_new_array: function() {
                    return th.toHandle([])
                },
                _emval_new_cstring: function(e) {
                    return th.toHandle(tw(e))
                },
                _emval_new_object: function() {
                    return th.toHandle({})
                },
                _emval_not: function(e) {
                    return !(e = th.toValue(e))
                },
                _emval_run_destructors: function(e) {
                    eT(th.toValue(e)),
                    tf(e)
                },
                _emval_set_property: function(e, t, r) {
                    e = th.toValue(e),
                    t = th.toValue(t),
                    r = th.toValue(r),
                    e[t] = r
                },
                _emval_take_value: function(e, t) {
                    var r = (e = td(e, "_emval_take_value")).readValueFromPointer(t);
                    return th.toHandle(r)
                },
                _mmap_js: function(e, t, r, n, a, o, i, s) {
                    return tF(a, o),
                    -52
                },
                _munmap_js: function(e, t, r, n, a, o, i) {
                    tF(o, i)
                },
                abort: () => {
                    en("native code called abort()")
                }
                ,
                emscripten_date_now: function() {
                    return Date.now()
                },
                emscripten_get_now: x,
                emscripten_resize_heap: e => {
                    var t = g.length;
                    z((e >>>= 0) > t);
                    var r = tk();
                    e > r && ($("Cannot enlarge memory, asked to go up to ".concat(e, " bytes, but the limit is ").concat(r, " bytes!")),
                    tE(e));
                    for (var n = (e, t) => e + (t - e % t) % t, a = 1; a <= 4; a *= 2) {
                        var o = t * (1 + .2 / a);
                        o = Math.min(o, e + 100663296);
                        var i = Math.min(r, n(Math.max(e, o), 65536));
                        if (tM(i))
                            return !0
                    }
                    $("Failed to grow the heap from ".concat(t, " bytes to ").concat(i, " bytes, not enough memory!")),
                    tE(e)
                }
                ,
                emscripten_webgl_get_current_context: function() {
                    return tI.currentContext ? tI.currentContext.handle : 0
                },
                environ_get: (e, t) => {
                    var r = 0;
                    return tL().forEach(function(n, a) {
                        var o = t + r;
                        v[e + 4 * a >> 2] = o,
                        tO(n, o),
                        r += n.length + 1
                    }),
                    0
                }
                ,
                environ_sizes_get: (e, t) => {
                    var r = tL();
                    v[e >> 2] = r.length;
                    var n = 0;
                    return r.forEach(function(e) {
                        n += e.length + 1
                    }),
                    v[t >> 2] = n,
                    0
                }
                ,
                exit: (e, t) => {
                    if (function() {
                        var e = j
                          , t = $
                          , r = !1;
                        j = $ = e => {
                            r = !0
                        }
                        ;
                        try {
                            tW()
                        } catch (e) {}
                        j = e,
                        $ = t,
                        r && (eA("stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the Emscripten FAQ), or make sure to emit a newline when you printf etc."),
                        eA("(this may also be due to not including full filesystem support - try building with -sFORCE_FILESYSTEM)"))
                    }(),
                    N && !t) {
                        var r = "program exited (with status: ".concat(e, "), but keepRuntimeAlive() is set (counter=").concat(0, ") due to an async operation, so halting execution but not exiting the runtime or preventing further async execution (you can use emscripten_force_exit, if you want to force a true shutdown)");
                        s(r),
                        $(r)
                    }
                    tD(e)
                }
                ,
                fd_close: e => {
                    en("fd_close called without SYSCALLS_REQUIRE_FILESYSTEM")
                }
                ,
                fd_pread: function(e, t, r, n, a, o) {
                    tF(n, a),
                    en("fd_pread called without SYSCALLS_REQUIRE_FILESYSTEM")
                },
                fd_read: (e, t, r, n) => {
                    en("fd_read called without SYSCALLS_REQUIRE_FILESYSTEM")
                }
                ,
                fd_seek: function(e, t, r, n, a) {
                    return tF(t, r),
                    70
                },
                fd_write: (e, t, r, n) => {
                    for (var a = 0, o = 0; o < r; o++) {
                        var i = v[t >> 2]
                          , s = v[t + 4 >> 2];
                        t += 8;
                        for (var l = 0; l < s; l++)
                            tG(e, g[i + l]);
                        a += s
                    }
                    return v[n >> 2] = a,
                    0
                }
                ,
                glActiveTexture: function(e) {
                    T.activeTexture(e)
                },
                glAttachShader: function(e, t) {
                    T.attachShader(tI.programs[e], tI.shaders[t])
                },
                glBindAttribLocation: function(e, t, r) {
                    T.bindAttribLocation(tI.programs[e], t, eb(r))
                },
                glBindBuffer: function(e, t) {
                    35051 == e ? T.currentPixelPackBufferBinding = t : 35052 == e && (T.currentPixelUnpackBufferBinding = t),
                    T.bindBuffer(e, tI.buffers[t])
                },
                glBindFramebuffer: function(e, t) {
                    T.bindFramebuffer(e, tI.framebuffers[t])
                },
                glBindRenderbuffer: function(e, t) {
                    T.bindRenderbuffer(e, tI.renderbuffers[t])
                },
                glBindSampler: function(e, t) {
                    T.bindSampler(e, tI.samplers[t])
                },
                glBindTexture: function(e, t) {
                    T.bindTexture(e, tI.textures[t])
                },
                glBindVertexArray: tH,
                glBindVertexArrayOES: tH,
                glBlendColor: function(e, t, r, n) {
                    T.blendColor(e, t, r, n)
                },
                glBlendEquation: function(e) {
                    T.blendEquation(e)
                },
                glBlendFunc: function(e, t) {
                    T.blendFunc(e, t)
                },
                glBlitFramebuffer: function(e, t, r, n, a, o, i, s, l, c) {
                    T.blitFramebuffer(e, t, r, n, a, o, i, s, l, c)
                },
                glBufferData: function(e, t, r, n) {
                    tI.currentContext.version >= 2 ? r && t ? T.bufferData(e, g, n, r, t) : T.bufferData(e, t, n) : T.bufferData(e, r ? g.subarray(r, r + t) : t, n)
                },
                glBufferSubData: function(e, t, r, n) {
                    if (tI.currentContext.version >= 2) {
                        r && T.bufferSubData(e, t, g, n, r);
                        return
                    }
                    T.bufferSubData(e, t, g.subarray(n, n + r))
                },
                glCheckFramebufferStatus: function(e) {
                    return T.checkFramebufferStatus(e)
                },
                glClear: function(e) {
                    T.clear(e)
                },
                glClearColor: function(e, t, r, n) {
                    T.clearColor(e, t, r, n)
                },
                glClearStencil: function(e) {
                    T.clearStencil(e)
                },
                glClientWaitSync: function(e, t, r, n) {
                    var a = tj(r, n);
                    return T.clientWaitSync(tI.syncs[e], t, a)
                },
                glColorMask: function(e, t, r, n) {
                    T.colorMask(!!e, !!t, !!r, !!n)
                },
                glCompileShader: function(e) {
                    T.compileShader(tI.shaders[e])
                },
                glCompressedTexImage2D: function(e, t, r, n, a, o, i, s) {
                    if (tI.currentContext.version >= 2) {
                        T.currentPixelUnpackBufferBinding || !i ? T.compressedTexImage2D(e, t, r, n, a, o, i, s) : T.compressedTexImage2D(e, t, r, n, a, o, g, s, i);
                        return
                    }
                    T.compressedTexImage2D(e, t, r, n, a, o, s ? g.subarray(s, s + i) : null)
                },
                glCompressedTexSubImage2D: function(e, t, r, n, a, o, i, s, l) {
                    if (tI.currentContext.version >= 2) {
                        T.currentPixelUnpackBufferBinding || !s ? T.compressedTexSubImage2D(e, t, r, n, a, o, i, s, l) : T.compressedTexSubImage2D(e, t, r, n, a, o, i, g, l, s);
                        return
                    }
                    T.compressedTexSubImage2D(e, t, r, n, a, o, i, l ? g.subarray(l, l + s) : null)
                },
                glCopyBufferSubData: function(e, t, r, n, a) {
                    T.copyBufferSubData(e, t, r, n, a)
                },
                glCopyTexSubImage2D: function(e, t, r, n, a, o, i, s) {
                    T.copyTexSubImage2D(e, t, r, n, a, o, i, s)
                },
                glCreateProgram: function() {
                    var e = tI.getNewId(tI.programs)
                      , t = T.createProgram();
                    return t.name = e,
                    t.maxUniformLength = t.maxAttributeLength = t.maxUniformBlockNameLength = 0,
                    t.uniformIdCounter = 1,
                    tI.programs[e] = t,
                    e
                },
                glCreateShader: function(e) {
                    var t = tI.getNewId(tI.shaders);
                    return tI.shaders[t] = T.createShader(e),
                    t
                },
                glCullFace: function(e) {
                    T.cullFace(e)
                },
                glDeleteBuffers: function(e, t) {
                    for (var r = 0; r < e; r++) {
                        var n = _[t + 4 * r >> 2]
                          , a = tI.buffers[n];
                        a && (T.deleteBuffer(a),
                        a.name = 0,
                        tI.buffers[n] = null,
                        n == T.currentPixelPackBufferBinding && (T.currentPixelPackBufferBinding = 0),
                        n == T.currentPixelUnpackBufferBinding && (T.currentPixelUnpackBufferBinding = 0))
                    }
                },
                glDeleteFramebuffers: function(e, t) {
                    for (var r = 0; r < e; ++r) {
                        var n = _[t + 4 * r >> 2]
                          , a = tI.framebuffers[n];
                        a && (T.deleteFramebuffer(a),
                        a.name = 0,
                        tI.framebuffers[n] = null)
                    }
                },
                glDeleteProgram: function(e) {
                    if (e) {
                        var t = tI.programs[e];
                        if (!t) {
                            tI.recordError(1281);
                            return
                        }
                        T.deleteProgram(t),
                        t.name = 0,
                        tI.programs[e] = null
                    }
                },
                glDeleteRenderbuffers: function(e, t) {
                    for (var r = 0; r < e; r++) {
                        var n = _[t + 4 * r >> 2]
                          , a = tI.renderbuffers[n];
                        a && (T.deleteRenderbuffer(a),
                        a.name = 0,
                        tI.renderbuffers[n] = null)
                    }
                },
                glDeleteSamplers: function(e, t) {
                    for (var r = 0; r < e; r++) {
                        var n = _[t + 4 * r >> 2]
                          , a = tI.samplers[n];
                        a && (T.deleteSampler(a),
                        a.name = 0,
                        tI.samplers[n] = null)
                    }
                },
                glDeleteShader: function(e) {
                    if (e) {
                        var t = tI.shaders[e];
                        if (!t) {
                            tI.recordError(1281);
                            return
                        }
                        T.deleteShader(t),
                        tI.shaders[e] = null
                    }
                },
                glDeleteSync: function(e) {
                    if (e) {
                        var t = tI.syncs[e];
                        if (!t) {
                            tI.recordError(1281);
                            return
                        }
                        T.deleteSync(t),
                        t.name = 0,
                        tI.syncs[e] = null
                    }
                },
                glDeleteTextures: function(e, t) {
                    for (var r = 0; r < e; r++) {
                        var n = _[t + 4 * r >> 2]
                          , a = tI.textures[n];
                        a && (T.deleteTexture(a),
                        a.name = 0,
                        tI.textures[n] = null)
                    }
                },
                glDeleteVertexArrays: t$,
                glDeleteVertexArraysOES: t$,
                glDepthMask: function(e) {
                    T.depthMask(!!e)
                },
                glDisable: function(e) {
                    T.disable(e)
                },
                glDisableVertexAttribArray: function(e) {
                    T.disableVertexAttribArray(e)
                },
                glDrawArrays: function(e, t, r) {
                    T.drawArrays(e, t, r)
                },
                glDrawArraysInstanced: function(e, t, r, n) {
                    T.drawArraysInstanced(e, t, r, n)
                },
                glDrawArraysInstancedBaseInstanceWEBGL: function(e, t, r, n, a) {
                    T.dibvbi.drawArraysInstancedBaseInstanceWEBGL(e, t, r, n, a)
                },
                glDrawBuffers: function(e, t) {
                    for (var r = tN[e], n = 0; n < e; n++)
                        r[n] = _[t + 4 * n >> 2];
                    T.drawBuffers(r)
                },
                glDrawElements: tV,
                glDrawElementsInstanced: function(e, t, r, n, a) {
                    T.drawElementsInstanced(e, t, r, n, a)
                },
                glDrawElementsInstancedBaseVertexBaseInstanceWEBGL: function(e, t, r, n, a, o, i) {
                    T.dibvbi.drawElementsInstancedBaseVertexBaseInstanceWEBGL(e, t, r, n, a, o, i)
                },
                glDrawRangeElements: function(e, t, r, n, a, o) {
                    tV(e, n, a, o)
                },
                glEnable: function(e) {
                    T.enable(e)
                },
                glEnableVertexAttribArray: function(e) {
                    T.enableVertexAttribArray(e)
                },
                glFenceSync: function(e, t) {
                    var r = T.fenceSync(e, t);
                    if (r) {
                        var n = tI.getNewId(tI.syncs);
                        return r.name = n,
                        tI.syncs[n] = r,
                        n
                    }
                    return 0
                },
                glFinish: function() {
                    T.finish()
                },
                glFlush: function() {
                    T.flush()
                },
                glFramebufferRenderbuffer: function(e, t, r, n) {
                    T.framebufferRenderbuffer(e, t, r, tI.renderbuffers[n])
                },
                glFramebufferTexture2D: function(e, t, r, n, a) {
                    T.framebufferTexture2D(e, t, r, tI.textures[n], a)
                },
                glFrontFace: function(e) {
                    T.frontFace(e)
                },
                glGenBuffers: function(e, t) {
                    tz(e, t, "createBuffer", tI.buffers)
                },
                glGenFramebuffers: function(e, t) {
                    tz(e, t, "createFramebuffer", tI.framebuffers)
                },
                glGenRenderbuffers: function(e, t) {
                    tz(e, t, "createRenderbuffer", tI.renderbuffers)
                },
                glGenSamplers: function(e, t) {
                    tz(e, t, "createSampler", tI.samplers)
                },
                glGenTextures: function(e, t) {
                    tz(e, t, "createTexture", tI.textures)
                },
                glGenVertexArrays: tY,
                glGenVertexArraysOES: tY,
                glGenerateMipmap: function(e) {
                    T.generateMipmap(e)
                },
                glGetBufferParameteriv: function(e, t, r) {
                    if (!r) {
                        tI.recordError(1281);
                        return
                    }
                    _[r >> 2] = T.getBufferParameter(e, t)
                },
                glGetError: function() {
                    var e = T.getError() || tI.lastError;
                    return tI.lastError = 0,
                    e
                },
                glGetFloatv: function(e, t) {
                    tX(e, t, 2)
                },
                glGetFramebufferAttachmentParameteriv: function(e, t, r, n) {
                    var a = T.getFramebufferAttachmentParameter(e, t, r);
                    (a instanceof WebGLRenderbuffer || a instanceof WebGLTexture) && (a = 0 | a.name),
                    _[n >> 2] = a
                },
                glGetIntegerv: function(e, t) {
                    tX(e, t, 0)
                },
                glGetProgramInfoLog: function(e, t, r, n) {
                    var a = T.getProgramInfoLog(tI.programs[e]);
                    null === a && (a = "(unknown error)");
                    var o = t > 0 && n ? ey(a, n, t) : 0;
                    r && (_[r >> 2] = o)
                },
                glGetProgramiv: function(e, t, r) {
                    if (!r || e >= tI.counter) {
                        tI.recordError(1281);
                        return
                    }
                    if (e = tI.programs[e],
                    35716 == t) {
                        var n = T.getProgramInfoLog(e);
                        null === n && (n = "(unknown error)"),
                        _[r >> 2] = n.length + 1
                    } else if (35719 == t) {
                        if (!e.maxUniformLength)
                            for (var a = 0; a < T.getProgramParameter(e, 35718); ++a)
                                e.maxUniformLength = Math.max(e.maxUniformLength, T.getActiveUniform(e, a).name.length + 1);
                        _[r >> 2] = e.maxUniformLength
                    } else if (35722 == t) {
                        if (!e.maxAttributeLength)
                            for (var a = 0; a < T.getProgramParameter(e, 35721); ++a)
                                e.maxAttributeLength = Math.max(e.maxAttributeLength, T.getActiveAttrib(e, a).name.length + 1);
                        _[r >> 2] = e.maxAttributeLength
                    } else if (35381 == t) {
                        if (!e.maxUniformBlockNameLength)
                            for (var a = 0; a < T.getProgramParameter(e, 35382); ++a)
                                e.maxUniformBlockNameLength = Math.max(e.maxUniformBlockNameLength, T.getActiveUniformBlockName(e, a).length + 1);
                        _[r >> 2] = e.maxUniformBlockNameLength
                    } else
                        _[r >> 2] = T.getProgramParameter(e, t)
                },
                glGetRenderbufferParameteriv: function(e, t, r) {
                    if (!r) {
                        tI.recordError(1281);
                        return
                    }
                    _[r >> 2] = T.getRenderbufferParameter(e, t)
                },
                glGetShaderInfoLog: function(e, t, r, n) {
                    var a = T.getShaderInfoLog(tI.shaders[e]);
                    null === a && (a = "(unknown error)");
                    var o = t > 0 && n ? ey(a, n, t) : 0;
                    r && (_[r >> 2] = o)
                },
                glGetShaderPrecisionFormat: function(e, t, r, n) {
                    var a = T.getShaderPrecisionFormat(e, t);
                    _[r >> 2] = a.rangeMin,
                    _[r + 4 >> 2] = a.rangeMax,
                    _[n >> 2] = a.precision
                },
                glGetShaderiv: function(e, t, r) {
                    if (!r) {
                        tI.recordError(1281);
                        return
                    }
                    if (35716 == t) {
                        var n = T.getShaderInfoLog(tI.shaders[e]);
                        null === n && (n = "(unknown error)");
                        var a = n ? n.length + 1 : 0;
                        _[r >> 2] = a
                    } else if (35720 == t) {
                        var o = T.getShaderSource(tI.shaders[e])
                          , i = o ? o.length + 1 : 0;
                        _[r >> 2] = i
                    } else
                        _[r >> 2] = T.getShaderParameter(tI.shaders[e], t)
                },
                glGetString: function(e) {
                    var t = tI.stringCache[e];
                    if (!t) {
                        switch (e) {
                        case 7939:
                            var r = T.getSupportedExtensions() || [];
                            t = tq((r = r.concat(r.map(function(e) {
                                return "GL_" + e
                            }))).join(" "));
                            break;
                        case 7936:
                        case 7937:
                        case 37445:
                        case 37446:
                            var n = T.getParameter(e);
                            n || tI.recordError(1280),
                            t = n && tq(n);
                            break;
                        case 7938:
                            var a = T.getParameter(7938);
                            t = tq(a = tI.currentContext.version >= 2 ? "OpenGL ES 3.0 (" + a + ")" : "OpenGL ES 2.0 (" + a + ")");
                            break;
                        case 35724:
                            var o = T.getParameter(35724)
                              , i = o.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);
                            null !== i && (3 == i[1].length && (i[1] = i[1] + "0"),
                            o = "OpenGL ES GLSL ES " + i[1] + " (" + o + ")"),
                            t = tq(o);
                            break;
                        default:
                            tI.recordError(1280)
                        }
                        tI.stringCache[e] = t
                    }
                    return t
                },
                glGetStringi: function(e, t) {
                    if (tI.currentContext.version < 2)
                        return tI.recordError(1282),
                        0;
                    var r = tI.stringiCache[e];
                    if (r)
                        return t < 0 || t >= r.length ? (tI.recordError(1281),
                        0) : r[t];
                    if (7939 === e) {
                        var n = T.getSupportedExtensions() || [];
                        return (n = (n = n.concat(n.map(function(e) {
                            return "GL_" + e
                        }))).map(function(e) {
                            return tq(e)
                        }),
                        r = tI.stringiCache[e] = n,
                        t < 0 || t >= r.length) ? (tI.recordError(1281),
                        0) : r[t]
                    }
                    return tI.recordError(1280),
                    0
                },
                glGetUniformLocation: function(e, t) {
                    if (t = eb(t),
                    e = tI.programs[e]) {
                        !function(e) {
                            var t, r, n = e.uniformLocsById, a = e.uniformSizeAndIdsByName;
                            if (!n)
                                for (t = 0,
                                e.uniformLocsById = n = {},
                                e.uniformArrayNamesById = {}; t < T.getProgramParameter(e, 35718); ++t) {
                                    var o = T.getActiveUniform(e, t)
                                      , i = o.name
                                      , s = o.size
                                      , l = tQ(i)
                                      , c = l > 0 ? i.slice(0, l) : i
                                      , u = e.uniformIdCounter;
                                    for (e.uniformIdCounter += s,
                                    a[c] = [s, u],
                                    r = 0; r < s; ++r)
                                        n[u] = r,
                                        e.uniformArrayNamesById[u++] = c
                                }
                        }(e);
                        var r = e.uniformLocsById
                          , n = 0
                          , a = t
                          , o = tQ(t);
                        o > 0 && (n = tJ(t.slice(o + 1)) >>> 0,
                        a = t.slice(0, o));
                        var i = e.uniformSizeAndIdsByName[a];
                        if (i && n < i[0] && (r[n += i[1]] = r[n] || T.getUniformLocation(e, t)))
                            return n
                    } else
                        tI.recordError(1281);
                    return -1
                },
                glInvalidateFramebuffer: function(e, t, r) {
                    for (var n = tN[t], a = 0; a < t; a++)
                        n[a] = _[r + 4 * a >> 2];
                    T.invalidateFramebuffer(e, n)
                },
                glInvalidateSubFramebuffer: function(e, t, r, n, a, o, i) {
                    for (var s = tN[t], l = 0; l < t; l++)
                        s[l] = _[r + 4 * l >> 2];
                    T.invalidateSubFramebuffer(e, s, n, a, o, i)
                },
                glIsSync: function(e) {
                    return T.isSync(tI.syncs[e])
                },
                glIsTexture: function(e) {
                    var t = tI.textures[e];
                    return t ? T.isTexture(t) : 0
                },
                glLineWidth: function(e) {
                    T.lineWidth(e)
                },
                glLinkProgram: function(e) {
                    e = tI.programs[e],
                    T.linkProgram(e),
                    e.uniformLocsById = 0,
                    e.uniformSizeAndIdsByName = {}
                },
                glMultiDrawArraysInstancedBaseInstanceWEBGL: function(e, t, r, n, a, o) {
                    T.mdibvbi.multiDrawArraysInstancedBaseInstanceWEBGL(e, _, t >> 2, _, r >> 2, _, n >> 2, v, a >> 2, o)
                },
                glMultiDrawElementsInstancedBaseVertexBaseInstanceWEBGL: function(e, t, r, n, a, o, i, s) {
                    T.mdibvbi.multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(e, _, t >> 2, r, _, n >> 2, _, a >> 2, _, o >> 2, v, i >> 2, s)
                },
                glPixelStorei: function(e, t) {
                    3317 == e && (tI.unpackAlignment = t),
                    T.pixelStorei(e, t)
                },
                glReadBuffer: function(e) {
                    T.readBuffer(e)
                },
                glReadPixels: function(e, t, r, n, a, o, i) {
                    if (tI.currentContext.version >= 2) {
                        if (T.currentPixelPackBufferBinding)
                            T.readPixels(e, t, r, n, a, o, i);
                        else {
                            var s = tK(o);
                            T.readPixels(e, t, r, n, a, o, s, i >> tZ(s))
                        }
                        return
                    }
                    var l = t1(o, a, r, n, i, a);
                    if (!l) {
                        tI.recordError(1280);
                        return
                    }
                    T.readPixels(e, t, r, n, a, o, l)
                },
                glRenderbufferStorage: function(e, t, r, n) {
                    T.renderbufferStorage(e, t, r, n)
                },
                glRenderbufferStorageMultisample: function(e, t, r, n, a) {
                    T.renderbufferStorageMultisample(e, t, r, n, a)
                },
                glSamplerParameterf: function(e, t, r) {
                    T.samplerParameterf(tI.samplers[e], t, r)
                },
                glSamplerParameteri: function(e, t, r) {
                    T.samplerParameteri(tI.samplers[e], t, r)
                },
                glSamplerParameteriv: function(e, t, r) {
                    var n = _[r >> 2];
                    T.samplerParameteri(tI.samplers[e], t, n)
                },
                glScissor: function(e, t, r, n) {
                    T.scissor(e, t, r, n)
                },
                glShaderSource: function(e, t, r, n) {
                    var a = tI.getSource(e, t, r, n);
                    T.shaderSource(tI.shaders[e], a)
                },
                glStencilFunc: function(e, t, r) {
                    T.stencilFunc(e, t, r)
                },
                glStencilFuncSeparate: function(e, t, r, n) {
                    T.stencilFuncSeparate(e, t, r, n)
                },
                glStencilMask: function(e) {
                    T.stencilMask(e)
                },
                glStencilMaskSeparate: function(e, t) {
                    T.stencilMaskSeparate(e, t)
                },
                glStencilOp: function(e, t, r) {
                    T.stencilOp(e, t, r)
                },
                glStencilOpSeparate: function(e, t, r, n) {
                    T.stencilOpSeparate(e, t, r, n)
                },
                glTexImage2D: function(e, t, r, n, a, o, i, s, l) {
                    if (tI.currentContext.version >= 2) {
                        if (T.currentPixelUnpackBufferBinding)
                            T.texImage2D(e, t, r, n, a, o, i, s, l);
                        else if (l) {
                            var c = tK(s);
                            T.texImage2D(e, t, r, n, a, o, i, s, c, l >> tZ(c))
                        } else
                            T.texImage2D(e, t, r, n, a, o, i, s, null);
                        return
                    }
                    T.texImage2D(e, t, r, n, a, o, i, s, l ? t1(s, i, n, a, l, r) : null)
                },
                glTexParameterf: function(e, t, r) {
                    T.texParameterf(e, t, r)
                },
                glTexParameterfv: function(e, t, r) {
                    var n = b[r >> 2];
                    T.texParameterf(e, t, n)
                },
                glTexParameteri: function(e, t, r) {
                    T.texParameteri(e, t, r)
                },
                glTexParameteriv: function(e, t, r) {
                    var n = _[r >> 2];
                    T.texParameteri(e, t, n)
                },
                glTexStorage2D: function(e, t, r, n, a) {
                    T.texStorage2D(e, t, r, n, a)
                },
                glTexSubImage2D: function(e, t, r, n, a, o, i, s, l) {
                    if (tI.currentContext.version >= 2) {
                        if (T.currentPixelUnpackBufferBinding)
                            T.texSubImage2D(e, t, r, n, a, o, i, s, l);
                        else if (l) {
                            var c = tK(s);
                            T.texSubImage2D(e, t, r, n, a, o, i, s, c, l >> tZ(c))
                        } else
                            T.texSubImage2D(e, t, r, n, a, o, i, s, null);
                        return
                    }
                    var u = null;
                    l && (u = t1(s, i, a, o, l, 0)),
                    T.texSubImage2D(e, t, r, n, a, o, i, s, u)
                },
                glUniform1f: function(e, t) {
                    T.uniform1f(t2(e), t)
                },
                glUniform1fv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform1fv(t2(e), b, r >> 2, t);
                        return
                    }
                    if (t <= 288)
                        for (var n = t0[t - 1], a = 0; a < t; ++a)
                            n[a] = b[r + 4 * a >> 2];
                    else
                        var n = b.subarray(r >> 2, r + 4 * t >> 2);
                    T.uniform1fv(t2(e), n)
                },
                glUniform1i: function(e, t) {
                    T.uniform1i(t2(e), t)
                },
                glUniform1iv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform1iv(t2(e), _, r >> 2, t);
                        return
                    }
                    if (t <= 288)
                        for (var n = t3[t - 1], a = 0; a < t; ++a)
                            n[a] = _[r + 4 * a >> 2];
                    else
                        var n = _.subarray(r >> 2, r + 4 * t >> 2);
                    T.uniform1iv(t2(e), n)
                },
                glUniform2f: function(e, t, r) {
                    T.uniform2f(t2(e), t, r)
                },
                glUniform2fv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform2fv(t2(e), b, r >> 2, 2 * t);
                        return
                    }
                    if (t <= 144)
                        for (var n = t0[2 * t - 1], a = 0; a < 2 * t; a += 2)
                            n[a] = b[r + 4 * a >> 2],
                            n[a + 1] = b[r + (4 * a + 4) >> 2];
                    else
                        var n = b.subarray(r >> 2, r + 8 * t >> 2);
                    T.uniform2fv(t2(e), n)
                },
                glUniform2i: function(e, t, r) {
                    T.uniform2i(t2(e), t, r)
                },
                glUniform2iv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform2iv(t2(e), _, r >> 2, 2 * t);
                        return
                    }
                    if (t <= 144)
                        for (var n = t3[2 * t - 1], a = 0; a < 2 * t; a += 2)
                            n[a] = _[r + 4 * a >> 2],
                            n[a + 1] = _[r + (4 * a + 4) >> 2];
                    else
                        var n = _.subarray(r >> 2, r + 8 * t >> 2);
                    T.uniform2iv(t2(e), n)
                },
                glUniform3f: function(e, t, r, n) {
                    T.uniform3f(t2(e), t, r, n)
                },
                glUniform3fv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform3fv(t2(e), b, r >> 2, 3 * t);
                        return
                    }
                    if (t <= 96)
                        for (var n = t0[3 * t - 1], a = 0; a < 3 * t; a += 3)
                            n[a] = b[r + 4 * a >> 2],
                            n[a + 1] = b[r + (4 * a + 4) >> 2],
                            n[a + 2] = b[r + (4 * a + 8) >> 2];
                    else
                        var n = b.subarray(r >> 2, r + 12 * t >> 2);
                    T.uniform3fv(t2(e), n)
                },
                glUniform3i: function(e, t, r, n) {
                    T.uniform3i(t2(e), t, r, n)
                },
                glUniform3iv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform3iv(t2(e), _, r >> 2, 3 * t);
                        return
                    }
                    if (t <= 96)
                        for (var n = t3[3 * t - 1], a = 0; a < 3 * t; a += 3)
                            n[a] = _[r + 4 * a >> 2],
                            n[a + 1] = _[r + (4 * a + 4) >> 2],
                            n[a + 2] = _[r + (4 * a + 8) >> 2];
                    else
                        var n = _.subarray(r >> 2, r + 12 * t >> 2);
                    T.uniform3iv(t2(e), n)
                },
                glUniform4f: function(e, t, r, n, a) {
                    T.uniform4f(t2(e), t, r, n, a)
                },
                glUniform4fv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform4fv(t2(e), b, r >> 2, 4 * t);
                        return
                    }
                    if (t <= 72) {
                        var n = t0[4 * t - 1]
                          , a = b;
                        r >>= 2;
                        for (var o = 0; o < 4 * t; o += 4) {
                            var i = r + o;
                            n[o] = a[i],
                            n[o + 1] = a[i + 1],
                            n[o + 2] = a[i + 2],
                            n[o + 3] = a[i + 3]
                        }
                    } else
                        var n = b.subarray(r >> 2, r + 16 * t >> 2);
                    T.uniform4fv(t2(e), n)
                },
                glUniform4i: function(e, t, r, n, a) {
                    T.uniform4i(t2(e), t, r, n, a)
                },
                glUniform4iv: function(e, t, r) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniform4iv(t2(e), _, r >> 2, 4 * t);
                        return
                    }
                    if (t <= 72)
                        for (var n = t3[4 * t - 1], a = 0; a < 4 * t; a += 4)
                            n[a] = _[r + 4 * a >> 2],
                            n[a + 1] = _[r + (4 * a + 4) >> 2],
                            n[a + 2] = _[r + (4 * a + 8) >> 2],
                            n[a + 3] = _[r + (4 * a + 12) >> 2];
                    else
                        var n = _.subarray(r >> 2, r + 16 * t >> 2);
                    T.uniform4iv(t2(e), n)
                },
                glUniformMatrix2fv: function(e, t, r, n) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniformMatrix2fv(t2(e), !!r, b, n >> 2, 4 * t);
                        return
                    }
                    if (t <= 72)
                        for (var a = t0[4 * t - 1], o = 0; o < 4 * t; o += 4)
                            a[o] = b[n + 4 * o >> 2],
                            a[o + 1] = b[n + (4 * o + 4) >> 2],
                            a[o + 2] = b[n + (4 * o + 8) >> 2],
                            a[o + 3] = b[n + (4 * o + 12) >> 2];
                    else
                        var a = b.subarray(n >> 2, n + 16 * t >> 2);
                    T.uniformMatrix2fv(t2(e), !!r, a)
                },
                glUniformMatrix3fv: function(e, t, r, n) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniformMatrix3fv(t2(e), !!r, b, n >> 2, 9 * t);
                        return
                    }
                    if (t <= 32)
                        for (var a = t0[9 * t - 1], o = 0; o < 9 * t; o += 9)
                            a[o] = b[n + 4 * o >> 2],
                            a[o + 1] = b[n + (4 * o + 4) >> 2],
                            a[o + 2] = b[n + (4 * o + 8) >> 2],
                            a[o + 3] = b[n + (4 * o + 12) >> 2],
                            a[o + 4] = b[n + (4 * o + 16) >> 2],
                            a[o + 5] = b[n + (4 * o + 20) >> 2],
                            a[o + 6] = b[n + (4 * o + 24) >> 2],
                            a[o + 7] = b[n + (4 * o + 28) >> 2],
                            a[o + 8] = b[n + (4 * o + 32) >> 2];
                    else
                        var a = b.subarray(n >> 2, n + 36 * t >> 2);
                    T.uniformMatrix3fv(t2(e), !!r, a)
                },
                glUniformMatrix4fv: function(e, t, r, n) {
                    if (tI.currentContext.version >= 2) {
                        t && T.uniformMatrix4fv(t2(e), !!r, b, n >> 2, 16 * t);
                        return
                    }
                    if (t <= 18) {
                        var a = t0[16 * t - 1]
                          , o = b;
                        n >>= 2;
                        for (var i = 0; i < 16 * t; i += 16) {
                            var s = n + i;
                            a[i] = o[s],
                            a[i + 1] = o[s + 1],
                            a[i + 2] = o[s + 2],
                            a[i + 3] = o[s + 3],
                            a[i + 4] = o[s + 4],
                            a[i + 5] = o[s + 5],
                            a[i + 6] = o[s + 6],
                            a[i + 7] = o[s + 7],
                            a[i + 8] = o[s + 8],
                            a[i + 9] = o[s + 9],
                            a[i + 10] = o[s + 10],
                            a[i + 11] = o[s + 11],
                            a[i + 12] = o[s + 12],
                            a[i + 13] = o[s + 13],
                            a[i + 14] = o[s + 14],
                            a[i + 15] = o[s + 15]
                        }
                    } else
                        var a = b.subarray(n >> 2, n + 64 * t >> 2);
                    T.uniformMatrix4fv(t2(e), !!r, a)
                },
                glUseProgram: function(e) {
                    e = tI.programs[e],
                    T.useProgram(e),
                    T.currentProgram = e
                },
                glVertexAttrib1f: function(e, t) {
                    T.vertexAttrib1f(e, t)
                },
                glVertexAttrib2fv: function(e, t) {
                    T.vertexAttrib2f(e, b[t >> 2], b[t + 4 >> 2])
                },
                glVertexAttrib3fv: function(e, t) {
                    T.vertexAttrib3f(e, b[t >> 2], b[t + 4 >> 2], b[t + 8 >> 2])
                },
                glVertexAttrib4fv: function(e, t) {
                    T.vertexAttrib4f(e, b[t >> 2], b[t + 4 >> 2], b[t + 8 >> 2], b[t + 12 >> 2])
                },
                glVertexAttribDivisor: function(e, t) {
                    T.vertexAttribDivisor(e, t)
                },
                glVertexAttribIPointer: function(e, t, r, n, a) {
                    T.vertexAttribIPointer(e, t, r, n, a)
                },
                glVertexAttribPointer: function(e, t, r, n, a, o) {
                    T.vertexAttribPointer(e, t, r, !!n, a, o)
                },
                glViewport: function(e, t, r, n) {
                    T.viewport(e, t, r, n)
                },
                glWaitSync: function(e, t, r, n) {
                    var a = tj(r, n);
                    T.waitSync(tI.syncs[e], t, a)
                },
                invoke_ii: function(e, t) {
                    var r = rh();
                    try {
                        return te(e)(t)
                    } catch (e) {
                        if (rd(r),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_iii: function(e, t, r) {
                    var n = rh();
                    try {
                        return te(e)(t, r)
                    } catch (e) {
                        if (rd(n),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_iiii: function(e, t, r, n) {
                    var a = rh();
                    try {
                        return te(e)(t, r, n)
                    } catch (e) {
                        if (rd(a),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_iiiii: function(e, t, r, n, a) {
                    var o = rh();
                    try {
                        return te(e)(t, r, n, a)
                    } catch (e) {
                        if (rd(o),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_iiiiii: function(e, t, r, n, a, o) {
                    var i = rh();
                    try {
                        return te(e)(t, r, n, a, o)
                    } catch (e) {
                        if (rd(i),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_iiiiiii: function(e, t, r, n, a, o, i) {
                    var s = rh();
                    try {
                        return te(e)(t, r, n, a, o, i)
                    } catch (e) {
                        if (rd(s),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_iiiiiiiiii: function(e, t, r, n, a, o, i, s, l, c) {
                    var u = rh();
                    try {
                        return te(e)(t, r, n, a, o, i, s, l, c)
                    } catch (e) {
                        if (rd(u),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_v: function(e) {
                    var t = rh();
                    try {
                        te(e)()
                    } catch (e) {
                        if (rd(t),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_vi: function(e, t) {
                    var r = rh();
                    try {
                        te(e)(t)
                    } catch (e) {
                        if (rd(r),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_vii: function(e, t, r) {
                    var n = rh();
                    try {
                        te(e)(t, r)
                    } catch (e) {
                        if (rd(n),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_viii: function(e, t, r, n) {
                    var a = rh();
                    try {
                        te(e)(t, r, n)
                    } catch (e) {
                        if (rd(a),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_viiii: function(e, t, r, n, a) {
                    var o = rh();
                    try {
                        te(e)(t, r, n, a)
                    } catch (e) {
                        if (rd(o),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_viiiii: function(e, t, r, n, a, o) {
                    var i = rh();
                    try {
                        te(e)(t, r, n, a, o)
                    } catch (e) {
                        if (rd(i),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_viiiiii: function(e, t, r, n, a, o, i) {
                    var s = rh();
                    try {
                        te(e)(t, r, n, a, o, i)
                    } catch (e) {
                        if (rd(s),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                invoke_viiiiiiiii: function(e, t, r, n, a, o, i, s, l, c) {
                    var u = rh();
                    try {
                        te(e)(t, r, n, a, o, i, s, l, c)
                    } catch (e) {
                        if (rd(u),
                        e !== e + 0)
                            throw e;
                        rc(1, 0)
                    }
                },
                strftime_l: (e, t, r, n, a) => re(e, t, r, n)
            };
            (function() {
                var t, r, n, a, o = {
                    env: ra,
                    wasi_snapshot_preview1: ra
                };
                function i(t, r) {
                    var n, a = t.exports;
                    return z(h = (d = a).memory, "memory not found in wasm exports"),
                    Y(),
                    z(A = d.__indirect_function_table, "table not found in wasm exports"),
                    n = d.__wasm_call_ctors,
                    J.unshift(n),
                    function(t) {
                        if (Z--,
                        e.monitorRunDependencies && e.monitorRunDependencies(Z),
                        t ? (z(er[t]),
                        delete er[t]) : $("warning: run dependency removed without ID"),
                        0 == Z && (null !== ee && (clearInterval(ee),
                        ee = null),
                        et)) {
                            var r = et;
                            et = null,
                            r()
                        }
                    }("wasm-instantiate"),
                    a
                }
                t = "wasm-instantiate",
                Z++,
                e.monitorRunDependencies && e.monitorRunDependencies(Z),
                t ? (z(!er[t]),
                er[t] = 1,
                null === ee && "undefined" != typeof setInterval && (ee = setInterval( () => {
                    if (V) {
                        clearInterval(ee),
                        ee = null;
                        return
                    }
                    var e = !1;
                    for (var t in er)
                        e || (e = !0,
                        $("still waiting on run dependencies:")),
                        $("dependency: " + t);
                    e && $("(end of list)")
                }
                , 1e4))) : $("warning: run dependency added without ID");
                var l = e;
                if (e.instantiateWasm)
                    try {
                        return e.instantiateWasm(o, i)
                    } catch (e) {
                        $("Module.instantiateWasm callback failed with error: " + e),
                        s(e)
                    }
                (r = f,
                n = w,
                a = function(t) {
                    z(e === l, "the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),
                    l = null,
                    i(t.instance)
                }
                ,
                r || "function" != typeof WebAssembly.instantiateStreaming || eo(n) || ei(n) || O || "function" != typeof fetch ? ec(n, o, a) : fetch(n, {
                    credentials: "same-origin"
                }).then(e => WebAssembly.instantiateStreaming(e, o).then(a, function(e) {
                    return $("wasm streaming compile failed: " + e),
                    $("falling back to ArrayBuffer instantiation"),
                    ec(n, o, a)
                }))).catch(s)
            }
            )(),
            es("__wasm_call_ctors");
            var ro = e._malloc = es("malloc");
            es("__errno_location");
            var ri = e._free = es("free")
              , rs = e._fflush = es("fflush");
            es("setTempRet0");
            var rl = es("__getTypeName");
            e.__embind_initialize_bindings = es("_embind_initialize_bindings");
            var rc = es("setThrew")
              , ru = () => (ru = d.emscripten_stack_init)()
              , rf = () => (rf = d.emscripten_stack_get_end)()
              , rh = es("stackSave")
              , rd = es("stackRestore");
            function rp() {
                var t;
                function r() {
                    !P && (P = !0,
                    e.calledRun = !0,
                    V || (z(!K),
                    K = !0,
                    X(),
                    ep(J),
                    i(e),
                    e.onRuntimeInitialized && e.onRuntimeInitialized(),
                    z(!e._main, 'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),
                    function() {
                        if (X(),
                        e.postRun)
                            for ("function" == typeof e.postRun && (e.postRun = [e.postRun]); e.postRun.length; ) {
                                var t;
                                t = e.postRun.shift(),
                                Q.unshift(t)
                            }
                        ep(Q)
                    }()))
                }
                !(Z > 0) && (ru(),
                z((3 & (t = rf())) == 0),
                0 == t && (t += 4),
                v[t >> 2] = 34821223,
                v[t + 4 >> 2] = 2310721022,
                v[0] = 1668509029,
                function() {
                    if (e.preRun)
                        for ("function" == typeof e.preRun && (e.preRun = [e.preRun]); e.preRun.length; ) {
                            var t;
                            t = e.preRun.shift(),
                            q.unshift(t)
                        }
                    ep(q)
                }(),
                Z > 0 || (e.setStatus ? (e.setStatus("Running..."),
                setTimeout(function() {
                    setTimeout(function() {
                        e.setStatus("")
                    }, 1),
                    r()
                }, 1)) : r(),
                X()))
            }
            if (es("stackAlloc"),
            es("__cxa_demangle"),
            e.dynCall_viji = es("dynCall_viji"),
            e.dynCall_vijiii = es("dynCall_vijiii"),
            e.dynCall_viiiiij = es("dynCall_viiiiij"),
            e.dynCall_jiiiijiiiii = es("dynCall_jiiiijiiiii"),
            e.dynCall_viiij = es("dynCall_viiij"),
            e.dynCall_jii = es("dynCall_jii"),
            e.dynCall_vij = es("dynCall_vij"),
            e.dynCall_iiij = es("dynCall_iiij"),
            e.dynCall_iiiij = es("dynCall_iiiij"),
            e.dynCall_viij = es("dynCall_viij"),
            e.dynCall_ji = es("dynCall_ji"),
            e.dynCall_iij = es("dynCall_iij"),
            e.dynCall_jiiiii = es("dynCall_jiiiii"),
            e.dynCall_jiiiiii = es("dynCall_jiiiiii"),
            e.dynCall_jiiiiji = es("dynCall_jiiiiji"),
            e.dynCall_iijj = es("dynCall_iijj"),
            e.dynCall_iiiji = es("dynCall_iiiji"),
            e.dynCall_iiji = es("dynCall_iiji"),
            e.dynCall_iijjiii = es("dynCall_iijjiii"),
            e.dynCall_vijjjii = es("dynCall_vijjjii"),
            e.dynCall_jiji = es("dynCall_jiji"),
            e.dynCall_viijii = es("dynCall_viijii"),
            e.dynCall_iiiiij = es("dynCall_iiiiij"),
            e.dynCall_iiiiijj = es("dynCall_iiiiijj"),
            e.dynCall_iiiiiijj = es("dynCall_iiiiiijj"),
            ["writeI53ToI64Clamped", "writeI53ToI64Signaling", "writeI53ToU64Clamped", "writeI53ToU64Signaling", "convertU32PairToI53", "zeroMemory", "ydayFromDate", "inetPton4", "inetNtop4", "inetPton6", "inetNtop6", "readSockaddr", "writeSockaddr", "getHostByName", "initRandomFill", "randomFill", "getCallstack", "emscriptenLog", "convertPCtoSourceLocation", "readEmAsmArgs", "jstoi_s", "listenOnce", "autoResumeAudioContext", "setWasmTableEntry", "handleException", "runtimeKeepalivePush", "runtimeKeepalivePop", "callUserCallback", "maybeExit", "safeSetTimeout", "asmjsMangle", "asyncLoad", "alignMemory", "mmapAlloc", "getNativeTypeSize", "STACK_SIZE", "STACK_ALIGN", "POINTER_SIZE", "ASSERTIONS", "getCFunc", "ccall", "cwrap", "uleb128Encode", "sigToWasmTypes", "generateFuncType", "convertJsFunctionToWasm", "getEmptyTableSlot", "updateTableMap", "getFunctionAddress", "addFunction", "removeFunction", "reallyNegative", "unSign", "strLen", "reSign", "formatString", "intArrayToString", "AsciiToString", "registerKeyEventCallback", "maybeCStringToJsString", "findEventTarget", "findCanvasEventTarget", "getBoundingClientRect", "fillMouseEventData", "registerMouseEventCallback", "registerWheelEventCallback", "registerUiEventCallback", "registerFocusEventCallback", "fillDeviceOrientationEventData", "registerDeviceOrientationEventCallback", "fillDeviceMotionEventData", "registerDeviceMotionEventCallback", "screenOrientation", "fillOrientationChangeEventData", "registerOrientationChangeEventCallback", "fillFullscreenChangeEventData", "registerFullscreenChangeEventCallback", "JSEvents_requestFullscreen", "JSEvents_resizeCanvasForFullscreen", "registerRestoreOldStyle", "hideEverythingExceptGivenElement", "restoreHiddenElements", "setLetterbox", "softFullscreenResizeWebGLRenderTarget", "doRequestFullscreen", "fillPointerlockChangeEventData", "registerPointerlockChangeEventCallback", "registerPointerlockErrorEventCallback", "requestPointerLock", "fillVisibilityChangeEventData", "registerVisibilityChangeEventCallback", "registerTouchEventCallback", "fillGamepadEventData", "registerGamepadEventCallback", "registerBeforeUnloadEventCallback", "fillBatteryEventData", "battery", "registerBatteryEventCallback", "setCanvasElementSize", "getCanvasElementSize", "checkWasiClock", "wasiRightsToMuslOFlags", "wasiOFlagsToMuslOFlags", "createDyncallWrapper", "setImmediateWrapped", "clearImmediateWrapped", "polyfillSetImmediate", "getPromise", "makePromise", "idsToPromises", "makePromiseCallback", "ExceptionInfo", "findMatchingCatch", "setMainLoop", "getSocketFromFD", "getSocketAddress", "GLFW_Window", "emscriptenWebGLGetUniform", "emscriptenWebGLGetVertexAttrib", "__glGetActiveAttribOrUniform", "emscriptenWebGLGetIndexed", "writeGLArray", "registerWebGlEventCallback", "registerInheritedInstance", "unregisterInheritedInstance", "validateThis"].forEach(function(e) {
                "undefined" == typeof globalThis || Object.getOwnPropertyDescriptor(globalThis, e) || Object.defineProperty(globalThis, e, {
                    configurable: !0,
                    get() {
                        var t = "`" + e + "` is a library symbol and not included by default; add it to your library.js __deps or to DEFAULT_LIBRARY_FUNCS_TO_INCLUDE on the command line"
                          , r = e;
                        r.startsWith("_") || (r = "$" + e),
                        t += " (e.g. -sDEFAULT_LIBRARY_FUNCS_TO_INCLUDE='" + r + "')",
                        ef(e) && (t += ". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),
                        eA(t)
                    }
                }),
                eh(e)
            }),
            ["run", "addOnPreRun", "addOnInit", "addOnPreMain", "addOnExit", "addOnPostRun", "addRunDependency", "removeRunDependency", "FS_createFolder", "FS_createPath", "FS_createDataFile", "FS_createLazyFile", "FS_createLink", "FS_createDevice", "FS_unlink", "out", "err", "callMain", "abort", "keepRuntimeAlive", "wasmMemory", "wasmTable", "wasmExports", "stackAlloc", "stackSave", "stackRestore", "getTempRet0", "setTempRet0", "writeStackCookie", "checkStackCookie", "writeI53ToI64", "readI53FromI64", "readI53FromU64", "convertI32PairToI53", "convertI32PairToI53Checked", "ptrToString", "exitJS", "getHeapMax", "abortOnCannotGrowMemory", "growMemory", "ENV", "MONTH_DAYS_REGULAR", "MONTH_DAYS_LEAP", "MONTH_DAYS_REGULAR_CUMULATIVE", "MONTH_DAYS_LEAP_CUMULATIVE", "isLeapYear", "arraySum", "addDays", "ERRNO_CODES", "ERRNO_MESSAGES", "setErrNo", "DNS", "Protocols", "Sockets", "timers", "warnOnce", "UNWIND_CACHE", "readEmAsmArgsArray", "jstoi_q", "getExecutableName", "dynCallLegacy", "getDynCaller", "dynCall", "getWasmTableEntry", "handleAllocatorInit", "HandleAllocator", "freeTableIndexes", "functionsInTableMap", "setValue", "getValue", "PATH", "PATH_FS", "UTF8Decoder", "UTF8ArrayToString", "UTF8ToString", "stringToUTF8Array", "stringToUTF8", "lengthBytesUTF8", "intArrayFromString", "stringToAscii", "UTF16Decoder", "UTF16ToString", "stringToUTF16", "lengthBytesUTF16", "UTF32ToString", "stringToUTF32", "lengthBytesUTF32", "stringToNewUTF8", "stringToUTF8OnStack", "writeArrayToMemory", "JSEvents", "specialHTMLTargets", "currentFullscreenStrategy", "restoreOldWindowedStyle", "demangle", "demangleAll", "jsStackTrace", "stackTrace", "ExitStatus", "getEnvStrings", "flush_NO_FILESYSTEM", "promiseMap", "uncaughtExceptionCount", "exceptionLast", "exceptionCaught", "Browser", "wget", "SYSCALLS", "GLFW", "tempFixedLengthArray", "miniTempWebGLFloatBuffers", "miniTempWebGLIntBuffers", "heapObjectForWebGLType", "heapAccessShiftForWebGLHeap", "webgl_enable_ANGLE_instanced_arrays", "webgl_enable_OES_vertex_array_object", "webgl_enable_WEBGL_draw_buffers", "webgl_enable_WEBGL_multi_draw", "GL", "emscriptenWebGLGet", "computeUnpackAlignedImageSize", "colorChannelsInGlTextureFormat", "emscriptenWebGLGetTexPixelData", "__glGenObject", "webglGetUniformLocation", "webglPrepareUniformLocationsBeforeFirstUse", "webglGetLeftBracePos", "webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance", "webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance", "emscripten_webgl_power_preferences", "InternalError", "BindingError", "throwInternalError", "throwBindingError", "registeredTypes", "awaitingDependencies", "typeDependencies", "tupleRegistrations", "structRegistrations", "sharedRegisterType", "whenDependentTypesAreResolved", "embind_charCodes", "embind_init_charCodes", "readLatin1String", "getTypeName", "heap32VectorToArray", "requireRegisteredType", "UnboundTypeError", "PureVirtualError", "init_embind", "throwUnboundTypeError", "ensureOverloadTable", "exposePublicSymbol", "replacePublicSymbol", "extendError", "createNamedFunction", "embindRepr", "registeredInstances", "getBasestPointer", "getInheritedInstance", "getInheritedInstanceCount", "getLiveInheritedInstances", "registeredPointers", "registerType", "getShiftFromSize", "integerReadValueFromPointer", "enumReadValueFromPointer", "floatReadValueFromPointer", "simpleReadValueFromPointer", "runDestructors", "craftInvokerFunction", "embind__requireFunction", "genericPointerToWireType", "constNoSmartPtrRawPointerToWireType", "nonConstNoSmartPtrRawPointerToWireType", "init_RegisteredPointer", "RegisteredPointer", "RegisteredPointer_getPointee", "RegisteredPointer_destructor", "RegisteredPointer_deleteObject", "RegisteredPointer_fromWireType", "runDestructor", "releaseClassHandle", "finalizationRegistry", "detachFinalizer_deps", "detachFinalizer", "attachFinalizer", "makeClassHandle", "init_ClassHandle", "ClassHandle", "ClassHandle_isAliasOf", "throwInstanceAlreadyDeleted", "ClassHandle_clone", "ClassHandle_delete", "deletionQueue", "ClassHandle_isDeleted", "ClassHandle_deleteLater", "flushPendingDeletes", "delayFunction", "setDelayFunction", "RegisteredClass", "shallowCopyInternalPointer", "downcastPointer", "upcastPointer", "char_0", "char_9", "makeLegalFunctionName", "emval_handles", "emval_symbols", "init_emval", "count_emval_handles", "getStringOrSymbol", "Emval", "emval_newers", "craftEmvalAllocator", "emval_get_global", "emval_lookupTypes", "emval_allocateDestructors", "emval_methodCallers", "emval_addMethodCaller", "emval_registeredMethods"].forEach(eh),
            et = function e() {
                P || rp(),
                P || (et = e)
            }
            ,
            e.preInit)
                for ("function" == typeof e.preInit && (e.preInit = [e.preInit]); e.preInit.length > 0; )
                    e.preInit.pop()();
            return rp(),
            e.ready
        }
        );
        e.exports = i
    },
    15153: function(e) {
        !function() {
            "use strict";
            var t = {
                114: function(e) {
                    function t(e) {
                        if ("string" != typeof e)
                            throw TypeError("Path must be a string. Received " + JSON.stringify(e))
                    }
                    function r(e, t) {
                        for (var r, n = "", a = 0, o = -1, i = 0, s = 0; s <= e.length; ++s) {
                            if (s < e.length)
                                r = e.charCodeAt(s);
                            else if (47 === r)
                                break;
                            else
                                r = 47;
                            if (47 === r) {
                                if (o === s - 1 || 1 === i)
                                    ;
                                else if (o !== s - 1 && 2 === i) {
                                    if (n.length < 2 || 2 !== a || 46 !== n.charCodeAt(n.length - 1) || 46 !== n.charCodeAt(n.length - 2)) {
                                        if (n.length > 2) {
                                            var l = n.lastIndexOf("/");
                                            if (l !== n.length - 1) {
                                                -1 === l ? (n = "",
                                                a = 0) : a = (n = n.slice(0, l)).length - 1 - n.lastIndexOf("/"),
                                                o = s,
                                                i = 0;
                                                continue
                                            }
                                        } else if (2 === n.length || 1 === n.length) {
                                            n = "",
                                            a = 0,
                                            o = s,
                                            i = 0;
                                            continue
                                        }
                                    }
                                    t && (n.length > 0 ? n += "/.." : n = "..",
                                    a = 2)
                                } else
                                    n.length > 0 ? n += "/" + e.slice(o + 1, s) : n = e.slice(o + 1, s),
                                    a = s - o - 1;
                                o = s,
                                i = 0
                            } else
                                46 === r && -1 !== i ? ++i : i = -1
                        }
                        return n
                    }
                    var n = {
                        resolve: function() {
                            for (var e, n, a = "", o = !1, i = arguments.length - 1; i >= -1 && !o; i--)
                                i >= 0 ? n = arguments[i] : (void 0 === e && (e = ""),
                                n = e),
                                t(n),
                                0 !== n.length && (a = n + "/" + a,
                                o = 47 === n.charCodeAt(0));
                            return (a = r(a, !o),
                            o) ? a.length > 0 ? "/" + a : "/" : a.length > 0 ? a : "."
                        },
                        normalize: function(e) {
                            if (t(e),
                            0 === e.length)
                                return ".";
                            var n = 47 === e.charCodeAt(0)
                              , a = 47 === e.charCodeAt(e.length - 1);
                            return (0 !== (e = r(e, !n)).length || n || (e = "."),
                            e.length > 0 && a && (e += "/"),
                            n) ? "/" + e : e
                        },
                        isAbsolute: function(e) {
                            return t(e),
                            e.length > 0 && 47 === e.charCodeAt(0)
                        },
                        join: function() {
                            if (0 == arguments.length)
                                return ".";
                            for (var e, r = 0; r < arguments.length; ++r) {
                                var a = arguments[r];
                                t(a),
                                a.length > 0 && (void 0 === e ? e = a : e += "/" + a)
                            }
                            return void 0 === e ? "." : n.normalize(e)
                        },
                        relative: function(e, r) {
                            if (t(e),
                            t(r),
                            e === r || (e = n.resolve(e)) === (r = n.resolve(r)))
                                return "";
                            for (var a = 1; a < e.length && 47 === e.charCodeAt(a); ++a)
                                ;
                            for (var o = e.length, i = o - a, s = 1; s < r.length && 47 === r.charCodeAt(s); ++s)
                                ;
                            for (var l = r.length - s, c = i < l ? i : l, u = -1, f = 0; f <= c; ++f) {
                                if (f === c) {
                                    if (l > c) {
                                        if (47 === r.charCodeAt(s + f))
                                            return r.slice(s + f + 1);
                                        if (0 === f)
                                            return r.slice(s + f)
                                    } else
                                        i > c && (47 === e.charCodeAt(a + f) ? u = f : 0 === f && (u = 0));
                                    break
                                }
                                var h = e.charCodeAt(a + f);
                                if (h !== r.charCodeAt(s + f))
                                    break;
                                47 === h && (u = f)
                            }
                            var d = "";
                            for (f = a + u + 1; f <= o; ++f)
                                (f === o || 47 === e.charCodeAt(f)) && (0 === d.length ? d += ".." : d += "/..");
                            return d.length > 0 ? d + r.slice(s + u) : (s += u,
                            47 === r.charCodeAt(s) && ++s,
                            r.slice(s))
                        },
                        _makeLong: function(e) {
                            return e
                        },
                        dirname: function(e) {
                            if (t(e),
                            0 === e.length)
                                return ".";
                            for (var r = e.charCodeAt(0), n = 47 === r, a = -1, o = !0, i = e.length - 1; i >= 1; --i)
                                if (47 === (r = e.charCodeAt(i))) {
                                    if (!o) {
                                        a = i;
                                        break
                                    }
                                } else
                                    o = !1;
                            return -1 === a ? n ? "/" : "." : n && 1 === a ? "//" : e.slice(0, a)
                        },
                        basename: function(e, r) {
                            if (void 0 !== r && "string" != typeof r)
                                throw TypeError('"ext" argument must be a string');
                            t(e);
                            var n, a = 0, o = -1, i = !0;
                            if (void 0 !== r && r.length > 0 && r.length <= e.length) {
                                if (r.length === e.length && r === e)
                                    return "";
                                var s = r.length - 1
                                  , l = -1;
                                for (n = e.length - 1; n >= 0; --n) {
                                    var c = e.charCodeAt(n);
                                    if (47 === c) {
                                        if (!i) {
                                            a = n + 1;
                                            break
                                        }
                                    } else
                                        -1 === l && (i = !1,
                                        l = n + 1),
                                        s >= 0 && (c === r.charCodeAt(s) ? -1 == --s && (o = n) : (s = -1,
                                        o = l))
                                }
                                return a === o ? o = l : -1 === o && (o = e.length),
                                e.slice(a, o)
                            }
                            for (n = e.length - 1; n >= 0; --n)
                                if (47 === e.charCodeAt(n)) {
                                    if (!i) {
                                        a = n + 1;
                                        break
                                    }
                                } else
                                    -1 === o && (i = !1,
                                    o = n + 1);
                            return -1 === o ? "" : e.slice(a, o)
                        },
                        extname: function(e) {
                            t(e);
                            for (var r = -1, n = 0, a = -1, o = !0, i = 0, s = e.length - 1; s >= 0; --s) {
                                var l = e.charCodeAt(s);
                                if (47 === l) {
                                    if (!o) {
                                        n = s + 1;
                                        break
                                    }
                                    continue
                                }
                                -1 === a && (o = !1,
                                a = s + 1),
                                46 === l ? -1 === r ? r = s : 1 !== i && (i = 1) : -1 !== r && (i = -1)
                            }
                            return -1 === r || -1 === a || 0 === i || 1 === i && r === a - 1 && r === n + 1 ? "" : e.slice(r, a)
                        },
                        format: function(e) {
                            var t, r;
                            if (null === e || "object" != typeof e)
                                throw TypeError('The "pathObject" argument must be of type Object. Received type ' + typeof e);
                            return t = e.dir || e.root,
                            r = e.base || (e.name || "") + (e.ext || ""),
                            t ? t === e.root ? t + r : t + "/" + r : r
                        },
                        parse: function(e) {
                            t(e);
                            var r, n = {
                                root: "",
                                dir: "",
                                base: "",
                                ext: "",
                                name: ""
                            };
                            if (0 === e.length)
                                return n;
                            var a = e.charCodeAt(0)
                              , o = 47 === a;
                            o ? (n.root = "/",
                            r = 1) : r = 0;
                            for (var i = -1, s = 0, l = -1, c = !0, u = e.length - 1, f = 0; u >= r; --u) {
                                if (47 === (a = e.charCodeAt(u))) {
                                    if (!c) {
                                        s = u + 1;
                                        break
                                    }
                                    continue
                                }
                                -1 === l && (c = !1,
                                l = u + 1),
                                46 === a ? -1 === i ? i = u : 1 !== f && (f = 1) : -1 !== i && (f = -1)
                            }
                            return -1 === i || -1 === l || 0 === f || 1 === f && i === l - 1 && i === s + 1 ? -1 !== l && (0 === s && o ? n.base = n.name = e.slice(1, l) : n.base = n.name = e.slice(s, l)) : (0 === s && o ? (n.name = e.slice(1, i),
                            n.base = e.slice(1, l)) : (n.name = e.slice(s, i),
                            n.base = e.slice(s, l)),
                            n.ext = e.slice(i, l)),
                            s > 0 ? n.dir = e.slice(0, s - 1) : o && (n.dir = "/"),
                            n
                        },
                        sep: "/",
                        delimiter: ":",
                        win32: null,
                        posix: null
                    };
                    n.posix = n,
                    e.exports = n
                }
            }
              , r = {};
            function n(e) {
                var a = r[e];
                if (void 0 !== a)
                    return a.exports;
                var o = r[e] = {
                    exports: {}
                }
                  , i = !0;
                try {
                    t[e](o, o.exports, n),
                    i = !1
                } finally {
                    i && delete r[e]
                }
                return o.exports
            }
            n.ab = "//";
            var a = n(114);
            e.exports = a
        }()
    },
    73289: function() {}
}]);
