<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 98.18 98.18">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        mask: url(#mask);
      }

      .cls-3 {
        fill: #bdcedb;
      }

      .cls-4 {
        fill: #f7f8fa;
      }

      .cls-5 {
        fill: #fff;
      }

      .cls-6 {
        fill: #ff391f;
        fill-rule: evenodd;
      }

      .cls-7 {
        opacity: .4;
      }

      .cls-8 {
        fill: url(#linear-gradient);
        fill-opacity: .3;
      }

      .cls-9 {
        fill: #6c92af;
      }
    </style>
    <mask id="mask" x="0" y="-5.24" width="98.18" height="108.66" maskUnits="userSpaceOnUse">
      <g id="mask0_1261_81902" data-name="mask0 1261 81902">
        <path class="cls-5" d="M98.18-5.24H0v108.66h98.18V-5.24Z"/>
      </g>
    </mask>
    <linearGradient id="linear-gradient" x1="49.09" y1="-5167.72" x2="49.09" y2="-5265.9" gradientTransform="translate(0 -5167.72) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#bdcedb"/>
      <stop offset="1" stop-color="#bdcedb" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <g>
        <g class="cls-2">
          <g class="cls-7">
            <path class="cls-8" d="M49.09,98.18c9.71,0,19.2-2.88,27.27-8.27,8.07-5.39,14.36-13.06,18.08-22.03,3.72-8.97,4.69-18.84,2.79-28.36-1.89-9.52-6.57-18.27-13.44-25.14-6.87-6.87-15.61-11.54-25.14-13.43C49.15-.95,39.27.02,30.3,3.74c-8.97,3.72-16.64,10.01-22.03,18.08C2.88,29.89,0,39.38,0,49.09c0,13.02,5.17,25.51,14.38,34.71,9.21,9.21,21.69,14.38,34.71,14.38Z"/>
          </g>
        </g>
        <path class="cls-6" d="M26.83,41.6c.66-.16,1.07-.83.91-1.49-.16-.66-.83-1.07-1.49-.91l-3.21.77c-.66.16-1.07.83-.91,1.49.16.66.83,1.07,1.49.91l3.21-.77ZM38.72,26.57c0,.68-.56,1.23-1.24,1.23-.68,0-1.23-.56-1.23-1.24v-3.3c0-.68.56-1.23,1.24-1.23.68,0,1.23.56,1.23,1.24v3.3ZM28.4,34.19c-.16.66-.82,1.08-1.48.92l-3.21-.75c-.66-.15-1.08-.82-.92-1.48.15-.66.82-1.08,1.48-.92l3.21.75c.66.15,1.08.82.92,1.48ZM32.42,28.14c.3.61.06,1.35-.55,1.66-.61.3-1.35.06-1.66-.55l-1.47-2.95c-.3-.61-.06-1.35.55-1.66.61-.3,1.35-.06,1.66.55l1.47,2.95Z"/>
      </g>
      <g>
        <g>
          <path class="cls-9" d="M67.72,65.81l-1.52-4.67c1.4-2.43,1.87-5.3,1.35-8.09-2.93-13.44-22.02-12.23-23.3,1.42-.63,6.64,5.07,12.75,11.75,12.61,1.37,0,2.71-.24,4.01-.71.25-.09.48-.12.69-.09l5.96.8c.68.11,1.29-.61,1.05-1.26h0Z"/>
          <path class="cls-3" d="M47.04,44.92c1.94-1.7,4.34-2.8,6.88-3.18-.41-5.83-5.85-10.78-11.7-10.64-8.98-.13-14.75,9.88-10.23,17.6l-1.52,4.66c-.23.65.37,1.38,1.06,1.26l5.95-.79c.21-.03.45,0,.69.09,1.34.49,2.72.72,4.13.71.15-3.71,1.87-7.26,4.74-9.72h0ZM35.26,43.73c.05-1.84,2.72-1.84,2.77,0-.05,1.83-2.72,1.83-2.77,0ZM42.19,45.12c-1.83-.05-1.82-2.73,0-2.78,1.83.05,1.84,2.73,0,2.78Z"/>
          <path class="cls-4" d="M62.85,53.05c-.19-.48-.61-.82-1.12-.9l-3.14-.45-1.39-2.87c-.32-.67-1.13-.95-1.8-.62-.27.13-.49.35-.62.62l-1.39,2.87-3.14.45c-.51.08-.93.42-1.12.9-.14.49,0,1.02.36,1.39l2.29,2.24-.54,3.14c-.1.5.11,1.02.54,1.3.41.3.95.33,1.39.09l2.82-1.48,2.82,1.48c.19.11.41.17.63.18.28,0,.55-.09.76-.27.43-.28.64-.8.54-1.3l-.54-3.14,2.29-2.24c.36-.37.5-.9.36-1.39Z"/>
        </g>
        <rect class="cls-1" x="30.41" y="30.41" width="37.35" height="37.35"/>
      </g>
    </g>
  </g>
</svg>