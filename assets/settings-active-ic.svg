<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: #a3b1cc;
      }

      .cls-3 {
        fill: #470ced;
      }

      .cls-4 {
        fill: #606f95;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <rect class="cls-1" width="28" height="28"/>
      <g>
        <path class="cls-3" d="M20.71,28H7.28c-3.55,0-6.43-2.88-6.43-6.43s2.64-6.27,6.14-6.43h.3s13.43,0,13.43,0c3.55,0,6.43,2.88,6.43,6.43s-2.64,6.27-6.14,6.43h-.3Z"/>
        <path class="cls-2" d="M7.28,23.91c1.29,0,2.33-1.04,2.33-2.33s-1.04-2.33-2.33-2.33-2.33,1.04-2.33,2.33,1.04,2.33,2.33,2.33Z"/>
        <path class="cls-2" d="M20.71,12.86H7.28c-3.55,0-6.43-2.89-6.43-6.43S3.49.17,6.98,0h.31s13.43,0,13.43,0c3.55,0,6.43,2.88,6.43,6.43s-2.64,6.27-6.14,6.43h-.3ZM7.04,2.39c-2.14.11-3.8,1.84-3.8,4.04s1.81,4.04,4.04,4.04h13.68c2.14-.1,3.8-1.84,3.8-4.04s-1.81-4.04-4.04-4.04H7.04Z"/>
        <path class="cls-4" d="M20.72,8.76c1.29,0,2.33-1.04,2.33-2.33s-1.04-2.33-2.33-2.33-2.33,1.04-2.33,2.33,1.04,2.33,2.33,2.33Z"/>
      </g>
    </g>
  </g>
</svg>